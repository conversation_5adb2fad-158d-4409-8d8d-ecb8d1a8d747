package com.rf.exchange.module.trade.api.dto;

import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import com.rf.exchange.module.trade.enums.TimeContractOrderStatusEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-07-13
 */
@Data
public class TimeContractRecordDTO {
    private Long id;
    private String tradeNo;
    private Long userId;
    private String username;
    private String code;
    private Integer duration;
    private BigDecimal amount;
    /**
     * 买入价格
     */
    private BigDecimal orderPrice;
    /**
     * 结算价格
     */
    private BigDecimal settlePrice;
    private Integer shortLong;
    /**
     * {@link MemberConfigProfitTypeEnum}
     */
    private Integer profitType;
    /**
     * 收益率
     */
    private BigDecimal profitRate;
    /**
     * 亏损率
     * 用于计算亏损金额
     */
    private BigDecimal loseProfitRate;
    /**
     * 交易状态
     * {@link TimeContractOrderStatusEnum}
     */
    private Integer tradeStatus;
    /**
     * 实际盈亏金额
     */
    private BigDecimal profitResult;
    private Long sendTime;
    private Long settleTime;
    private Long tenantId;
    private String failReason;
}
