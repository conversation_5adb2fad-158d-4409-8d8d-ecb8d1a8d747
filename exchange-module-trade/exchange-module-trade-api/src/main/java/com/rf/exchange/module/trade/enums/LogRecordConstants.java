package com.rf.exchange.module.trade.enums;

/**
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface LogRecordConstants {
    String TRADE_TIME_CONTRACT_TYPE = "TRADE 限时合约";
    String TRADE_TIME_CONTRACT_DELETE_SUB_TYPE = "删除限时合约记录";
    String TRADE_TIME_CONTRACT_DELETE_SUCCESS = "删除限时合约记录 用户ID:【{{#record.userId}}】单号:【{{#record.tradeNo}}】";
    String TRADE_TIME_CONTRACT_MANUAL_SUB_TYPE = "手动结算限时合约";
    String TRADE_TIME_CONTRACT_MANUAL_SUCCESS = "手动结算限时合约 用户ID:【{#record.userId}】单号:【{#record.tradeNo}】结果:【{{#result}}】";
    String TRADE_TIME_CONTRACT_SETTLE_SUB_TYPE = "手动派奖限时合约";
    String TRADE_TIME_CONTRACT_SETTLE_SUCCESS = "手动派奖限时合约 用户ID:【{#record.userId}】单号:【{#record.tradeNo}】结果:【{{#result}}】";
}
