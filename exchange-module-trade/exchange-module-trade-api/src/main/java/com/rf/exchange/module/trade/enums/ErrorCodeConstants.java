package com.rf.exchange.module.trade.enums;

import com.rf.exchange.framework.common.exception.ErrorCode;

/**
 * 交易模块的错误吗
 *
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    // ========== 限时合约交易记录 ==========
    ErrorCode TIME_CONTRACT_AMOUNT_LESS = new ErrorCode(1_011_000_000, "购买金额小于最小金额", "TIME_CONTRACT_AMOUNT_LESS");

    // ========== 限时合约交易记录 ==========
    ErrorCode TIME_CONTRACT_RECORD_NOT_EXISTS = new ErrorCode(1_011_000_000, "限时合约交易记录不存在", "TIME_CONTRACT_RECORD_NOT_EXISTS");

    // ========== 订单时长配置 ==========
    ErrorCode TRADE_DURATION_NOT_EXISTS = new ErrorCode(1_011_001_000, "订单时长配置不存在", "TRADE_DURATION_NOT_EXISTS");

    // ========== 订单倍数配置 ==========
    ErrorCode TRADE_MULTIPLE_NOT_EXISTS = new ErrorCode(1_011_002_000, "订单倍数配置不存在", "TRADE_MULTIPLE_NOT_EXISTS");

    // ========== 合约配置 ==========
    ErrorCode TRADE_CONTRACT_CONFIG_ERROR = new ErrorCode(1_011_003_000, "合约配置信息错误", "TRADE_CONTRACT_CONFIG_ERROR");

    // ========== 合约交易记录 ==========
    ErrorCode TRADE_CONTRACT_ORDER_RECORD_NOT_EXISTS = new ErrorCode(1_011_004_000, "合约委托记录不存在", "TRADE_CONTRACT_RECORD_NOT_EXISTS");
    ErrorCode TRADE_CONTRACT_ORDER_CANCEL_ALREADY = new ErrorCode(1_011_004_001, "合约委托已取消", "TRADE_CONTRACT_ORDER_CANCEL_ALREADY");
    ErrorCode TRADE_CONTRACT_CURRENCY_NOT_EXISTS = new ErrorCode(1_011_004_002, "委托订单汇率错误", "TRADE_CONTRACT_CURRENCY_NOT_EXISTS");
    ErrorCode TRADE_CONTRACT_ORDER_FINISHED_ALREADY = new ErrorCode(1_011_004_002, "合约委托已成交", "TRADE_CONTRACT_ORDER_FINISHED_ALREADY");
    ErrorCode TRADE_CONTRACT_POSITION_RECORD_NOT_EXISTS = new ErrorCode(1_011_005_001, "合约持仓记录不存在", "CONTRACT_POSITION_RECORD_NOT_EXISTS");
    ErrorCode TRADE_CONTRACT_POSITION_CLOSE_ALREADY = new ErrorCode(1_011_005_002, "合约已经平仓", "TRADE_CONTRACT_POSITION_CLOSE_ALREADY");
}