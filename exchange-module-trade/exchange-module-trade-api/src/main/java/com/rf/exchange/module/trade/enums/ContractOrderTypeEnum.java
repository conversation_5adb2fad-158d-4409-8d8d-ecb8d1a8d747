package com.rf.exchange.module.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合约订单的委托类型 枚举
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Getter
@AllArgsConstructor
public enum ContractOrderTypeEnum {
    OPEN_ORDER(0, "开仓委托"),
    STOP_ORDER(1, "平仓委托"),
    STOP_PROFIT_ORDER(2, "止盈委托"),
    STOP_LOSS_ORDER(3, "止损委托"),
    LIQUIDATE_ORDER(4, "强平委托"),
    ;
    private final Integer type;
    private final String name;
}
