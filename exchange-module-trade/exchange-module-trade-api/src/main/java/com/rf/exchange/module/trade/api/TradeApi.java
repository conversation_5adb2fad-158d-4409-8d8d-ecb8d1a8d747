package com.rf.exchange.module.trade.api;

import com.rf.exchange.module.trade.api.dto.TimeContractRecordDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 交易模块 API
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
public interface TradeApi {

    /**
     * 获取 等待结束 的 限时合约订单
     *
     * @return 订单列表
     */
    List<TimeContractRecordDTO> getWaitingTimeContractRecordList();

    /**
     * 计算实际赢亏
     *
     * @param recordDto        限时合约的记录信息
     * @param currentPrice     当前交易对的价格
     * @param configProfitType 配置的盈亏类型
     * @return 计算之后的限时合约记录信息
     */
    TimeContractRecordDTO calculateTimeContractRecordResult(TimeContractRecordDTO recordDto, BigDecimal currentPrice, int configProfitType);

    /**
     * 更新订单的状态
     *
     * @param recordDto 限时合约记录信息
     */
    void updateTimeContractRecordStatus(TimeContractRecordDTO recordDto);

    /**
     * 清除redis中的记录的订单号信息
     */
    void removeRedisNotWaitOrderNos(Set<String> orderSet);
}
