<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange-module-trade</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>exchange-module-trade-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-member-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-common</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-i18n</artifactId>
        </dependency>

    </dependencies>
</project>