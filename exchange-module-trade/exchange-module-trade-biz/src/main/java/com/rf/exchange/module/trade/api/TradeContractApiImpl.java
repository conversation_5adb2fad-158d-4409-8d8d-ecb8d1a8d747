package com.rf.exchange.module.trade.api;

import com.rf.exchange.module.trade.service.contract.ContractPositionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-08-30
 */
@Service
public class TradeContractApiImpl implements TradeContractApi{

    @Resource
    private ContractPositionService positionRecordService;

    @Override
    public BigDecimal getContractPositionTotalMargin(Long userId) {
        return positionRecordService.getContractPositionTotalMargin(userId);
    }
}
