package com.rf.exchange.module.trade.service.contract;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractPositionPageReqVO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractPositionRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.*;
import com.rf.exchange.module.trade.controller.app.tradetotal.vo.AppContractSummaryRespVO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractPositionDO;
import com.rf.exchange.module.trade.enums.ContractOrderTypeEnum;
import com.rf.exchange.module.trade.enums.ContractPositionCloseTypeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 合约持仓记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ContractPositionService {

    AppContractSummaryRespVO appGetContractSummary(LoginUser loginUser);

    /**
     * APP 获取合约持仓记录分页
     *
     * @param reqVO     请求信息
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     * @return 合约持仓分页数据
     */
    PageResult<AppContractPositionRespVO> appGetContractPositionPage(AppContractPositionPageReqVO reqVO, LoginUser loginUser, Long tenantId);

    /**
     * APP 止盈止损平仓
     *
     * @param reqVO     请求信息
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     * @return 成功失败
     */
    Boolean appCloseContractPositionWithCondition(AppContractCloseConditionReqVO reqVO, LoginUser loginUser, Long tenantId);

    void internalCreateStopConditionOrder(ContractOrderTypeEnum orderTypeEnum, BigDecimal orderPrice, ContractPositionDO position, long tenantId);

    /**
     * APP 平仓
     *
     * @param reqVO     平仓信息
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     * @return 是否成功
     */
    Boolean appCloseContractPosition(AppContractCloseReqVO reqVO, LoginUser loginUser, Long tenantId);

    /**
     * APP 批量平仓
     *
     * @param reqVO     请求参数
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     * @return 是否成功
     */
    Boolean appBatchCloseContractPosition(AppContractBatchCloseReqVO reqVO, LoginUser loginUser, Long tenantId);

    /**
     * 通过委托订单创建合约持仓记录
     *
     * @param contractOrder 委托订单
     * @param openPrice     开仓价格
     * @param tradePair     交易对
     * @param tenantId      租户id
     * @return 持仓记录
     */
    ContractPositionDO createContractPositionByOrder(ContractOrderDO contractOrder, BigDecimal openPrice, TradePairRespDTO tradePair, Long tenantId);

    /**
     * 通过委托订单平仓合约持仓记录
     *
     * @param contractOrder 合约委托订单
     * @param price         平仓价格
     * @param closeTypeEnum 平仓类型
     */
    void autoStopContractPositionByOrder(ContractOrderDO contractOrder, BigDecimal price, ContractPositionCloseTypeEnum closeTypeEnum);

    /**
     * 强平用户所有的持仓仓位
     *
     * @param triggerPosition    触发强平所有订单的持仓
     * @param otherTradePriceMap 实时价格
     */
    void forceLiquidateAllContractPosition(ContractPositionDO triggerPosition, Map<String, BigDecimal> otherTradePriceMap);

    /**
     * 计算仓位的盈亏
     *
     * @param volume        持仓
     * @param openPrice     开仓价格
     * @param realTimePrice 单前价格
     * @param exchangeRate  汇率
     * @param leverage      杠杆倍数
     * @param shortLong     多空
     * @return 盈亏金额
     */
    BigDecimal calculatePositionProfitLoss(BigDecimal volume, BigDecimal openPrice, BigDecimal realTimePrice, BigDecimal exchangeRate, int leverage, int shortLong);

    /**
     * 获得合约持仓记录
     *
     * @param id 编号
     * @return 合约持仓记录
     */
    ContractPositionDO getContractPosition(Long id);

    ContractPositionDO getContractPositionByNo(String recordNo);

    /**
     * 获得合约持仓记录分页
     *
     * @param pageReqVO 分页查询
     * @return 合约持仓记录分页
     */
    PageResult<ContractPositionRespVO> getContractPositionPage(ContractPositionPageReqVO pageReqVO);

    /**
     * 获取用户持仓的总保证金
     *
     * @param userId 用户id
     * @return 用户的合约保证金总额
     */
    BigDecimal getContractPositionTotalMargin(Long userId);

    /**
     * 获取指定的持仓记录编号
     *
     * @param recordNos 持仓记录编号集合
     * @return 持仓记录列表
     */
    List<ContractPositionDO> getUnStoppedPositionsByRecordNos(Set<String> recordNos);
}