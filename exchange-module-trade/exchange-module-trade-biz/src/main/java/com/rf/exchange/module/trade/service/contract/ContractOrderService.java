package com.rf.exchange.module.trade.service.contract;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractOrderPageReqVO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractOrderSaveReqVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppContractOrderCancelReaVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppContractOrderCreateReqVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppContractOrderHistoryPageReqVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppContractOrderRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppContractSimpleInfoRespVO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.enums.ContractPositionCloseTypeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 合约委托记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ContractOrderService {

    /**
     * 获取合约的简要详情接口
     *
     * @param code     系统交易对代码
     * @param tenantId 租户id
     * @return APP合约委托设置
     */
    AppContractSimpleInfoRespVO appGetContractSimpleInfo(String code, Long tenantId);

    /**
     * 创建委托
     *
     * @param saveReqVO 委托信息
     * @param tenantId  租户id
     * @return 委托订单
     */
    ContractOrderDO createContractOrder(ContractOrderSaveReqVO saveReqVO, Long tenantId);

    /**
     * APP 获取合约委托记录
     *
     * @param reqVO     查询信息
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     */
    PageResult<AppContractOrderRespVO> appGetContractOrderPage(PageParam reqVO, LoginUser loginUser, Long tenantId);

    /**
     * APP 获取合约订单历史记录
     *
     * @param reqVO     查询信息
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     * @return 合约订单历史分页数据
     */
    PageResult<AppContractOrderRespVO> appGetContractOrderHistoryPage(AppContractOrderHistoryPageReqVO reqVO, LoginUser loginUser, Long tenantId);

    /**
     * APP 创建开仓/平仓委托单
     *
     * @param reqVO     开/平仓信息
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     * @return 委托订单id
     */
    AppContractOrderRespVO appCreateContractOrder(AppContractOrderCreateReqVO reqVO, LoginUser loginUser, Long tenantId);

    /**
     * APP 取消开平仓委托订单
     *
     * @param reqVO     请求信息
     * @param loginUser 当前登录用户
     * @param tenantId  租户id
     * @return 取消委托订单编号
     */
    Long appCancelContractOrder(AppContractOrderCancelReaVO reqVO, LoginUser loginUser, Long tenantId);

    /**
     * 完成委托中状态的委托订单成交
     *
     * @param order            委托中订单信息
     * @param transactionPrice 成交价格
     * @return 处理成功的order id集合
     */
    Set<Long> autoCompleteOrderingOrderTransaction(ContractOrderDO order, BigDecimal transactionPrice);

    /**
     * 自动完成止盈委托订单的成交
     *
     * @param order 止盈委托的订单信息
     * @param price 成交价格
     * @return 处理成功的order id集合
     */
    Set<Long> autoCompleteStopProfitOrderTransaction(ContractOrderDO order, BigDecimal price);

    /**
     * 自动完成止损委托订单的成交
     *
     * @param order 止损委托的订单信息
     * @param price 成交价格
     * @return 处理成功的order id集合
     */
    Set<Long> autoCompleteStopLossOrderTransaction(ContractOrderDO order, BigDecimal price);

    /**
     * 自动完成强平委托订单的成交
     *
     * @param order 强平委托订单
     * @param price 成交价格
     * @return 处理成功的order id集合
     */
    Set<Long> autoCompleteLiquidateOrderTransaction(ContractOrderDO order, BigDecimal price);

    Set<Long> internalAutoStopPositionOrder(ContractOrderDO order, BigDecimal price, ContractPositionCloseTypeEnum closeTypeEnum);

    /**
     * 通过id获取委托订单信息
     *
     * @param id 订单id
     * @return 委托订单信息
     */
    ContractOrderDO getContractOrderRecord(Long id);

    /**
     * 通过委托订单号获取订单信息
     *
     * @param orderNo 订单号
     * @return 委托订单信息
     */
    ContractOrderDO getContractOrderByOrderNo(String orderNo);

    /**
     * 获得合约委托记录分页
     *
     * @param pageReqVO 分页查询
     * @return 合约委托记录分页
     */
    PageResult<ContractOrderDO> getContractOrderRecordPage(ContractOrderPageReqVO pageReqVO);

    /**
     * 获取所有未完成的合约委托订单
     *
     * @return 合约委托订单列表
     */
    List<ContractOrderDO> getAllUnfinishedContractOrders();

    /**
     * 获取指定仓位订单关联的委托订单
     *
     * @param positionOrderNo 仓位订单编号
     * @return 合约委托订单列表
     */
    List<ContractOrderDO> getRelatedOrdersByPositionOrderNo(String positionOrderNo);

    /**
     * 获取指定仓位记录的关联的委托订单
     *
     * @param positionOrderNOs 仓位订单编号集合
     * @return 合约委托订单列表
     */
    List<ContractOrderDO> getAllOrderByPositionOrderNos(Set<String> positionOrderNOs);

    /**
     * 更新order信息
     *
     * @param order         信息
     * @param sendMQMessage 是否发送订单更新的MQ消息
     */
    void updateOrder(ContractOrderDO order, boolean sendMQMessage);

    /**
     * 取消委托订单
     * @param order 委托订单
     */
    void cancelOrder(ContractOrderDO order);
}