package com.rf.exchange.module.trade.controller.app.trade.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.module.trade.enums.TimeContractOrderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-13
 */
@Schema(description = "限时合约订单详情接口 Response VO")
@Data
public class AppTradeTimeContractPageReqVO extends PageParam {

    /**
     * {@link TimeContractOrderTypeEnum}
     */
    @Schema(description = "分类 0:持仓 1:历史", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    @NotNull(message = "{TRADE_PAGE_TYPE_ERROR}")
    private Integer type;

}
