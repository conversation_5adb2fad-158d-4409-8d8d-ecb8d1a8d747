package com.rf.exchange.module.trade.dal.redis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.rf.exchange.module.trade.dal.redis.RedisKeyConstants.*;

/**
 * <AUTHOR>
 * @since 2024-09-06
 */
@Repository
public class ContractLiquidateEngineRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public void updateLiquidatePriceMap(Map<String, BigDecimal> priceMap) {
        updateMapByKey(CONTRACT_LIQUIDATE_PRICE, priceMap);
    }

    public Map<String, BigDecimal> getLiquidatePriceMap() {
        return getMapByKey(CONTRACT_LIQUIDATE_PRICE);
    }

    public BigDecimal getLiquidatePriceByPositionNo(String positionNo) {
        return getValueByKeyAndPositionNo(CONTRACT_LIQUIDATE_PRICE, positionNo);
    }

    public void updateMarginRateMap(Map<String, BigDecimal> marginMap) {
        updateMapByKey(CONTRACT_POSITION_MARGIN_RATE, marginMap);
    }

    public Map<String, BigDecimal> getMarginRateMap() {
        return getMapByKey(CONTRACT_POSITION_MARGIN_RATE);
    }

    public BigDecimal getMarginRateByPositionNo(String positionNo) {
        return getValueByKeyAndPositionNo(CONTRACT_POSITION_MARGIN_RATE, positionNo);
    }

    public void updateProfitLossMap(Map<String, BigDecimal> profitLossMap) {
        updateMapByKey(CONTRACT_POSITION_PROFIT_LOSS, profitLossMap);
    }

    public Map<String, BigDecimal> getProfitLossMap() {
        return getMapByKey(CONTRACT_POSITION_PROFIT_LOSS);
    }

    public BigDecimal getProfitLossByPositionNo(String positionNo) {
        return getValueByKeyAndPositionNo(CONTRACT_POSITION_PROFIT_LOSS, positionNo);
    }

    public void removeAll() {
        stringRedisTemplate.delete(CONTRACT_LIQUIDATE_PRICE);
        stringRedisTemplate.delete(CONTRACT_POSITION_PROFIT_LOSS);
        stringRedisTemplate.delete(CONTRACT_POSITION_MARGIN_RATE);
    }

    public void removeUnusefulKeyValue(String positionNo) {
        if (StrUtil.isEmpty(positionNo)) {
            return;
        }
        removeValueByKey(CONTRACT_LIQUIDATE_PRICE, positionNo);
        removeValueByKey(CONTRACT_POSITION_MARGIN_RATE, positionNo);
        removeValueByKey(CONTRACT_POSITION_PROFIT_LOSS, positionNo);
    }

    private BigDecimal getValueByKeyAndPositionNo(String key, String positionNo) {
        final Object obj = stringRedisTemplate.opsForHash().get(key, positionNo);
        if (null == obj) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(obj.toString());
    }

    private Map<String, BigDecimal> getMapByKey(String key) {
        final Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
        if (CollUtil.isEmpty(entries)) {
            return Collections.emptyMap();
        }
        Map<String, BigDecimal> map = new HashMap<>(entries.size());
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            map.put(entry.getKey().toString(), new BigDecimal(entry.getValue().toString()));
        }
        return map;
    }

    private void updateMapByKey(String key, Map<String, BigDecimal> map) {
        if (CollUtil.isEmpty(map)) {
            return;
        }
        Map<String, String> dataMap = new HashMap<>(map.size());
        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
            dataMap.put(entry.getKey(), entry.getValue().toString());
        }
        stringRedisTemplate.opsForHash().putAll(key, dataMap);
    }

    private void removeValueByKey(String key, String hashKey) {
        stringRedisTemplate.opsForHash().delete(key, hashKey);
    }
}
