package com.rf.exchange.module.trade.service.timecontract;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.trade.controller.admin.timecontract.vo.*;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTimeContractConfigRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractBuyReqVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractOrderDetailRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractPageReqVO;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 限时合约交易记录 Service 接口
 *
 * <AUTHOR>
 */
public interface TimeContractRecordService {

    /**
     * APP 获取限时合约的配置信息
     *
     * @param tradePairCode 请求参数
     * @param tenantId      租户编号
     * @return 配置列表
     */
    List<AppTimeContractConfigRespVO> getTimeContractConfig(String tradePairCode, Long tenantId);

    /**
     * 创建限时合约交易记录
     *
     * @param reqVO    创建信息
     * @param userId   用户id
     * @param tenantId 租户id
     */
    Long createTimeContractRecord(@Valid AppTradeTimeContractBuyReqVO reqVO, Long userId, Long tenantId);

    /**
     * 获取订单详情
     *
     * @param id       订单号
     * @param userId   用户id
     * @param tenantId 租户id
     * @return 订单详情
     */
    AppTradeTimeContractOrderDetailRespVO getTimeContractOrderDetail(Long id, Long userId, Long tenantId);

    /**
     * 获取订单分页
     *
     * @param reqVO    请求信息
     * @param userId   登录用户
     * @param tenantId 租户id
     * @return 订单分页列表
     */
    PageResult<AppTradeTimeContractOrderDetailRespVO> appGetTimeContractOrderPage(AppTradeTimeContractPageReqVO reqVO, Long userId, Long tenantId);

    /**
     * 更新限时合约交易记录
     *
     * @param updateReqVO 更新信息
     */
    void updateTimeContractRecord(@Valid TimeContractRecordSaveReqVO updateReqVO);

    /**
     * 删除限时合约交易记录
     *
     * @param id 编号
     */
    void deleteTimeContractRecord(Long id);

    /**
     * 获得限时合约交易记录
     *
     * @param id 编号
     * @return 限时合约交易记录
     */
    TimeContractRecordDO getTimeContractRecord(Long id);

    /**
     * 获取指定用户的限时合约交易记录 所有
     *
     * @param userId 用户id
     * @return 限时交易记录列表
     */
    List<TimeContractRecordDO> getTimeContractRecords(Long userId);

    /**
     * 获取指定用户指定状态的限时合约记录
     *
     * @param userId 用户id
     * @param status 交易状态
     * @return 限时交易记录列表
     */
    List<TimeContractRecordDO> getTimeContractRecordsByStatus(Long userId, Integer status);

    /**
     * 获取指定交易
     *
     * @param orderNoSet 订单号集合
     * @return 限时交易记录列表
     */
    List<TimeContractRecordDO> getTimeContractRecordsByOrderNo(Set<String> orderNoSet);

    /**
     * 计算限时合约记录的盈亏
     *
     * @param recordDO     限时合约记录
     * @param currentPrice 当前价格
     * @param profitType   盈亏配置类型
     * @return 新的合约记录信息
     */
    TimeContractRecordDO calculateTimeRecordResult(TimeContractRecordDO recordDO, BigDecimal currentPrice, int profitType);

    /**
     * 获得限时合约交易记录分页
     *
     * @param pageReqVO 分页查询
     * @param tenantId  租户id
     * @return 限时合约交易记录分页
     */
    PageResult<TimeContractRecordDO> getTimeContractRecordPage(TimeContractRecordPageReqVO pageReqVO, Long tenantId);


    /**
     * 更新限时合约记录信息
     *
     * @param recordDO 限时合约信息
     */
    void updateTimeContractRecordResult(TimeContractRecordDO recordDO);

    /**
     * 更新限时合约订单的状态
     *
     * @param recordDO 限时合约信息
     */
    void updateTimeContractRecordStatus(TimeContractRecordDO recordDO);

    /**
     * 手动结算限时合约订单
     *
     * @param reqVO 结算信息
     */
    TimeContractRecordRespVO manualDrawTimeContract(TimeContractRecordDrawReqVO reqVO);

    /**
     * 手动结算订单
     *
     * @param reqVO 结算信息
     */
    void settleOrderAgain(TimeContractRecordSettleReqVO reqVO);

    /**
     * 获取订单列表通过时间范围
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<TimeContractRecordDO> getListByRange(Long startTime, Long endTime);

    /**
     * 获取订单列表通过时间范围
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<TimeContractRecordDO> getListByRange(Long userId, Long startTime, Long endTime);
}