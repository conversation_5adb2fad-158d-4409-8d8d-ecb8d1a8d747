package com.rf.exchange.module.trade.dal.mysql.contract;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractPositionPageReqVO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractPositionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 合约持仓记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContractPositionMapper extends BaseMapperX<ContractPositionDO> {

    default ContractPositionDO selectPositionByOrderNo(String orderNo) {
        LambdaQueryWrapperX<ContractPositionDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(ContractPositionDO::getRecordNo, orderNo);
        wrapper.last("limit 1");
        return selectOne(wrapper);
    }

    default PageResult<ContractPositionDO> selectPage(ContractPositionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractPositionDO>()
                .eqIfPresent(ContractPositionDO::getRecordNo, reqVO.getRecordNo())
                .eqIfPresent(ContractPositionDO::getUserId, reqVO.getUserId())
                .likeIfPresent(ContractPositionDO::getUsername, reqVO.getUsername())
                .eqIfPresent(ContractPositionDO::getCode, reqVO.getCode())
                .eqIfPresent(ContractPositionDO::getShortLong, reqVO.getShortLong())
                .eqIfPresent(ContractPositionDO::getCloseType, reqVO.getCloseType())
                .eqIfPresent(ContractPositionDO::getPositionStatus, reqVO.getPositionStatus())
                .eqIfPresent(ContractPositionDO::getOpenPrice, reqVO.getOpenPrice())
                .eqIfPresent(ContractPositionDO::getClosePrice, reqVO.getClosePrice())
                .eqIfPresent(ContractPositionDO::getLeverage, reqVO.getLeverage())
                .betweenIfPresent(ContractPositionDO::getOpenTime, reqVO.getOpenTime())
                .betweenIfPresent(ContractPositionDO::getCloseTime, reqVO.getCloseTime())
                .betweenIfPresent(ContractPositionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ContractPositionDO::getId));
    }

}