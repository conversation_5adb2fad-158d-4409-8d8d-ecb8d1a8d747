package com.rf.exchange.module.trade.controller.app.trade.vo;

import com.rf.exchange.framework.common.validation.InList;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-08-29
 */
@Schema(description = "合约批量平仓接口参数 Request VO")
@Data
public class AppContractBatchCloseReqVO {

    @Schema(description = "仓位类型 0:空单 1:多单 2:全部", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{ARG_ORDER_TYPE_IS_EMPTY}")
    @InList(values = {0, 1, 2}, message = "{ARG_VALUE_ERROR}")
    private Integer type;
}
