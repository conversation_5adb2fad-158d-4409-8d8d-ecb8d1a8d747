package com.rf.exchange.module.trade.dal.mysql.timecontract;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.trade.controller.admin.timecontract.vo.*;

import java.util.List;
import java.util.Set;

/**
 * 限时合约交易记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TimeContractRecordMapper extends BaseMapperX<TimeContractRecordDO> {

    default PageResult<TimeContractRecordDO> selectPage(TimeContractRecordPageReqVO reqVO) {
        LambdaQueryWrapperX<TimeContractRecordDO> wrapper = new LambdaQueryWrapperX<TimeContractRecordDO>()
                .eqIfPresent(TimeContractRecordDO::getTradeNo, reqVO.getTradeNo())
                .eqIfPresent(TimeContractRecordDO::getUserId, reqVO.getUserId())
                .likeIfPresent(TimeContractRecordDO::getUsername, reqVO.getUsername())
                .eqIfPresent(TimeContractRecordDO::getCode, reqVO.getCode())
                .eqIfPresent(TimeContractRecordDO::getDuration, reqVO.getDuration())
                .eqIfPresent(TimeContractRecordDO::getAmount, reqVO.getAmount())
                .eqIfPresent(TimeContractRecordDO::getShortLong, reqVO.getShortLong())
                .eqIfPresent(TimeContractRecordDO::getProfitType, reqVO.getProfitType())
                .eqIfPresent(TimeContractRecordDO::getProfitRate, reqVO.getProfitRate())
                .eqIfPresent(TimeContractRecordDO::getTradeStatus, reqVO.getTradeStatus())
                .betweenIfPresent(TimeContractRecordDO::getSendTime, reqVO.getSendTime())
                .betweenIfPresent(TimeContractRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TimeContractRecordDO::getId);
        if (reqVO.getWinOrLose() != null) {
            wrapper.ge(reqVO.getWinOrLose(), TimeContractRecordDO::getProfitResult, 0);
            wrapper.le(!reqVO.getWinOrLose(), TimeContractRecordDO::getProfitResult, 0);
        }
        return selectPage(reqVO, wrapper);
    }

    default List<TimeContractRecordDO> selectListByOrderNos(Set<String> orderNo) {
        return selectList(new LambdaQueryWrapperX<TimeContractRecordDO>()
                .inIfPresent(TimeContractRecordDO::getTradeNo, orderNo)
                .orderByAsc(TimeContractRecordDO::getSendTime)); // 按照发送时间进行升序排列保证最先创建的订单最先被处理
    }

    default List<TimeContractRecordDO> selectList(Long userId, Integer status) {
        return selectList(new LambdaQueryWrapperX<TimeContractRecordDO>()
                .eqIfPresent(TimeContractRecordDO::getUserId, userId)
                .eqIfPresent(TimeContractRecordDO::getTradeStatus, status)
                .orderByDesc(TimeContractRecordDO::getSendTime));
    }

}