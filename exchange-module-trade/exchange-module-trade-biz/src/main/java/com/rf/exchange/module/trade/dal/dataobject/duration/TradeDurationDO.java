package com.rf.exchange.module.trade.dal.dataobject.duration;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 订单时长配置 DO
 *
 * <AUTHOR>
 */
@TableName("trade_duration")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeDurationDO extends TenantBaseDO {
    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 时长
     */
    private Integer duration;
    /**
     * 单位时间
     */
    private Integer timeUnit;
    /**
     * 最小购买金额
     */
    private Integer minAmount;
    /**
     * 收益率
     */
    private BigDecimal profitRate;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 开启状态（0正常 1停用）
     * {@link CommonStatusEnum}
     */
    private Integer status;
}