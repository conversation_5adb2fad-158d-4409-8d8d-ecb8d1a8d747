package com.rf.exchange.module.trade.dal.redis;

/**
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface RedisKeyConstants {

    String PRIVATE_KEY_PREFIX = "exch:trade:";

    /**
     * 限时合约交易的时间配置
     */
    String TRADE_DURATION = PRIVATE_KEY_PREFIX + "duration#1m";

    /**
     * 限时合约等待结束的订单号缓存
     */
    String TIME_CONTRACT_WAIT = PRIVATE_KEY_PREFIX + "time_contract_wait";
    /**
     * 限时合约开奖中的订单号
     */
    String TIME_CONTRACT_DRAWING = PRIVATE_KEY_PREFIX + "time_contract_drawing";

    /**
     * 合约委托未完成的订单号
     */
    String CONTRACT_UNFINISHED_ORDER_NO = PRIVATE_KEY_PREFIX + "contract:unfinished_order_no";

    /**
     * 合约持仓的强平价格
     */
    String CONTRACT_LIQUIDATE_PRICE = PRIVATE_KEY_PREFIX + "contract:position_liquidate_price";

    /**
     * 合约持仓的盈亏收益
     */
    String CONTRACT_POSITION_PROFIT_LOSS = PRIVATE_KEY_PREFIX + "contract:position_profit_loss";

    /**
     * 合约持仓的保证金率
     */
    String CONTRACT_POSITION_MARGIN_RATE = PRIVATE_KEY_PREFIX + "contract:position_margin_rate";
}
