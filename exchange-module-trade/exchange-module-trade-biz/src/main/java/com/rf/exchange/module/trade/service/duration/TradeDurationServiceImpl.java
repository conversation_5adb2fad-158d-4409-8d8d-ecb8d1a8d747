package com.rf.exchange.module.trade.service.duration;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.trade.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rf.exchange.module.trade.controller.admin.duration.vo.*;
import com.rf.exchange.module.trade.dal.dataobject.duration.TradeDurationDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;

import com.rf.exchange.module.trade.dal.mysql.duration.TradeDurationMapper;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.trade.enums.ErrorCodeConstants.*;

/**
 * 订单时长配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TradeDurationServiceImpl implements TradeDurationService {

    @Resource
    private TradeDurationMapper durationMapper;

    @Override
    @Master
    @DSTransactional
    public void copyDataFromTenant(Long sourceTenantId, Long targetTenantId) {
        if (sourceTenantId == null || targetTenantId == null) {
            throw exception(GlobalErrorCodeConstants.VALUE_ERROR);
        }
        TenantContextHolder.setIgnore(true);
        LambdaQueryWrapperX<TradeDurationDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(TradeDurationDO::getTenantId, sourceTenantId);
        wrapper.eq(TradeDurationDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        final List<TradeDurationDO> tradeDurationDOList = durationMapper.selectList(wrapper);
        for (TradeDurationDO tradeDurationDO : tradeDurationDOList) {
            tradeDurationDO.setId(null);
            tradeDurationDO.setTenantId(targetTenantId);
        }
        TenantContextHolder.clearIgnore();
        durationMapper.insertBatch(tradeDurationDOList);
    }

    @Override
    @Slave
    @Cacheable(cacheNames = RedisKeyConstants.TRADE_DURATION, key = "#tradePairCode + #tenantId", unless = "#result == null or #result.isEmpty()")
    public List<TradeDurationDO> getDurations(String tradePairCode, Long tenantId) {
        return durationMapper.selectList(tenantId);
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.TRADE_DURATION, allEntries = true)
    public Long createDuration(TradeDurationSaveReqVO createReqVO) {
        // 插入
        TradeDurationDO duration = BeanUtils.toBean(createReqVO, TradeDurationDO.class);
        durationMapper.insert(duration);
        // 返回
        return duration.getId();
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.TRADE_DURATION, allEntries = true)
    public void updateDuration(TradeDurationSaveReqVO updateReqVO) {
        // 校验存在
        validateDurationExists(updateReqVO.getId());
        // 更新
        TradeDurationDO updateObj = BeanUtils.toBean(updateReqVO, TradeDurationDO.class);
        durationMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.TRADE_DURATION, allEntries = true)
    public void deleteDuration(Long id) {
        // 校验存在
        validateDurationExists(id);
        // 删除
        durationMapper.deleteById(id);
    }

    private void validateDurationExists(Long id) {
        if (durationMapper.selectById(id) == null) {
            throw exception(TRADE_DURATION_NOT_EXISTS);
        }
    }

    @Override
    public TradeDurationDO getDuration(Long id) {
        return durationMapper.selectById(id);
    }

    @Override
    public PageResult<TradeDurationDO> getDurationPage(TradeDurationPageReqVO pageReqVO) {
        return durationMapper.selectPage(pageReqVO);
    }

}