package com.rf.exchange.module.trade.api;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.module.trade.api.dto.TimeContractRecordDTO;
import com.rf.exchange.module.trade.convert.TradeConvert;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;
import com.rf.exchange.module.trade.dal.redis.TimeContractOrderRedisDAO;
import com.rf.exchange.module.trade.service.timecontract.TimeContractRecordService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-07-13
 */
@Service
public class TradeApiImpl implements TradeApi {

    @Resource
    private TimeContractRecordService timeContractRecordService;
    @Resource
    private TimeContractOrderRedisDAO timeContractOrderRedisDAO;

    @Override
    public List<TimeContractRecordDTO> getWaitingTimeContractRecordList() {
        // 获取redis中处于等待状态的所有订单号
        List<Object> allWaitOrderNoList = timeContractOrderRedisDAO.getAll();
        if (CollectionUtil.isEmpty(allWaitOrderNoList)) {
            return Collections.emptyList();
        }
        Set<String> orderNoSet = allWaitOrderNoList.stream().map(Object::toString).collect(Collectors.toSet());
        List<TimeContractRecordDO> orderList = timeContractRecordService.getTimeContractRecordsByOrderNo(orderNoSet);
        return TradeConvert.INSTANCE.convertList(orderList);
    }

    @Override
    public TimeContractRecordDTO calculateTimeContractRecordResult(TimeContractRecordDTO recordDto, BigDecimal currentPrice, int configProfitType) {
        TimeContractRecordDO record = TradeConvert.INSTANCE.convert2(recordDto);
        TimeContractRecordDO calculatedRecord = timeContractRecordService.calculateTimeRecordResult(record, currentPrice, configProfitType);
        return TradeConvert.INSTANCE.convert(calculatedRecord);
    }

    @Override
    public void updateTimeContractRecordStatus(TimeContractRecordDTO recordDto) {
        TimeContractRecordDO record = TradeConvert.INSTANCE.convert2(recordDto);
        timeContractRecordService.updateTimeContractRecordStatus(record);
    }

    @Override
    public void removeRedisNotWaitOrderNos(Set<String> orderSet) {
        timeContractOrderRedisDAO.removeAll(orderSet);
    }
}
