package com.rf.exchange.module.trade.dal.dataobject.contract;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.trade.enums.ContractMarginTypeEnum;
import com.rf.exchange.module.trade.enums.ContractPositionCloseTypeEnum;
import com.rf.exchange.module.trade.enums.PositionStatusEnum;
import lombok.*;

import java.math.BigDecimal;

/**
 * 合约持仓记录 DO
 *
 * <AUTHOR>
 */
@TableName("trade_contract_position")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractPositionDO extends TenantBaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 记录编号
     */
    private String recordNo;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 持仓资产类型
     * {@link TradeAssetTypeEnum}
     */
    private Integer assetType;
    /**
     * 多空类型 0:做空 1:做多
     */
    private Integer shortLong;
    /**
     * 平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平
     * {@link ContractPositionCloseTypeEnum}
     */
    private Integer closeType;
    /**
     * 仓位状态 0:持仓中 1:已平仓
     * {@link PositionStatusEnum}
     */
    private Integer positionStatus;
    /**
     * 开仓价格
     */
    private BigDecimal openPrice;
    /**
     * 平仓价格
     */
    private BigDecimal closePrice;
    /**
     * 初始保证金
     */
    private BigDecimal margin;
    /**
     * 保证金类型
     * {@link ContractMarginTypeEnum}
     */
    private Integer marginType;
    /**
     * 初始保证金法币币种
     */
    private String marginCurrency;
    /**
     * 持仓量
     */
    private BigDecimal volume;
    /**
     * 持仓资产
     */
    private String volumeAsset;
    /**
     * 持仓价值
     */
    private BigDecimal volumeValue;
    /**
     * 持仓价值法币币种
     */
    private String volumeValueCurrency;
    /**
     * 持仓手续费
     */
    private BigDecimal positionFee;
    /**
     * 止盈价
     */
    private BigDecimal stopProfitPrice;
    /**
     * 止盈委托订单号
     */
    private String stopProfitOrderNo;
    /**
     * 止损价
     */
    private BigDecimal stopLossPrice;
    /**
     * 止损委托订单号
     */
    private String stopLossOrderNo;
    /**
     * 收益亏损
     */
    private BigDecimal profitLoss;
    /**
     * 收益亏损率
     */
    private BigDecimal profitLossRate;
    /**
     * 杠杆倍数
     */
    private Integer leverage;
    /**
     * 开仓时间
     */
    private Long openTime;
    /**
     * 平仓时间
     */
    private Long closeTime;
    /**
     * 备注
     */
    private String remark;
}