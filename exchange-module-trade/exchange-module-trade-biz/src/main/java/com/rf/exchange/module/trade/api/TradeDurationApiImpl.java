package com.rf.exchange.module.trade.api;

import com.rf.exchange.module.trade.service.duration.TradeDurationService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-08-04
 */
@Service
public class TradeDurationApiImpl implements TradeDurationApi {

    @Resource
    private TradeDurationService tradeDurationService;

    @Override
    public void copyDataFromTenant(Long sourceTenantId, Long targetTenantId) {
        tradeDurationService.copyDataFromTenant(sourceTenantId, targetTenantId);
    }
}
