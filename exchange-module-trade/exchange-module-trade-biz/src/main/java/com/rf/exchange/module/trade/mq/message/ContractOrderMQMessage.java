package com.rf.exchange.module.trade.mq.message;

import com.rf.exchange.module.trade.enums.ContractOrderMQMessageType;
import com.rf.exchange.module.trade.enums.ContractOrderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-08-26
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractOrderMQMessage {
    /**
     * 订单id
     */
    private Long id;
    /**
     * 委托订单号
     */
    private String orderNo;
    /**
     * 持仓记录编号
     */
    private String positionNo;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 订单类型
     * {@link ContractOrderTypeEnum}
     */
    private Integer orderType;
    /**
     * 消息类型
     * {@link ContractOrderMQMessageType}
     */
    private Integer messageType;
}
