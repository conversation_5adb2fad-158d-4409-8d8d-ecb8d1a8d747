package com.rf.exchange.module.trade.controller.app.trade.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.framework.common.validation.InList;
import com.rf.exchange.module.trade.enums.ContractMarginTypeEnum;
import com.rf.exchange.module.trade.enums.ContractOrderPriceTypeEnum;
import com.rf.exchange.module.trade.enums.ContractOrderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * APP 合约买卖 请求参数
 * <AUTHOR>
 * @since 2024-07-12
 */
@Schema(description = "合约开仓请求参数")
@Data
public class AppContractOrderCreateReqVO {

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{ARG_VALUE_ERROR}")
    private String code;

    @Schema(description = "委托金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{ARG_VALUE_ERROR}")
    private BigDecimal orderAmount;

    @Schema(description = "委托金额法币币种", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{ARG_VALUE_ERROR}")
    private String amountCurrency;

    @Schema(description = "委托价格(限价订单需要传这个参数, 市价单这个参数不起作用)")
    private BigDecimal orderPrice;

    @Schema(description = "交易方向 0:做空 1:做多", requiredMode = Schema.RequiredMode.REQUIRED)
    @InList(values = {0, 1}, message = "{ARG_VALUE_ERROR}")
    private Integer shortLong;

    @Schema(description = "倍率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{ARG_LEVERAGE_IS_EMPTY}")
    private Integer leverage;
    /**
     * {@link ContractOrderPriceTypeEnum}
     */
    @Schema(description = "价格类型 0市价 1限价", requiredMode = Schema.RequiredMode.REQUIRED)
    @InList(values = {0, 1}, message = "{ARG_VALUE_ERROR}")
    private Integer priceType;
    /**
     * {@link ContractOrderTypeEnum}
     */
    @Schema(description = "订单类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @InList(values = {0, 1, 2, 3}, message = "{ARG_VALUE_ERROR}")
    private Integer orderType;

    @JsonIgnore
    @InEnum(value = ContractMarginTypeEnum.class,message = "{ARG_VALUE_ERROR}")
    private Integer marginType = ContractMarginTypeEnum.ISOLATED_MARGIN.getType();

    @Schema(description = "止盈价格（开仓时可选设置）")
    private BigDecimal stopProfitPrice;

    @Schema(description = "止损价格（开仓时可选设置）")
    private BigDecimal stopLossPrice;
}
