package com.rf.exchange.module.trade.convert;

import com.rf.exchange.module.trade.api.dto.TimeContractRecordDTO;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-12
 */
@Mapper
public interface TradeConvert {
    TradeConvert INSTANCE = Mappers.getMapper(TradeConvert.class);

    TimeContractRecordDTO convert(TimeContractRecordDO recordDO);

    TimeContractRecordDO convert2(TimeContractRecordDTO bean);

    List<TimeContractRecordDTO> convertList(List<TimeContractRecordDO> recordDOS);
}
