package com.rf.exchange.module.trade.service.contract;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.lock.annotation.Lock4j;
import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.common.util.order.OrderUtil;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.member.api.balance.MemberBalanceApi;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceDTO;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractOrderSaveReqVO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractPositionPageReqVO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractPositionRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.*;
import com.rf.exchange.module.trade.controller.app.tradetotal.vo.AppContractSummaryRespVO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractPositionDO;
import com.rf.exchange.module.trade.dal.mysql.contract.ContractPositionMapper;
import com.rf.exchange.module.trade.dal.redis.ContractMatchEnginRedisDAO;
import com.rf.exchange.module.trade.enums.*;
import com.rf.exchange.module.trade.matchengine.ContractLiquidateEngine;
import com.rf.exchange.module.trade.matchengine.ContractOrderMatchEngine;
import com.rf.exchange.module.trade.mq.ContractOrderProducer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.trade.enums.ErrorCodeConstants.*;

/**
 * 合约持仓记录 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ContractPositionServiceImpl implements ContractPositionService {

    @Resource
    private ContractPositionMapper contractPositionMapper;
    @Resource
    private ContractOrderService contractOrderService;
    @Resource
    private ContractOrderProducer orderMessageProducer;
    @Resource
    private ContractWebSocketService contractWebSocketService;
    @Resource
    private ContractMatchEnginRedisDAO matchEnginRedisDAO;
    @Resource
    @Lazy
    private ContractLiquidateEngine liquidateEngine;
    @Resource
    @Lazy
    private ContractOrderMatchEngine matchEngine;
    @Resource
    @Lazy
    private TradePairApi tradePairApi;
    @Resource
    @Lazy
    private CandleDataApi candleDataApi;
    @Resource
    @Lazy
    private CurrencyRateApi currencyRateApi;
    @Resource
    @Lazy
    private MemberBalanceApi memberBalanceApi;

    @Override
    public AppContractSummaryRespVO appGetContractSummary(LoginUser loginUser) {
        final MemberBalanceDTO balanceDTO = memberBalanceApi.getMemberBalanceCached(loginUser.getId());

        LambdaQueryWrapperX<ContractPositionDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ContractPositionDO::getUserId, loginUser.getId());
        wrapper.eq(ContractPositionDO::getPositionStatus, PositionStatusEnum.OPEN.getStatus());
        final List<ContractPositionDO> positions = contractPositionMapper.selectList(wrapper);
        BigDecimal totalProfitLoss = BigDecimal.ZERO;
        BigDecimal totalMargin = BigDecimal.ZERO;
        BigDecimal totalContractValue = BigDecimal.ZERO;
        //
        for (ContractPositionDO position : positions) {
            totalProfitLoss = totalProfitLoss.add(liquidateEngine.getProfitLoss(position.getRecordNo()));
            totalMargin = totalMargin.add(position.getMargin());

            final CurrentPriceRespDTO currentPrice = candleDataApi.getCurrentPrice(position.getCode());
            totalContractValue = totalContractValue.add(position.getVolume().multiply(currentPrice.getCurrentPrice()));
        }
        AppContractSummaryRespVO respVO = new AppContractSummaryRespVO();
        respVO.setTotalProfitLoss(DecimalFormatUtil.formatWithScale(totalProfitLoss, 2));
        respVO.setMarginTotal(DecimalFormatUtil.formatWithScale(totalMargin, 2));
        respVO.setValidBalance(DecimalFormatUtil.formatWithScale(balanceDTO.getUsdtBalance(), 4));
        respVO.setContractValue(DecimalFormatUtil.formatWithScale(totalContractValue, 4));
        return respVO;
    }

    @Override
    public PageResult<AppContractPositionRespVO> appGetContractPositionPage(AppContractPositionPageReqVO reqVO, LoginUser loginUser, Long tenantId) {
        PositionStatusEnum statusEnum;
        if (reqVO.getType() == 1) { // 持仓中
            statusEnum = PositionStatusEnum.OPEN;
        } else if (reqVO.getType() == 2) { // 已平仓
            statusEnum = PositionStatusEnum.CLOSED;
        } else {
            throw exception(GlobalErrorCodeConstants.VALUE_ERROR);
        }
        LambdaQueryWrapperX<ContractPositionDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eqIfPresent(ContractPositionDO::getUserId, loginUser.getId());
        wrapperX.eqIfPresent(ContractPositionDO::getPositionStatus, statusEnum.getStatus());
        if (reqVO.getType() == 1) {
            wrapperX.orderByDesc(ContractPositionDO::getCreateTime);
        } else if (reqVO.getType() == 2) {
            wrapperX.orderByDesc(ContractPositionDO::getUpdateTime);
        }
        PageResult<ContractPositionDO> pageResult = contractPositionMapper.selectPage(reqVO, wrapperX);

        //
        final Set<String> orderNos = pageResult.getList().stream().map(ContractPositionDO::getRecordNo).collect(Collectors.toSet());
        // 关联的委托列表
        final List<ContractOrderDO> orderList = contractOrderService.getAllOrderByPositionOrderNos(orderNos);
        Map<String, List<ContractOrderDO>> orderMap = new HashMap<>(orderList.size());
        for (ContractOrderDO order : orderList) {
            List<ContractOrderDO> orders = orderMap.computeIfAbsent(order.getPositionRecordNo(), k -> new ArrayList<>());
            orders.add(order);
        }

        final PageResult<AppContractPositionRespVO> resultPage = BeanUtils.toBean(pageResult, AppContractPositionRespVO.class);
        resultPage.getList().forEach(respVO -> {
            // 当前价格
            BigDecimal tradePairRealTimePrice = matchEngine.getPriceByCode(respVO.getCode());
            respVO.setCurrentPrice(tradePairRealTimePrice);

            if (PositionStatusEnum.OPEN.getStatus().equals(respVO.getPositionStatus())) {
                final BigDecimal liquidatePrice = liquidateEngine.getLiquidatePrice(respVO.getRecordNo());
                final BigDecimal profitLoss = liquidateEngine.getProfitLoss(respVO.getRecordNo());
                final BigDecimal profitLossRate = profitLoss.divide(respVO.getMargin(), 2, RoundingMode.HALF_UP);
                respVO.setEstimateLiquidatePrice(liquidatePrice);
                respVO.setProfitLoss(profitLoss.setScale(4, RoundingMode.HALF_UP));
                respVO.setProfitLossRate(profitLossRate);

                final BigDecimal marginRate = liquidateEngine.getMarginRate(respVO.getRecordNo());
                // 持仓的保证金率
                respVO.setMarginRate(DecimalFormatUtil.formatPlain(marginRate));
            } else {
                respVO.setProfitLoss(respVO.getProfitLoss().setScale(4, RoundingMode.HALF_UP));
            }

            // 委托手续费和委托金额
            final List<ContractOrderDO> finishOrders = orderMap.get(respVO.getRecordNo());
            if (CollUtil.isNotEmpty(finishOrders)) {
                BigDecimal orderFee = BigDecimal.ZERO;
                BigDecimal orderAmount = BigDecimal.ZERO;
                for (ContractOrderDO order : finishOrders) {
                    orderFee = orderFee.add(order.getFee());
                    if (order.getOrderAmount() != null) {
                        orderAmount = orderAmount.add(order.getOrderAmount());
                    }
                }
                respVO.setOrderFee(orderFee);
                respVO.setOrderAmount(orderAmount);
            }
        });
        return resultPage;
    }

    @Override
    public Boolean appCloseContractPositionWithCondition(AppContractCloseConditionReqVO reqVO, LoginUser loginUser, Long tenantId) {
        // 校验信息是否正确
        ContractPositionDO position = validateContractPositionExists(reqVO.getId());
        validateUserHasContractPosition(position, loginUser.getId());
        // 校验仓位是否仍然是持仓中状态
        validateContractPositionStatusStillOpen(position);
        if (reqVO.getStopProfitPrice() != null && reqVO.getStopProfitPrice().compareTo(BigDecimal.ZERO) > 0) {
            internalCreateStopConditionOrder(ContractOrderTypeEnum.STOP_PROFIT_ORDER, reqVO.getStopProfitPrice(), position, tenantId);
        }
        if (reqVO.getStopLossPrice() != null && reqVO.getStopLossPrice().compareTo(BigDecimal.ZERO) > 0) {
            internalCreateStopConditionOrder(ContractOrderTypeEnum.STOP_LOSS_ORDER, reqVO.getStopLossPrice(), position, tenantId);
        }
        return true;
    }

    @Master
    @DSTransactional
    public void internalCreateStopConditionOrder(ContractOrderTypeEnum orderTypeEnum, BigDecimal orderPrice, ContractPositionDO position, long tenantId) {
        // 先判断仓位是否已经设置了止盈和止损委托订单
        if (ContractOrderTypeEnum.STOP_PROFIT_ORDER.equals(orderTypeEnum) && StrUtil.isNotEmpty(position.getStopProfitOrderNo())) { // 止盈订单
            final ContractOrderDO order = contractOrderService.getContractOrderByOrderNo(position.getStopProfitOrderNo());
            // 如果已经创建的委托订单价格和新的委托订单价格相同则不处理
            if (order.getOrderPrice().compareTo(orderPrice) != 0) {
                order.setOrderPrice(orderPrice);
                contractOrderService.updateOrder(order, true);
                // 更新持仓信息
                position.setStopProfitPrice(orderPrice);
                contractPositionMapper.updateById(position);
            }

        } else if (ContractOrderTypeEnum.STOP_LOSS_ORDER.equals(orderTypeEnum) && StrUtil.isNotEmpty(position.getStopLossOrderNo())) { // 止损订单
            final ContractOrderDO order = contractOrderService.getContractOrderByOrderNo(position.getStopLossOrderNo());
            // 如果已经创建的委托订单价格和新的委托订单价格相同则不处理
            if (order.getOrderPrice().compareTo(orderPrice) != 0) {
                order.setOrderPrice(orderPrice);
                contractOrderService.updateOrder(order, true);
                // 更新持仓信息
                position.setStopLossPrice(orderPrice);
                contractPositionMapper.updateById(position);
            }

        } else {
            ContractOrderSaveReqVO orderReqVO = BeanUtils.toBean(position, ContractOrderSaveReqVO.class);
            orderReqVO.setId(null);
            orderReqVO.setPositionRecordNo(position.getRecordNo());
            orderReqVO.setOrderVolume(position.getVolume());
            orderReqVO.setOrderPrice(orderPrice);
            orderReqVO.setOrderType(orderTypeEnum.getType());
            orderReqVO.setVisible(false);
            orderReqVO.setShortLong(position.getShortLong());
            orderReqVO.setOrderAmount(position.getVolume().multiply(orderPrice));
            orderReqVO.setOrderAmountCurrency(position.getMarginCurrency());
            orderReqVO.setOrderNo(OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.CONTRACT_ORDER_CREATE));
            orderReqVO.setLeverage(position.getLeverage());
            orderReqVO.setOrderStatus(ContractOrderStatusEnum.ORDERING.getStatus());
            orderReqVO.setPriceType(ContractOrderPriceTypeEnum.LIMIT_PRICE.getType());
            contractOrderService.createContractOrder(orderReqVO, tenantId);

            if (ContractOrderTypeEnum.STOP_PROFIT_ORDER.equals(orderTypeEnum)) {
                position.setStopProfitOrderNo(orderReqVO.getOrderNo());
                position.setStopProfitPrice(orderPrice);
                contractPositionMapper.updateById(position);

            } else if (ContractOrderTypeEnum.STOP_LOSS_ORDER.equals(orderTypeEnum)) {
                position.setStopLossOrderNo(orderReqVO.getOrderNo());
                position.setStopLossPrice(orderPrice);
                contractPositionMapper.updateById(position);
            }
        }
    }

    @Override
    @Master
    @DSTransactional
    public Boolean appCloseContractPosition(AppContractCloseReqVO reqVO, LoginUser loginUser, Long tenantId) {
        // 校验仓位信息
        ContractPositionDO position = validateContractPositionExists(reqVO.getId());
        validateUserHasContractPosition(position, loginUser.getId());
        validateContractPositionStatusStillOpen(position);
        return closePosition(position);
    }

    @Override
    public Boolean appBatchCloseContractPosition(AppContractBatchCloseReqVO reqVO, LoginUser loginUser, Long tenantId) {
        LambdaQueryWrapperX<ContractPositionDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ContractPositionDO::getUserId, loginUser.getId());
        wrapper.eq(ContractPositionDO::getPositionStatus, PositionStatusEnum.OPEN.getStatus());
        if (reqVO.getType() != ShortLongEnum.ALL.getType()) {
            wrapper.eqIfPresent(ContractPositionDO::getShortLong, reqVO.getType());
        }
        List<ContractPositionDO> records = contractPositionMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(records)) {
            return true;
        }
        for (ContractPositionDO record : records) {
            closePosition(record);
        }
        return true;
    }

    @Override
    @Master
    @DSTransactional
    public ContractPositionDO createContractPositionByOrder(ContractOrderDO contractOrder, BigDecimal openPrice, TradePairRespDTO tradePair, Long tenantId) {
        ContractPositionDO positionRecord = BeanUtils.toBean(contractOrder, ContractPositionDO.class);
        positionRecord.setPositionStatus(PositionStatusEnum.OPEN.getStatus());
        positionRecord.setRecordNo(OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.CONTRACT_POSITION_CREATE));
        positionRecord.setOpenPrice(openPrice);
        positionRecord.setOpenTime(DateUtils.getUnixTimestampNow());
        positionRecord.setMargin(contractOrder.getMargin());
        positionRecord.setMarginCurrency(contractOrder.getOrderAmountCurrency());
        positionRecord.setAssetType(tradePair.getAssetType());
        positionRecord.setVolume(contractOrder.getOrderVolume());
        positionRecord.setVolumeValue(contractOrder.getOrderAmount());
        positionRecord.setVolumeValueCurrency(contractOrder.getOrderAmountCurrency());
        // 如果是持仓贵金属或者大宗商品则持仓资产为name
        if (TradeAssetTypeEnum.FOREX.getType().equals(tradePair.getAssetType())) {
            positionRecord.setVolumeAsset(tradePair.getName());
        } else {
            positionRecord.setVolumeAsset(tradePair.getBaseAsset());
        }
        contractPositionMapper.insert(positionRecord);
        return positionRecord;
    }

    @Override
    @Master
    public void autoStopContractPositionByOrder(ContractOrderDO contractOrder, BigDecimal price, ContractPositionCloseTypeEnum closeTypeEnum) {
        final ContractPositionDO position = contractPositionMapper.selectPositionByOrderNo(contractOrder.getPositionRecordNo());
        if (null == position) {
            throw exception(TRADE_CONTRACT_POSITION_RECORD_NOT_EXISTS);
        }
        final BigDecimal profitLoss = internalStopPosition(position, price, closeTypeEnum, closeTypeEnum.getName());
        // 更新用户的余额
        updateUserBalanceByProfitLoss(profitLoss, position);
    }

    @Override
    @Master
    @DSTransactional
    @Lock4j(keys = "#triggerPosition.recordNo", acquireTimeout = 1000, expire = 2000) // 过期时间为2秒,如果业务执行的时间超过3秒则需要延长
    public void forceLiquidateAllContractPosition(ContractPositionDO triggerPosition, Map<String, BigDecimal> otherTradePriceMap) {
        // 获取触发强平持仓记录的用户下面所有的持仓记录
        LambdaQueryWrapperX<ContractPositionDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ContractPositionDO::getUserId, triggerPosition.getUserId());
        wrapper.eq(ContractPositionDO::getPositionStatus, PositionStatusEnum.OPEN.getStatus());
        final List<ContractPositionDO> positions = contractPositionMapper.selectList(wrapper);

        // 如果没有任何还在持仓中的订单则不处理
        if (CollUtil.isEmpty(positions)) {
            return;
        }

        BigDecimal totalProfitLoss = BigDecimal.ZERO;
        for (ContractPositionDO position : positions) {
            final BigDecimal realTimePrice = otherTradePriceMap.get(position.getCode());
            if (null != realTimePrice) {
                final BigDecimal profitLoss = internalStopPosition(position, realTimePrice, ContractPositionCloseTypeEnum.FORCED_LIQUIDATED, "自动强平所有持仓");
                totalProfitLoss = totalProfitLoss.add(profitLoss);
            }
        }
        // 如果总收益不小于0则表示强平所有订单的逻辑错误，因为用户所有持仓的总收益不小于0
        if (BigDecimal.ZERO.compareTo(totalProfitLoss) < 0) {
            log.error("强平所有订单错误 总收益不小于0");
            throw exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }

        final MemberBalanceDTO balanceDTO = memberBalanceApi.getMemberBalanceCached(triggerPosition.getUserId());
        MemberUserRespDTO user = new MemberUserRespDTO();
        user.setId(triggerPosition.getUserId());
        user.setTenantId(triggerPosition.getTenantId());
        user.setUsername(triggerPosition.getUsername());
        user.setUsdtBalance(balanceDTO.getUsdtBalance());
        user.setUsdtFrozenBalance(balanceDTO.getUsdtFrozenBalance());

        MemberBalanceUpdateReqVO balanceUpdate = new MemberBalanceUpdateReqVO();
        balanceUpdate.setUserId(triggerPosition.getUserId());
        balanceUpdate.setAmount(totalProfitLoss);
        balanceUpdate.setDecrToZeroIfLess(true);

        MemberTransactionSaveReqVO transactionSave = new MemberTransactionSaveReqVO();
        transactionSave.setUserId(triggerPosition.getUserId());
        transactionSave.setTransactionType(MemberTransactionsTypeEnum.CONTRACT_CLOSE_POSITION);
        transactionSave.setRemark(MemberTransactionsTypeEnum.CONTRACT_CLOSE_POSITION.getLabel());
        transactionSave.setBizOrderNo(triggerPosition.getRecordNo());
        transactionSave.setAmountCurrency(triggerPosition.getMarginCurrency());
        transactionSave.setTenantId(triggerPosition.getTenantId());

        MemberBalanceUpdateContext context = new MemberBalanceUpdateContext(user, balanceUpdate, transactionSave);
        memberBalanceApi.decrUserBalance(context);
    }

    @DSTransactional
    public BigDecimal internalStopPosition(ContractPositionDO position,
                                           BigDecimal price,
                                           ContractPositionCloseTypeEnum closeTypeEnum,
                                           String remark) {
        // 获取交易对信息
        TradePairRespDTO tradePairDTO = tradePairApi.getTradePairCachedByCode(position.getCode());

        // 获取交易对报价资产和委托订单法币之间的汇率, 如果委托订单的金额是CNY，交易对的报价资产为USD或者USDT，则这里的汇率为 USD/CNY
        CurrencyRateDTO currencyRateDTO = currencyRateApi.getTenantCurrencyRate(position.getTenantId(), tradePairDTO.getQuoteAsset(), position.getMarginCurrency());
        if (currencyRateDTO == null) {
            throw exception(TRADE_CONTRACT_CURRENCY_NOT_EXISTS);
        }

        // 计算实际收益和收益
        BigDecimal profitLoss = calculatePositionProfitLoss(position.getVolume(),
                position.getOpenPrice(),
                price,
                currencyRateDTO.getRateValue(),
                position.getLeverage(),
                position.getShortLong());
        // 保证盈亏值不要超过保证金
        if (profitLoss.compareTo(BigDecimal.ZERO) < 0 && profitLoss.abs().compareTo(position.getMargin()) > 0) {
            profitLoss = position.getMargin().multiply(BigDecimal.valueOf(-1));
        }
        // 盈亏收益率
        final BigDecimal profitLossRate = profitLoss.divide(position.getMargin(), 2, RoundingMode.HALF_UP);
        position.setProfitLoss(profitLoss);
        position.setProfitLossRate(profitLossRate);
        // 平仓价格和平仓时间
        position.setClosePrice(price);
        position.setCloseTime(System.currentTimeMillis());
        position.setCloseType(closeTypeEnum.getType());
        position.setRemark(remark);
        position.setPositionStatus(PositionStatusEnum.CLOSED.getStatus());
        contractPositionMapper.updateById(position);

        // 发送仓位变化
        contractWebSocketService.sendPositionDataMessage(position);

        log.info("持仓结束 仓位编号:[{}] 结束价格:[{}] 平仓类型:[{}]", position.getRecordNo(), price, closeTypeEnum.getName());
        return profitLoss;
    }

    public boolean closePosition(ContractPositionDO position) {
        TradePairRespDTO tradePairDTO = tradePairApi.getTradePairCachedByCode(position.getCode());
        // 更新持仓状态
        position.setPositionStatus(PositionStatusEnum.CLOSED.getStatus());
        // 当前的交易对价格
        CurrentPriceRespDTO currentPrice = candleDataApi.getCurrentPrice(position.getCode());

        // 获取交易对报价资产和委托订单法币之间的汇率, 如果委托订单的金额是CNY，交易对的报价资产为USD或者USDT，则这里的汇率为 USD/CNY
        CurrencyRateDTO currencyRateDTO = currencyRateApi.getTenantCurrencyRate(position.getTenantId(), tradePairDTO.getQuoteAsset(), position.getMarginCurrency());
        if (currencyRateDTO == null) {
            throw exception(TRADE_CONTRACT_CURRENCY_NOT_EXISTS);
        }

        // 计算盈亏和收益率
        BigDecimal profitLoss = calculatePositionProfitLoss(position.getVolume(),
                position.getOpenPrice(),
                currentPrice.getCurrentPrice(),
                currencyRateDTO.getRateValue(),
                position.getLeverage(),
                position.getShortLong());
        BigDecimal profitRate = profitLoss.divide(position.getMargin(), 2, RoundingMode.HALF_UP);
        position.setProfitLoss(profitLoss);
        position.setProfitLossRate(profitRate);
        position.setClosePrice(currentPrice.getCurrentPrice());
        // 平仓时间
        position.setCloseTime(DateUtils.getUnixTimestampNow());
        position.setCloseType(ContractPositionCloseTypeEnum.MANUAL_STOP.getType());
        contractPositionMapper.updateById(position);

        // 更新用户余额
        updateUserBalanceByProfitLoss(profitLoss, position);

        // 查询所有持仓关联的委托订单
        final List<ContractOrderDO> relatedOrders = contractOrderService.getRelatedOrdersByPositionOrderNo(position.getRecordNo());
        for (ContractOrderDO relatedOrder : relatedOrders) {
            if (relatedOrder.getOrderStatus().equals(ContractOrderStatusEnum.ORDERING.getStatus())) {
                relatedOrder.setOrderStatus(ContractOrderStatusEnum.CANCELED.getStatus());
                relatedOrder.setRemark("手动平仓自动取消关联委托");
                // 取消委托
                contractOrderService.cancelOrder(relatedOrder);
            }
        }

        // 发送仓位状态变化WS消息
        contractWebSocketService.sendPositionDataMessage(position);
        return true;
    }

    @DSTransactional
    public void updateUserBalanceByProfitLoss(BigDecimal profitLoss, ContractPositionDO position) {
        BigDecimal changeAmount;
        if (profitLoss.compareTo(BigDecimal.ZERO) > 0) {
            changeAmount = position.getMargin().add(profitLoss);
        } else if (profitLoss.compareTo(BigDecimal.ZERO) < 0) {
            // 如果changeAmount的结果小于0表示用户除了保证金全部亏损以外，用户的余额还需要扣除
            changeAmount = position.getMargin().subtract(profitLoss.abs());
        } else {
            changeAmount = position.getMargin();
        }

        // 如果盈亏为0则不处理
        if (changeAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        final MemberBalanceDTO balanceDTO = memberBalanceApi.getMemberBalanceCached(position.getUserId());
        MemberUserRespDTO user = new MemberUserRespDTO();
        user.setId(position.getUserId());
        user.setTenantId(position.getTenantId());
        user.setUsername(position.getUsername());
        user.setUsdtBalance(balanceDTO.getUsdtBalance());
        user.setUsdtFrozenBalance(balanceDTO.getUsdtFrozenBalance());

        MemberBalanceUpdateReqVO balanceUpdate = new MemberBalanceUpdateReqVO();
        balanceUpdate.setUserId(position.getUserId());
        balanceUpdate.setAmount(changeAmount);
        balanceUpdate.setDecrToZeroIfLess(true);

        MemberTransactionSaveReqVO transactionSave = new MemberTransactionSaveReqVO();
        transactionSave.setUserId(position.getUserId());
        transactionSave.setTransactionType(MemberTransactionsTypeEnum.CONTRACT_CLOSE_POSITION);
        transactionSave.setRemark(MemberTransactionsTypeEnum.CONTRACT_CLOSE_POSITION.getLabel());
        transactionSave.setBizOrderNo(position.getRecordNo());
        transactionSave.setAmountCurrency(position.getMarginCurrency());
        transactionSave.setTenantId(position.getTenantId());

        MemberBalanceUpdateContext context = new MemberBalanceUpdateContext(user, balanceUpdate, transactionSave);

        if (changeAmount.compareTo(BigDecimal.ZERO) > 0) { // 盈利
            memberBalanceApi.incrUserBalance(context);
        } else if (changeAmount.compareTo(BigDecimal.ZERO) < 0) { // 亏损
            memberBalanceApi.decrUserBalance(context);
        }
    }

    @Override
    public BigDecimal calculatePositionProfitLoss(BigDecimal volume, BigDecimal openPrice, BigDecimal realTimePrice, BigDecimal exchangeRate, int leverage, int shortLong) {
        BigDecimal priceDifference;
        if (ShortLongEnum.SHORT.getType() == shortLong) {
            priceDifference = openPrice.subtract(realTimePrice);
        } else if (ShortLongEnum.LONG.getType() == shortLong) {
            priceDifference = realTimePrice.subtract(openPrice);
        } else {
            return BigDecimal.ZERO;
        }

        // 正确的合约盈亏计算：价格差异 × 持仓量 × 杠杆倍数 × 汇率
        return priceDifference.multiply(volume).multiply(BigDecimal.valueOf(leverage)).multiply(exchangeRate);
    }

    /**
     * 计算保证金率
     *
     * @param position     仓位信息
     * @param currentPrice 当前价格
     * @return 保证金率
     */
    private BigDecimal calculateMarginRate(ContractPositionDO position, BigDecimal currentPrice) {
        return BigDecimal.ZERO;
    }

    /**
     * 校验持仓记录是否存在
     *
     * @param id 仓位id
     * @return 持仓记录
     */
    private ContractPositionDO validateContractPositionExists(Long id) {
        ContractPositionDO positionRecord = contractPositionMapper.selectById(id);
        if (positionRecord == null) {
            throw exception(TRADE_CONTRACT_POSITION_RECORD_NOT_EXISTS);
        }
        return positionRecord;
    }

    /**
     * 校验用户是否有该持仓记录
     *
     * @param position 持仓记录
     * @param userId   用户id
     */
    private void validateUserHasContractPosition(ContractPositionDO position, Long userId) {
        if (!position.getUserId().equals(userId)) {
            throw exception(TRADE_CONTRACT_POSITION_RECORD_NOT_EXISTS);
        }
    }

    /**
     * 校验持仓是否处于持仓中状态
     *
     * @param position 持仓记录
     */
    private void validateContractPositionStatusStillOpen(ContractPositionDO position) {
        if (PositionStatusEnum.CLOSED.getStatus().equals(position.getPositionStatus())) {
            throw exception(TRADE_CONTRACT_POSITION_CLOSE_ALREADY);
        }
    }

    @Override
    @Slave
    public ContractPositionDO getContractPosition(Long id) {
        return contractPositionMapper.selectById(id);
    }

    @Override
    public ContractPositionDO getContractPositionByNo(String recordNo) {
        return contractPositionMapper.selectPositionByOrderNo(recordNo);
    }

    @Override
    @Slave
    public PageResult<ContractPositionRespVO> getContractPositionPage(ContractPositionPageReqVO pageReqVO) {
        final PageResult<ContractPositionDO> positionPage = contractPositionMapper.selectPage(pageReqVO);
        final PageResult<ContractPositionRespVO> resultPage = BeanUtils.toBean(positionPage, ContractPositionRespVO.class);
        resultPage.getList().forEach(position -> {
            if (PositionStatusEnum.OPEN.getStatus().equals(position.getPositionStatus())) {
                final BigDecimal liquidatePrice = liquidateEngine.getLiquidatePrice(position.getRecordNo());
                final BigDecimal profitLoss = liquidateEngine.getProfitLoss(position.getRecordNo());
                position.setEstimateLiquidatePrice(liquidatePrice);
                position.setProfitLoss(profitLoss);
                position.setProfitLossRate(profitLoss.divide(position.getMargin(), 2, RoundingMode.HALF_UP));
            }
        });
        return resultPage;
    }

    @Override
    public BigDecimal getContractPositionTotalMargin(Long userId) {
        LambdaQueryWrapperX<ContractPositionDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ContractPositionDO::getUserId, userId);
        wrapper.eq(ContractPositionDO::getPositionStatus, PositionStatusEnum.OPEN.getStatus());
        final List<ContractPositionDO> positions = contractPositionMapper.selectList(wrapper);
        final Set<String> codes = positions.stream().map(ContractPositionDO::getCode).collect(Collectors.toSet());
        //
        final Map<String, CurrentPriceRespDTO> priceMap = candleDataApi.getCurrentPriceListByIdSet(codes);
        BigDecimal totalMargin = BigDecimal.ZERO;

        for (ContractPositionDO position : positions) {
            final CurrentPriceRespDTO priceDTO = priceMap.get(position.getCode());
            if (null != priceDTO) {
                // 汇率
                //CurrencyRateDTO currencyRateDTO = currencyRateApi.getTenantCurrencyRate(position.getTenantId(), position.getVolumeValueCurrency(), position.getMarginCurrency());
                totalMargin = totalMargin.add(position.getMargin());
            }
        }
        return totalMargin;
    }

    @Override
    public List<ContractPositionDO> getUnStoppedPositionsByRecordNos(Set<String> recordNos) {
        if (CollUtil.isEmpty(recordNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapperX<ContractPositionDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.in(ContractPositionDO::getRecordNo, recordNos);
        wrapper.eq(ContractPositionDO::getPositionStatus, PositionStatusEnum.OPEN.getStatus());
        return contractPositionMapper.selectList(wrapper);
    }
}