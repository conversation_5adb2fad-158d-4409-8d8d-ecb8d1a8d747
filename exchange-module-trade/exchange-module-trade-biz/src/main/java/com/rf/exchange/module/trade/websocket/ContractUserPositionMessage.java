package com.rf.exchange.module.trade.websocket;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户的持仓Websocket消息
 *
 * <AUTHOR>
 * @since 2024-09-08
 */
@Data
public class ContractUserPositionMessage implements Serializable {
    /**
     * 仓位id
     */
    private Long id;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 持仓编号
     */
    private String recordNo;
    /**
     * 预估强平金额
     */
    private String estimateLiquidatePrice;
    /**
     * 盈亏金额
     */
    private String profitLoss;
    /**
     * 保证金率
     */
    private String marginRate;
    /**
     * 持仓状态 0:持仓中 1:已平仓
     */
    private Integer positionStatus;
}
