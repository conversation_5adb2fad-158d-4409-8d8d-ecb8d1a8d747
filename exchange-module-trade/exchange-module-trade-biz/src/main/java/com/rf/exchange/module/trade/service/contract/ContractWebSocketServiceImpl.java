package com.rf.exchange.module.trade.service.contract;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import com.rf.exchange.framework.websocket.core.message.WebSocketPushMessage;
import com.rf.exchange.framework.websocket.core.session.WebSocketSessionManager;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgCmdEnum;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgTypeEnum;
import com.rf.exchange.module.infra.api.websocket.WebSocketSenderApi;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractPositionDO;
import com.rf.exchange.module.trade.dal.redis.ContractLiquidateEngineRedisDAO;
import com.rf.exchange.module.trade.websocket.ContractUserPositionMessage;
import com.rf.exchange.module.trade.websocket.ContractWebSocketMessage;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-05
 */
@Service
public class ContractWebSocketServiceImpl implements ContractWebSocketService {

    @Resource
    private WebSocketSenderApi webSocketSenderApi;
    @Resource
    private ContractLiquidateEngineRedisDAO liquidateEngineRedisDAO;
    @Resource
    private WebSocketSessionManager sessionManager;

    @Override
    public void sendContractOrderCompleteMessage(ContractOrderDO orderDO) {
        // 构建消息
        ContractWebSocketMessage messageData = new ContractWebSocketMessage();
        messageData.setId(orderDO.getId());
        messageData.setOrderNo(orderDO.getOrderNo());
        messageData.setOrderStatus(orderDO.getOrderStatus());

        WebSocketPushMessage<ContractWebSocketMessage> pushMessage = new WebSocketPushMessage<>();
        pushMessage.setType(WebSocketMsgTypeEnum.PUSH.getType());
        pushMessage.setCmd(WebSocketMsgCmdEnum.CONTRACT_ORDER_COMPLETE.getCmd());
        pushMessage.setTs(System.currentTimeMillis());
        pushMessage.setData(messageData);

        // 获取用户的所有websocket session
        final Collection<String> userSessions = sessionManager.getSessionIds(orderDO.getTenantId(), UserTypeEnum.MEMBER.getValue(), orderDO.getUserId());
        for (String session : userSessions) {
            webSocketSenderApi.sendObject(session, WebSocketMsgTypeEnum.PUSH.getType(), pushMessage);
        }
    }

    @Override
    public void sendPositionDataMessage(ContractPositionDO positionDO) {
        final BigDecimal liquidatePrice = liquidateEngineRedisDAO.getLiquidatePriceByPositionNo(positionDO.getRecordNo());
        final BigDecimal profitLoss = liquidateEngineRedisDAO.getProfitLossByPositionNo(positionDO.getRecordNo());
        final BigDecimal marginRate = liquidateEngineRedisDAO.getMarginRateByPositionNo(positionDO.getRecordNo());

        ContractUserPositionMessage messageData = new ContractUserPositionMessage();
        messageData.setId(positionDO.getId());
        messageData.setCode(positionDO.getCode());
        messageData.setRecordNo(positionDO.getRecordNo());
        messageData.setPositionStatus(positionDO.getPositionStatus());
        messageData.setEstimateLiquidatePrice(DecimalFormatUtil.formatWithScale(liquidatePrice, 4));
        messageData.setProfitLoss(DecimalFormatUtil.formatWithScale(profitLoss, 4));
        messageData.setMarginRate(DecimalFormatUtil.formatPlain(marginRate));

        WebSocketPushMessage<ContractUserPositionMessage> pushMessage = new WebSocketPushMessage<>();
        pushMessage.setType(WebSocketMsgTypeEnum.PUSH.getType());
        pushMessage.setCmd(WebSocketMsgCmdEnum.CONTRACT_POSITION_DATA.getCmd());
        pushMessage.setTs(System.currentTimeMillis());
        pushMessage.setData(messageData);

        // 获取用户的所有websocket session
        final Collection<String> userSessions = sessionManager.getSessionIds(positionDO.getTenantId(), UserTypeEnum.MEMBER.getValue(), positionDO.getUserId());
        for (String session : userSessions) {
            webSocketSenderApi.sendObject(session, WebSocketMsgTypeEnum.PUSH.getType(), pushMessage);
        }
    }

    @Override
    public void sendUserContractPositionMessage(List<ContractPositionDO> positionDOS) {
        if (CollUtil.isEmpty(positionDOS)) {
            return;
        }
        for (ContractPositionDO positionDO : positionDOS) {
            sendPositionDataMessage(positionDO);
        }
    }
}
