package com.rf.exchange.module.trade.controller.app.trade.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.module.trade.enums.ContractPositionCloseTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-08-15
 */
@Schema(description = "合约持仓接口 Response VO")
@Data
public class AppContractPositionRespVO {

    @Schema(description = "持仓订单id")
    private Long id;

    @Schema(description = "持仓订单编号")
    private String recordNo;

    @Schema(description = "交易对代码")
    private String code;

    @Schema(description = "多空类型 0:做空 1:做多")
    private Integer shortLong;

    /**
     * {@link ContractPositionCloseTypeEnum}
     */
    @Schema(description = "平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平")
    private Integer closeType;

    @Schema(description = "仓位状态 0:持仓中 1:已平仓")
    private Integer positionStatus;

    @Schema(description = "保证金")
    private BigDecimal margin;

    @Schema(description = "保证金法币币种")
    private String marginCurrency;

    @Schema(description = "保证金率")
    private String marginRate;

    @Schema(description = "开仓价格")
    private BigDecimal openPrice;

    @Schema(description = "平仓价格")
    private BigDecimal closePrice;

    @Schema(description = "当前价格")
    private BigDecimal currentPrice;

    @Schema(description = "持仓量")
    private BigDecimal volume;

    @Schema(description = "持仓资产")
    private String volumeAsset;

    @Schema(description = "持仓价值")
    private BigDecimal volumeValue;

    @Schema(description = "持仓价值法币币种")
    private String volumeValueCurrency;

    @Schema(description = "预估强平价格")
    private BigDecimal estimateLiquidatePrice;

    @Schema(description = "收益亏损")
    private BigDecimal profitLoss;

    @Schema(description = "收益亏损率")
    private BigDecimal profitLossRate;

    @Schema(description = "止盈价")
    private BigDecimal stopProfitPrice;

    @Schema(description = "止损价")
    private BigDecimal stopLossPrice;

    @Schema(description = "杠杆倍数")
    private Integer leverage;

    @Schema(description = "委托手续费")
    private BigDecimal orderFee;

    @Schema(description = "委托金额")
    private BigDecimal orderAmount;

    @Schema(description = "开仓时间")
    private Long openTime;

    @Schema(description = "平仓时间")
    private Long closeTime;

    @JsonIgnore
    private Long tenantId;
}
