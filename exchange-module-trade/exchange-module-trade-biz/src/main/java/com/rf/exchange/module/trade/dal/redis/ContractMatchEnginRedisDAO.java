package com.rf.exchange.module.trade.dal.redis;

import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Set;

import static com.rf.exchange.module.trade.dal.redis.RedisKeyConstants.CONTRACT_UNFINISHED_ORDER_NO;

/**
 * <AUTHOR>
 * @since 2024-09-03
 */
@Repository
public class ContractMatchEnginRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取所有未完成的委托订单编号
     *
     * @return 订单编号集合
     */
    public Set<String> getAllOrderNo() {
        return stringRedisTemplate.opsForSet().members(CONTRACT_UNFINISHED_ORDER_NO);
    }

    /**
     * 添加未完成的订单号
     *
     * @param orderNo 订单号
     */
    public void addOrderNo(String orderNo) {
        stringRedisTemplate.opsForSet().add(CONTRACT_UNFINISHED_ORDER_NO, orderNo);
    }

    public void setOrderNos(String[] orderNos) {
        stringRedisTemplate.delete(CONTRACT_UNFINISHED_ORDER_NO);
        if (null == orderNos || orderNos.length == 0) {
            return;
        }
        stringRedisTemplate.opsForSet().add(CONTRACT_UNFINISHED_ORDER_NO, orderNos);
    }

    /**
     * 清除之前的记录直接保存所有新值
     *
     * @param orderNos 新的订单号数组
     */
    public void resetWithOrderNos(String[] orderNos) {
        stringRedisTemplate.delete(CONTRACT_UNFINISHED_ORDER_NO);
        if (null == orderNos || orderNos.length == 0) {
            return;
        }
        setOrderNos(orderNos);
    }

    /**
     * 移除订单号
     *
     * @param orderNo 订单号
     */
    public void removeOrderNo(String orderNo) {
        stringRedisTemplate.opsForSet().remove(CONTRACT_UNFINISHED_ORDER_NO, orderNo);
    }

    /**
     * 批量移除订单号
     *
     * @param orderNos 订单号集合
     */
    public void removeOrderNos(Set<String> orderNos) {
        if (CollUtil.isEmpty(orderNos)) {
            return;
        }
        stringRedisTemplate.opsForSet().remove(CONTRACT_UNFINISHED_ORDER_NO, orderNos.toArray());
    }
}
