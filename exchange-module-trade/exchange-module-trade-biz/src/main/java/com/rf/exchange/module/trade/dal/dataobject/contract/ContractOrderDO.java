package com.rf.exchange.module.trade.dal.dataobject.contract;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import com.rf.exchange.module.trade.enums.*;
import lombok.*;

import java.math.BigDecimal;

/**
 * 合约委托记录 DO
 *
 * <AUTHOR>
 */
@TableName("trade_contract_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractOrderDO extends BaseNoDeleteDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 管理后台和APP是否可见此记录
     * true: 可见
     */
    @TableField(value = "is_visible")
    private Boolean visible;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 仓位订单号
     */
    private String positionRecordNo;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 价格类型 0:市价订单 1:限价订单
     * {@link ContractOrderPriceTypeEnum}
     */
    private Integer priceType;
    /**
     * 多空类型 0:做空 1:做多
     * {@link ShortLongEnum}
     */
    private Integer shortLong;
    /**
     * 委托类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托 4:强平委托
     * {@link ContractOrderTypeEnum}
     */
    private Integer orderType;
    /**
     * 保证金类型
     * {@link ContractMarginTypeEnum}
     */
    private Integer marginType;
    /**
     * 订单状态 0:委托中 1:已成交
     * {@link ContractOrderStatusEnum}
     */
    private Integer orderStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 委托保证金
     */
    private BigDecimal margin;
    /**
     * 委托金额
     */
    private BigDecimal orderAmount;
    /**
     * 委托金额法币币种
     */
    private String orderAmountCurrency;
    /**
     * 委托币数量
     */
    private BigDecimal orderVolume;
    /**
     * 委托价格
     */
    private BigDecimal orderPrice;
    /**
     * 成交价格
     */
    private BigDecimal transactionPrice;
    /**
     * 杠杆倍数
     */
    private Integer leverage;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 租户id
     */
    private Long tenantId;
}