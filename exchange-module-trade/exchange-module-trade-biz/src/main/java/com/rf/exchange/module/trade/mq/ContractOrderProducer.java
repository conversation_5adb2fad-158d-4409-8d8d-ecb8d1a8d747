package com.rf.exchange.module.trade.mq;

import com.rf.exchange.framework.mq.rocketmq.core.producer.RocketMQProducer;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.enums.ContractOrderMQMessageType;
import com.rf.exchange.module.trade.mq.message.ContractOrderMQMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-08-26
 */
@Slf4j
@Component
public class ContractOrderProducer {

    @Value("${exchange.rocketmq.contract.topic}")
    private String topic;

    @Value("${exchange.rocketmq.contract.tag-order}")
    private String tag;

    @Resource
    private RocketMQProducer rocketMqProducer;

    /**
     * 发送合约委托创建消息
     *
     * @param order 合约委托订单
     */
    public void sendCreateOrderMessage(ContractOrderDO order) {
        ContractOrderMQMessage message = ContractOrderMQMessage.builder()
                .id(order.getId())
                .code(order.getCode())
                .orderNo(order.getOrderNo())
                .tenantId(order.getTenantId())
                .userId(order.getUserId())
                .orderType(order.getOrderType())
                .messageType(ContractOrderMQMessageType.CREATE.getType())
                .build();
        rocketMqProducer.asyncSendObject(topic, tag, message);
    }

    /**
     * 发送合约委托取消的消息
     * <p>
     * 只有在撤销限价委托订单时才发送此消息
     *
     * @param order 合约委托订单
     */
    public void sendCancelOrderMessage(ContractOrderDO order) {
        ContractOrderMQMessage message = ContractOrderMQMessage.builder()
                .id(order.getId())
                .code(order.getCode())
                .orderNo(order.getOrderNo())
                .tenantId(order.getTenantId())
                .userId(order.getUserId())
                .orderType(order.getOrderType())
                .messageType(ContractOrderMQMessageType.CANCEL.getType())
                .build();
        rocketMqProducer.asyncSendObject(topic, tag, message);
    }

    /**
     * 发送合约持仓平仓的消息
     * <p>
     * 只有手动平仓时才发送此消息，自动强平或者自动止盈/自动止损时不发送此消息
     *
     * @param order 持仓记录
     */
    public void sendManualClosePositionMessage(ContractOrderDO order) {
        ContractOrderMQMessage message = ContractOrderMQMessage.builder()
                .id(order.getId())
                .code(order.getCode())
                .orderNo(order.getOrderNo())
                .tenantId(order.getTenantId())
                .userId(order.getUserId())
                .orderType(order.getOrderType())
                // 这里还是为委托取消消息，即时是平仓，因为只是取消未成交的强平委托订单
                .messageType(ContractOrderMQMessageType.MANUAL_STOP.getType())
                .build();
        rocketMqProducer.asyncSendObject(topic, tag, message);
    }

    /**
     * 发送强平委托创建消息
     * <p>
     * 只有强平委托创建时才发送此消息，强平委托成交时不发送消息
     *
     * @param liquidateOrder 强平委托记录
     */
    public void sendLiquidatePositionMessage(ContractOrderDO liquidateOrder) {
        ContractOrderMQMessage message = ContractOrderMQMessage.builder()
                .id(liquidateOrder.getId())
                .userId(liquidateOrder.getUserId())
                .tenantId(liquidateOrder.getTenantId())
                .code(liquidateOrder.getCode())
                .orderNo(liquidateOrder.getOrderNo())
                .positionNo(liquidateOrder.getPositionRecordNo())
                .orderType(liquidateOrder.getOrderType())
                .messageType(ContractOrderMQMessageType.LIQUIDATE.getType())
                .build();
        rocketMqProducer.asyncSendObject(topic, tag, message);
    }

    /**
     * 发送委托订单更新消息
     *
     * @param order 委托订单
     */
    public void sendOrderUpdateMessage(ContractOrderDO order) {
        ContractOrderMQMessage message = ContractOrderMQMessage.builder()
                .id(order.getId())
                .userId(order.getUserId())
                .tenantId(order.getTenantId())
                .code(order.getCode())
                .orderNo(order.getOrderNo())
                .positionNo(order.getPositionRecordNo())
                .orderType(order.getOrderType())
                .messageType(ContractOrderMQMessageType.UPDATE.getType())
                .build();
        rocketMqProducer.asyncSendObject(topic, tag, message);
    }
}
