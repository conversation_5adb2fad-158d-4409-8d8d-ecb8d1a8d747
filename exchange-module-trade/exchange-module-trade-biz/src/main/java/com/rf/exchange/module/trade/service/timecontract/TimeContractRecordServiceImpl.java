package com.rf.exchange.module.trade.service.timecontract;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.lock.annotation.Lock4j;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.rf.exchange.framework.common.enums.DateIntervalEnum;
import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.common.util.order.OrderUtil;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.member.api.balance.MemberBalanceApi;
import com.rf.exchange.module.member.api.balance.MemberTransactionApi;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionDTO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.MemberUserApi;
import com.rf.exchange.module.member.api.user.dto.MemberUserConfigRespDTO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;
import com.rf.exchange.module.member.enums.certification.MemberCertificationStatusEnum;
import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantAuthConfigRespDTO;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantDictDataRespDTO;
import com.rf.exchange.module.trade.controller.admin.timecontract.vo.*;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTimeContractConfigRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractBuyReqVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractOrderDetailRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractPageReqVO;
import com.rf.exchange.module.trade.dal.dataobject.duration.TradeDurationDO;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;
import com.rf.exchange.module.trade.dal.mysql.timecontract.TimeContractRecordMapper;
import com.rf.exchange.module.trade.dal.redis.TimeContractOrderRedisDAO;
import com.rf.exchange.module.trade.enums.ShortLongEnum;
import com.rf.exchange.module.trade.enums.TimeContractOrderStatusEnum;
import com.rf.exchange.module.trade.enums.TimeContractOrderTypeEnum;
import com.rf.exchange.module.trade.service.duration.TradeDurationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.rf.exchange.module.candle.enums.ErrorCodeConstants.CANDLE_PRICE_ERROR;
import static com.rf.exchange.module.exc.enums.ErrorCodeConstants.TRADE_PAIR_NOT_EXISTS;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;
import static com.rf.exchange.module.system.enums.DictTypeConstants.TENANT_TIME_CONTRACT_LOSE;
import static com.rf.exchange.module.trade.enums.ErrorCodeConstants.*;
import static com.rf.exchange.module.trade.enums.LogRecordConstants.*;

/**
 * 限时合约交易记录 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class TimeContractRecordServiceImpl implements TimeContractRecordService {

    @Resource
    private TimeContractRecordMapper timeContractRecordMapper;
    @Resource
    private TimeContractOrderRedisDAO timeContractOrderRedisDAO;
    @Resource
    private TradeDurationService tradeDurationService;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private MemberBalanceApi memberBalanceApi;
    @Resource
    private CandleDataApi candleDataApi;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private TenantDictDataApi tenantDictDataApi;
    @Resource
    private MemberTransactionApi memberTransactionApi;

    @Override
    public List<AppTimeContractConfigRespVO> getTimeContractConfig(String tradePairCode, Long tenantId) {
        List<TradeDurationDO> durations = tradeDurationService.getDurations(tradePairCode, tenantId);
        List<AppTimeContractConfigRespVO> resultList = new ArrayList<>();

        for (TradeDurationDO durationDO : durations) {
            AppTimeContractConfigRespVO respVO = new AppTimeContractConfigRespVO();
            respVO.setId(durationDO.getId());
            respVO.setMinBuy(durationDO.getMinAmount().toString());
            respVO.setProfitRate(DecimalFormatUtil.formatPlain(durationDO.getProfitRate()));
            if (DateIntervalEnum.SEC.getInterval().equals(durationDO.getTimeUnit())) {
                respVO.setDuration(durationDO.getDuration());
            } else if (DateIntervalEnum.MIN.getInterval().equals(durationDO.getTimeUnit())) {
                int interval = durationDO.getDuration() * 60;
                respVO.setDuration(interval);
            } else if (DateIntervalEnum.HOUR.getInterval().equals(durationDO.getTimeUnit())) {
                int interval = durationDO.getDuration() * 3600;
                respVO.setDuration(interval);
            } else if (DateIntervalEnum.DAY.getInterval().equals(durationDO.getTimeUnit())) {
                int interval = durationDO.getDuration() * 86400;
                respVO.setDuration(interval);
            }
            resultList.add(respVO);
        }
        return resultList;
    }

    @Override
    @Master
    @DSTransactional
    @Lock4j(keys = {"#userId", "#tenantId"}, acquireTimeout = 1000, expire = 2000)
    public Long  createTimeContractRecord(@Valid AppTradeTimeContractBuyReqVO reqVO, Long userId, Long tenantId) {
        TradeDurationDO duration = tradeDurationService.getDuration(reqVO.getDurationId());
        if (duration == null) {
            throw exception(TRADE_DURATION_NOT_EXISTS);
        }
        if (reqVO.getAmount().compareTo(new BigDecimal(duration.getMinAmount())) < 0) {
            throw exception(TIME_CONTRACT_AMOUNT_LESS);
        }
        // 判断用户是否存在
        MemberUserRespDTO userDTO = memberUserApi.checkUserExists(userId);

        //判断是否需要认证
        TenantAuthConfigRespDTO authConfig = tenantDictDataApi.getTenantAuthConfig(userDTO.getTenantId());
        if (authConfig.isTimeContractNeedAuth()) {
            if (Objects.equals(userDTO.getCertificationStatus(), MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getType())) {
                throw exception(USER_CERTIFICATION_VERIFYING);
            }
            if (!Objects.equals(userDTO.getCertificationStatus(), MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType())) {
                throw exception(USER_CERTIFICATION_NOT_VERIFY, MemberCertificationStatusEnum.getI18n(userDTO.getCertificationStatus()));
            }
        }

        // 判断此功能是否用
        if (userDTO.getDisableTimeContract()) {
            throw exception(USER_FORBIDDEN_TIME_CONTRACT);
        }

        // 检查用户余额是否足够
        memberBalanceApi.checkUSDTBalanceEnough(userDTO.getId(), reqVO.getAmount());
        // 获取租户的配置收益率
        BigDecimal configLoseProfitRate = null;
        final TenantDictDataRespDTO loseProfitRateDictData = tenantDictDataApi.parseDictData(tenantId, TENANT_TIME_CONTRACT_LOSE, "amount_decrease_rate");
        if (loseProfitRateDictData != null && StrUtil.isNotEmpty(loseProfitRateDictData.getValue())) {
            String dictData = loseProfitRateDictData.getValue();
            if (NumberUtil.isNumber(loseProfitRateDictData.getValue())) {
                BigDecimal configLoseRate = new BigDecimal(dictData);
                if (configLoseRate.compareTo(BigDecimal.ZERO) > 0) {
                    configLoseProfitRate = configLoseRate.multiply(BigDecimal.valueOf(100));
                }
            }
        }
        // 创建业务订单号
        String orderNo = OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.TIME_CONTRACT_BUY);
        // 扣减用户USDT余额的基础信息
        MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
        balanceReqVO.setUserId(userId);
        balanceReqVO.setAmount(reqVO.getAmount());
        // 帐变基础信息
        MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
        transactionReqVO.setBizOrderNo(orderNo);
        transactionReqVO.setTransactionType(MemberTransactionsTypeEnum.BUY_TIME_CONTRACT);
        transactionReqVO.setRemark("买入限时合约");
        // 扣减用户的账户USDT余额
        memberBalanceApi.decrUserBalance(new MemberBalanceUpdateContext(userDTO, balanceReqVO, transactionReqVO));

        // 获取交易对当前的价格
        final CurrentPriceRespDTO priceDTO = candleDataApi.getCurrentPrice(reqVO.getCode());
        if (null == priceDTO) {
            throw exception(CANDLE_PRICE_ERROR);
        }

        // 插入限时合约订单记录
        TimeContractRecordDO timeContractRecord = BeanUtils.toBean(reqVO, TimeContractRecordDO.class);
        timeContractRecord.setDuration(duration.getDuration());
        timeContractRecord.setProfitRate(duration.getProfitRate());
        if (configLoseProfitRate != null) {
            timeContractRecord.setLoseProfitRate(configLoseProfitRate);
        } else {
            timeContractRecord.setLoseProfitRate(duration.getProfitRate());
        }

        timeContractRecord.setOrderPrice(priceDTO.getCurrentPrice());
        timeContractRecord.setUsername(userDTO.getUsername());
        timeContractRecord.setUserId(userId);
        timeContractRecord.setTenantId(tenantId);
        timeContractRecord.setTradeStatus(TimeContractOrderStatusEnum.WAIT_DRAW.getStatus());
        timeContractRecord.setTradeNo(orderNo);
        timeContractRecordMapper.insert(timeContractRecord);
        // 将等待结算的订单号保存到redis中
        timeContractOrderRedisDAO.set(timeContractRecord.getTradeNo());
        return timeContractRecord.getId();
    }

    @Override
    @Slave
    public AppTradeTimeContractOrderDetailRespVO getTimeContractOrderDetail(Long id, Long userId, Long tenantId) {
        LambdaQueryWrapperX<TimeContractRecordDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(TimeContractRecordDO::getId, id);
        wrapper.eqIfPresent(TimeContractRecordDO::getUserId, userId);
        wrapper.eqIfPresent(TimeContractRecordDO::getTenantId, tenantId);
        final TimeContractRecordDO recordDO = timeContractRecordMapper.selectOne(wrapper);
        final TradePairRespDTO tradePairDto = tradePairApi.getTradePairMapCached().get(recordDO.getCode());
        if (tradePairDto == null) {
            throw exception(TRADE_PAIR_NOT_EXISTS);
        }
        // 响应
        AppTradeTimeContractOrderDetailRespVO respVO = BeanUtils.toBean(recordDO, AppTradeTimeContractOrderDetailRespVO.class);
        respVO.setName(tradePairDto.getName());
        respVO.setOrderPrice(DecimalFormatUtil.formatPlain(recordDO.getOrderPrice()));
        respVO.setAmount(DecimalFormatUtil.formatPlain(recordDO.getAmount()));
        // 预计盈利金额
        BigDecimal estimateWin = calculateProfitAmount(recordDO, true);
        BigDecimal estimateLose = calculateProfitAmount(recordDO, false);
        respVO.setWinProfit(DecimalFormatUtil.formatWithScale(estimateWin, 2));
        respVO.setLoseProfit(DecimalFormatUtil.formatWithScale(estimateLose, 2));

        final CurrentPriceRespDTO currentPriceDto = candleDataApi.getCurrentPrice(recordDO.getCode());
        if (currentPriceDto != null && currentPriceDto.getCurrentPrice() != null) {
            respVO.setCurrentPrice(DecimalFormatUtil.formatWithScale(currentPriceDto.getCurrentPrice(), tradePairDto.getScale()));
        }
        if (TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus() == recordDO.getTradeStatus()) {
            respVO.setSettlePrice(DecimalFormatUtil.formatPlain(recordDO.getSettlePrice()));
            respVO.setProfit(DecimalFormatUtil.formatWithScale(recordDO.getProfitResult(), 2));
        } else if (TimeContractOrderStatusEnum.DRAW_FAIL.getStatus() == recordDO.getTradeStatus()) {
            // 结算失败
            respVO.setSettlePrice("0");
            respVO.setProfit("0");
        } else {
            // 未结算的限时合约订单根据当前交易对的实时价格和用户购买的多空方向自动计算输赢这里不控结果
            if (currentPriceDto != null && currentPriceDto.getCurrentPrice() != null) {
                respVO.setCurrentPrice(DecimalFormatUtil.formatWithScale(currentPriceDto.getCurrentPrice(), tradePairDto.getScale()));
                boolean isWin = isWinByRealPrice(recordDO, currentPriceDto.getCurrentPrice());
                final BigDecimal profit = calculateProfitAmount(recordDO, isWin);
                respVO.setProfit(DecimalFormatUtil.formatWithScale(profit, 2));
            } else {
                respVO.setProfit("0");
            }
            respVO.setSettlePrice("0");
        }
        return respVO;
    }

    @Override
    @Slave
    public PageResult<AppTradeTimeContractOrderDetailRespVO> appGetTimeContractOrderPage(AppTradeTimeContractPageReqVO reqVO, Long userId, Long tenantId) {
        Map<String, CurrentPriceRespDTO> currentPriceMap = candleDataApi.getAllCurrentPriceList();
        Map<String, TradePairRespDTO> tradePairMapCached = tradePairApi.getTradePairMapCached();

        LambdaQueryWrapperX<TimeContractRecordDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(TimeContractRecordDO::getUserId, userId);
        wrapper.eqIfPresent(TimeContractRecordDO::getTenantId, tenantId);
        if (reqVO.getType().equals(TimeContractOrderTypeEnum.POSITION.getType())) {
            wrapper.eq(TimeContractRecordDO::getTradeStatus, TimeContractOrderStatusEnum.WAIT_DRAW.getStatus());
        } else {
            List<Integer> statusList = Arrays.asList(
                    TimeContractOrderStatusEnum.DRAW_FAIL.getStatus(),
                    TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus(),
                    TimeContractOrderStatusEnum.DRAWING.getStatus()
            );
            wrapper.in(TimeContractRecordDO::getTradeStatus, statusList);
        }
        wrapper.orderByDesc(TimeContractRecordDO::getSendTime);
        PageResult<TimeContractRecordDO> pageResult = timeContractRecordMapper.selectPage(reqVO, wrapper);
        // 响应
        List<AppTradeTimeContractOrderDetailRespVO> respVOList = new ArrayList<>();
        for (TimeContractRecordDO recordDO : pageResult.getList()) {
            AppTradeTimeContractOrderDetailRespVO respVO = BeanUtils.toBean(recordDO, AppTradeTimeContractOrderDetailRespVO.class);

            TradePairRespDTO tradePairDto = tradePairMapCached.get(recordDO.getCode());
            // 因为自发币中可能设置错误导致这里无法通过code取的交易对信息所以这里跳过
            if (tradePairDto == null) {
                continue;
            }
            respVO.setAmount(DecimalFormatUtil.formatPlain(recordDO.getAmount()));
            respVO.setName(tradePairDto.getName());
            respVO.setOrderPrice(DecimalFormatUtil.formatPlain(recordDO.getOrderPrice()));
            respVO.setProfit(DecimalFormatUtil.formatWithScale(recordDO.getProfitResult(), 2));
            if (TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus() == recordDO.getTradeStatus()) {
                respVO.setCurrentPrice("0");
                respVO.setSettlePrice(DecimalFormatUtil.formatPlain(recordDO.getSettlePrice()));
            } else {
                // 当前价格
                CurrentPriceRespDTO priceDto = currentPriceMap.get(respVO.getCode());
                if (priceDto != null && priceDto.getCurrentPrice() != null) {
                    respVO.setCurrentPrice(DecimalFormatUtil.formatWithScale(priceDto.getCurrentPrice(), tradePairDto.getScale()));
                }
                respVO.setSettlePrice("0");
            }
            respVOList.add(respVO);
        }
        return new PageResult<>(respVOList, pageResult.getTotal(), pageResult.getPageNo(), pageResult.getTotalPage());
    }

    /**
     * 根据利润率计算盈亏利润
     *
     * @param recordDO 交易记录
     * @param isWin    输还是赢
     * @return 盈亏预估值
     */
    private static BigDecimal calculateProfitAmount(TimeContractRecordDO recordDO, boolean isWin) {
        // 亏的情况则按照收益率计算具体的亏损金额
        if (isWin) {
            BigDecimal winProfitRate = recordDO.getProfitRate().divide(BigDecimal.valueOf(100), RoundingMode.HALF_DOWN);
            return recordDO.getAmount().multiply(winProfitRate);
        } else {
            BigDecimal loseProfitRate = recordDO.getLoseProfitRate().divide(BigDecimal.valueOf(100), RoundingMode.HALF_DOWN);
            return recordDO.getAmount().multiply(loseProfitRate).multiply(BigDecimal.valueOf(-1));
        }
    }

    /**
     * 输赢结果
     *
     * @param recordDO     限时合约订单
     * @param currentPrice 当前价格
     * @return true:赢 false:输
     */
    private static boolean isWinByRealPrice(TimeContractRecordDO recordDO, BigDecimal currentPrice) {
        // priceCompare 小于0表示当前价格大于购买价格(币价涨了)，等于0表示当前价格和购买价格相等，大于0表示当前价格小于购买价格(币价跌了)
        int priceCompare = recordDO.getOrderPrice().compareTo(currentPrice);
        if (priceCompare == 0) {
            return false;
        } else {
            return (ShortLongEnum.SHORT.getType() == recordDO.getShortLong() && priceCompare > 0) ||
                    (ShortLongEnum.LONG.getType() == recordDO.getShortLong() && priceCompare < 0);
        }
    }

    @Override
    @Master
    @DSTransactional
    public void updateTimeContractRecord(TimeContractRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateTimeContractRecordExists(updateReqVO.getId());
        // 更新
        TimeContractRecordDO updateObj = BeanUtils.toBean(updateReqVO, TimeContractRecordDO.class);
        timeContractRecordMapper.updateById(updateObj);
    }

    @Override
    @Master
    @DSTransactional
    @LogRecord(type = TRADE_TIME_CONTRACT_TYPE, subType = TRADE_TIME_CONTRACT_DELETE_SUB_TYPE, bizNo = "{{#id}}", success = TRADE_TIME_CONTRACT_DELETE_SUCCESS)
    public void deleteTimeContractRecord(Long id) {
        // 校验存在
        TimeContractRecordDO recordDO = validateTimeContractRecordExists(id);
        // 删除
        timeContractRecordMapper.deleteById(id);
        // 记录操作日志上下文
        LogRecordContext.putVariable("record", recordDO);
    }

    private TimeContractRecordDO validateTimeContractRecordExists(Long id) {
        TimeContractRecordDO recordDO = timeContractRecordMapper.selectById(id);
        if (recordDO == null) {
            throw exception(TIME_CONTRACT_RECORD_NOT_EXISTS);
        }
        return recordDO;
    }

    @Override
    @Slave
    public TimeContractRecordDO getTimeContractRecord(Long id) {
        return timeContractRecordMapper.selectById(id);
    }

    @Override
    public List<TimeContractRecordDO> getTimeContractRecords(Long userId) {
        return getTimeContractRecordsByStatus(userId, null);
    }

    @Override
    @Slave
    public List<TimeContractRecordDO> getTimeContractRecordsByStatus(Long userId, Integer status) {
        return timeContractRecordMapper.selectList(userId, status);
    }

    @Override
    @Slave
    public List<TimeContractRecordDO> getTimeContractRecordsByOrderNo(Set<String> orderNoSet) {
        return timeContractRecordMapper.selectListByOrderNos(orderNoSet);
    }

    @Override
    public TimeContractRecordDO calculateTimeRecordResult(TimeContractRecordDO recordDO, BigDecimal currentPrice, int profitType) {
        // 如果状态不是开奖中则返回
        if (TimeContractOrderStatusEnum.DRAWING.getStatus() != recordDO.getTradeStatus()) {
            return recordDO;
        }
        long now = System.currentTimeMillis();
        if (profitType == MemberConfigProfitTypeEnum.RANDOM.getType()) {
            log.error("无法计算盈亏，不支持的输赢配置类型");
            // 暂时不支持这种配置
            recordDO.setTradeStatus(TimeContractOrderStatusEnum.DRAW_FAIL.getStatus());
            recordDO.setSettleTime(now);
            recordDO.setFailReason("盈利配置不支持");
            return recordDO;
        }
        BigDecimal profit;
        // 根据价格决定或者没有未设置都是按照实际价格来进行计算输赢
        if (profitType == MemberConfigProfitTypeEnum.REAL_PRICE.getType() || profitType == MemberConfigProfitTypeEnum.NONE.getType()) {
            boolean isWin = isWinByRealPrice(recordDO, currentPrice);
            profit = calculateProfitAmount(recordDO, isWin);
            recordDO.setSettlePrice(currentPrice);
        } else if (profitType == MemberConfigProfitTypeEnum.WIN_ALWAYS.getType()) {
            // 必赢
            double randomValue = getRandomAddValue();
            profit = calculateProfitAmount(recordDO, true);
            if (recordDO.getShortLong() == ShortLongEnum.SHORT.getType()) {
                recordDO.setSettlePrice(recordDO.getOrderPrice().subtract(BigDecimal.valueOf(randomValue)));
            } else {
                recordDO.setSettlePrice(recordDO.getOrderPrice().add(BigDecimal.valueOf(randomValue)));
            }
        } else if (profitType == MemberConfigProfitTypeEnum.TIE.getType()) {
            // 平局
            profit = BigDecimal.ZERO;
            recordDO.setSettlePrice(recordDO.getOrderPrice());
        } else {
            // 必输
            double randomValue = getRandomAddValue();
            profit = calculateProfitAmount(recordDO, false);
            if (recordDO.getShortLong() == ShortLongEnum.LONG.getType()) {
                recordDO.setSettlePrice(recordDO.getOrderPrice().subtract(BigDecimal.valueOf(randomValue)));
            } else {
                recordDO.setSettlePrice(recordDO.getOrderPrice().add(BigDecimal.valueOf(randomValue)));
            }
        }
        recordDO.setProfitResult(profit);
        recordDO.setSettleTime(now);
        recordDO.setTradeStatus(TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus());

        // 更新订单信息
        updateTimeContractRecordResult(recordDO);
        // 更新用户的账户余额和帐变记录
        updateUserBalanceAndTransaction(recordDO, profit, profitType);

        return recordDO;
    }

    @Override
    @Master
    @DSTransactional
    public void updateTimeContractRecordResult(TimeContractRecordDO recordDO) {
        if (recordDO == null) {
            return;
        }
        recordDO.setUpdater("SystemJob");
        timeContractRecordMapper.updateById(recordDO);
        // 如果计算失败则进入手动计算盈利的情况
        if (TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus() == recordDO.getTradeStatus() ||
                TimeContractOrderStatusEnum.DRAW_FAIL.getStatus() == recordDO.getTradeStatus()) {
            timeContractOrderRedisDAO.remove(recordDO.getTradeNo());
            timeContractOrderRedisDAO.removeDrawing(recordDO.getTradeNo());
        }
    }

    /**
     * 更新用户的账户余额和帐变记录
     *
     * @param recordDO            账变记录信息
     * @param profit              利润
     * @param calculateProfitType 计算利润的类型
     */
    @Master
    @DSTransactional
    public void updateUserBalanceAndTransaction(TimeContractRecordDO recordDO, BigDecimal profit, Integer calculateProfitType) {
        if (TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus() != recordDO.getTradeStatus()) {
            return;
        }
        log.info("更新限时订单的利润信息 userId:{} orderNo:{} profit:{}", recordDO.getUserId(), recordDO.getTradeNo(), profit);

        MemberUserRespDTO userDTO = memberUserApi.checkUserExists(recordDO.getUserId());
        boolean isProfitResultTie = profit.compareTo(BigDecimal.ZERO) == 0 && MemberConfigProfitTypeEnum.TIE.getType().equals(calculateProfitType);
        // 如果盈亏利润大于0表示赢，则需要增加用户的USDT余额
        if (profit.compareTo(BigDecimal.ZERO) > 0 || isProfitResultTie) {

            // 更新用户USDT余额的基础信息
            MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
            balanceReqVO.setUserId(recordDO.getUserId());
            balanceReqVO.setAmount(recordDO.getAmount().add(profit));
            balanceReqVO.setRelatedOrderNo(recordDO.getTradeNo());

            // 更新帐变的基础信息
            MemberTransactionSaveReqVO profitTransaction = new MemberTransactionSaveReqVO();
            profitTransaction.setBizOrderNo(recordDO.getTradeNo());
            profitTransaction.setTransactionType(MemberTransactionsTypeEnum.TIME_CONTRACT_WIN);
            profitTransaction.setRemark(MemberTransactionsTypeEnum.TIME_CONTRACT_WIN.getLabel());
            profitTransaction.setCreator("SystemJob");
            profitTransaction.setUpdater("SystemJob");

            MemberBalanceUpdateContext updateContext = new MemberBalanceUpdateContext(userDTO, balanceReqVO, profitTransaction);
            // 增加用户的账户USDT余额
            memberBalanceApi.incrUserBalance(updateContext);

        } else if (profit.compareTo(BigDecimal.ZERO) < 0) {
            // 返还给用户的金额, 如果配置了订单结果为输输掉所有下单金额则这里不需要返还用户余额
            BigDecimal refundAmount = recordDO.getAmount().subtract(profit.abs());
            if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 更新用户的账户USDT余额
                MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
                balanceReqVO.setUserId(recordDO.getUserId());
                balanceReqVO.setAmount(refundAmount);
                balanceReqVO.setRelatedOrderNo(recordDO.getTradeNo());

                // 更新帐变的基础信息
                MemberTransactionSaveReqVO refundTransaction = new MemberTransactionSaveReqVO();
                refundTransaction.setBizOrderNo(recordDO.getTradeNo());
                refundTransaction.setTransactionType(MemberTransactionsTypeEnum.CLOSE_TIME_CONTRACT);
                refundTransaction.setRemark(MemberTransactionsTypeEnum.CLOSE_TIME_CONTRACT.getLabel());
                refundTransaction.setCreator("SystemJob");
                refundTransaction.setUpdater("SystemJob");

                MemberBalanceUpdateContext updateContext = new MemberBalanceUpdateContext(userDTO, balanceReqVO, refundTransaction);
                memberBalanceApi.incrUserBalance(updateContext);
            }
        }
    }

    @Override
    @Slave
    public PageResult<TimeContractRecordDO> getTimeContractRecordPage(TimeContractRecordPageReqVO pageReqVO, Long tenantId) {
        final PageResult<TimeContractRecordDO> pageResult = timeContractRecordMapper.selectPage(pageReqVO);
        if (CollUtil.isNotEmpty(pageResult.getList())) {
            // 获取用户的风控类型
            final Set<Long> userIds = pageResult.getList().stream().map(TimeContractRecordDO::getUserId).collect(Collectors.toSet());
            final Map<Long, MemberUserConfigRespDTO> userConfigMap = memberUserApi.getUserConfigByIds(userIds);
            pageResult.getList().forEach(recordDO -> {
                if (userConfigMap.containsKey(recordDO.getUserId())) {
                    final MemberUserConfigRespDTO config = userConfigMap.get(recordDO.getUserId());
                    recordDO.setUserProfitType(config.getProfitType());
                }
            });
        }
        return pageResult;
    }

    @Override
    @Master
    @DSTransactional
    public void updateTimeContractRecordStatus(TimeContractRecordDO recordDO) {
        if (recordDO == null) {
            return;
        }
        final TimeContractRecordDO updateDO = new TimeContractRecordDO();
        updateDO.setId(recordDO.getId());
        updateDO.setTradeStatus(recordDO.getTradeStatus());
        updateDO.setUpdater("SystemJob");
        timeContractRecordMapper.updateById(updateDO);
        // 如果订单是开奖中的状态则从等待结算的订单中移除并添加到开奖中的订单列表中
        if (TimeContractOrderStatusEnum.DRAWING.getStatus() == recordDO.getTradeStatus()) {
            // 从等待的订单中移除
            timeContractOrderRedisDAO.remove(updateDO.getTradeNo());
            // 添加到开奖订单号中
            timeContractOrderRedisDAO.setDrawing(updateDO.getTradeNo());
        }
    }

    @Override
    @Master
    @LogRecord(type = TRADE_TIME_CONTRACT_TYPE, subType = TRADE_TIME_CONTRACT_MANUAL_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = TRADE_TIME_CONTRACT_MANUAL_SUCCESS)
    public TimeContractRecordRespVO manualDrawTimeContract(TimeContractRecordDrawReqVO reqVO) {
        // 校验订单是否存在
        TimeContractRecordDO recordDO = validateTimeContractRecordExists(reqVO.getId());
        // 已经结算成功的订单不处理
        if (TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus() == recordDO.getTradeStatus()) {
            return BeanUtil.toBean(recordDO, TimeContractRecordRespVO.class);
        }
        // 先修改状态否则无法更新利润到用户的余额
        recordDO.setTradeStatus(TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus());
        recordDO.setSettleTime(recordDO.getSendTime() + recordDO.getDuration());

        // 计算盈亏金额
        BigDecimal profit = calculateProfitAmount(recordDO, reqVO.getWinOrLose());

        double randomValue = getRandomAddValue();
        BigDecimal settlePrice = recordDO.getOrderPrice();
        // 手动结算为赢
        if (reqVO.getWinOrLose()) {
            if (ShortLongEnum.LONG.getType() == recordDO.getShortLong()) {
                settlePrice = settlePrice.add(BigDecimal.valueOf(randomValue));
            } else {
                settlePrice = settlePrice.subtract(BigDecimal.valueOf(randomValue));
            }
        } else {
            profit = profit.multiply(BigDecimal.valueOf(-1));

            if (ShortLongEnum.LONG.getType() == recordDO.getShortLong()) {
                settlePrice = settlePrice.subtract(BigDecimal.valueOf(randomValue));
            } else {
                settlePrice = settlePrice.add(BigDecimal.valueOf(randomValue));
            }
        }
        recordDO.setProfitResult(profit);
        recordDO.setSettlePrice(settlePrice);

        // 更新限时合约的信息
        updateTimeContractRecordResult(recordDO);
        // 更新用户的余额和帐变
        updateUserBalanceAndTransaction(recordDO, profit, null);

        // 记录操作日志
        LogRecordContext.putVariable("result", reqVO.getWinOrLose());
        LogRecordContext.putVariable("record", recordDO);

        return BeanUtil.toBean(recordDO, TimeContractRecordRespVO.class);
    }

    @Override
    @LogRecord(type = TRADE_TIME_CONTRACT_TYPE, subType = TRADE_TIME_CONTRACT_SETTLE_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = TRADE_TIME_CONTRACT_SETTLE_SUCCESS)
    public void settleOrderAgain(TimeContractRecordSettleReqVO reqVO) {
        LambdaQueryWrapperX<TimeContractRecordDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TimeContractRecordDO::getTradeNo, reqVO.getOrderNo()).last("LIMIT 1");
        TimeContractRecordDO recordDO = timeContractRecordMapper.selectOne(queryWrapperX);
        if (recordDO == null) {
            throw exception(TIME_CONTRACT_RECORD_NOT_EXISTS);
        }
        // 只有开奖成功或者开奖失败的订单才能手动结算
        boolean couldSettle = recordDO.getTradeStatus() == TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus();
        if (couldSettle) {
            // 查询账变记录中是否存在此订单的派奖
            final List<MemberTransactionDTO> transactionList = memberTransactionApi.getTransactionsByRelationOrderNo(reqVO.getOrderNo());
            boolean isSettled = false;
            if (CollUtil.isNotEmpty(transactionList)) {
                final List<MemberTransactionDTO> settledList = transactionList.stream().filter(t -> {
                    final Integer type = t.getType();
                    return Objects.equals(type, MemberTransactionsTypeEnum.TIME_CONTRACT_WIN.getValue()) ||
                            Objects.equals(type, MemberTransactionsTypeEnum.CLOSE_TIME_CONTRACT.getValue());
                }).toList();
                isSettled = CollUtil.isNotEmpty(settledList);
            }
            // 如果账变中没有此限时合约订单的结算记录
            if (!isSettled) {
                log.info("限时合约订单:{} 没有对应的结算账变信息, 手动派奖", reqVO.getOrderNo());
                // 更新用户的余额和帐变
                updateUserBalanceAndTransaction(recordDO, recordDO.getProfitResult(), null);
            } else {
                throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "错误:{}", "限时合约订单已经派奖, 无法再次派奖");
            }
        }
    }

    @Override
    public List<TimeContractRecordDO> getListByRange(Long startTime, Long endTime) {
        LambdaQueryWrapperX<TimeContractRecordDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.between(TimeContractRecordDO::getCreateTime, startTime, endTime)
                .eq(TimeContractRecordDO::getTradeStatus, TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus())
                .select(TimeContractRecordDO::getUserId, TimeContractRecordDO::getProfitResult);
        return timeContractRecordMapper.selectList(queryWrapperX);
    }

    @Override
    public List<TimeContractRecordDO> getListByRange(Long userId, Long startTime, Long endTime) {
        LambdaQueryWrapperX<TimeContractRecordDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.between(TimeContractRecordDO::getCreateTime, startTime, endTime).eq(TimeContractRecordDO::getUserId, userId)
                .eq(TimeContractRecordDO::getTradeStatus, TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus())
                .select(TimeContractRecordDO::getUserId, TimeContractRecordDO::getProfitResult);
        return timeContractRecordMapper.selectList(queryWrapperX);
    }

    /**
     * 随机一个交易对的价格增减值
     * 用于构造控赢场景时的结算价格
     *
     * @return 随机值
     */
    private double getRandomAddValue() {
        int random = RandomUtil.randomInt(60);
        return (double) random / 100;
    }

}