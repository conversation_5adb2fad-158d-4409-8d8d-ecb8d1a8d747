package com.rf.exchange.module.candle.dal.dataobject.sync;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fhs.core.trans.vo.TransPojo;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * K线数据同步记录 DO
 * 
 * 用于记录每个交易对的K线数据同步状态和最后同步时间
 * 支持多种数据源和时间周期的同步管理
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@TableName("data_candle_last_sync")
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CandleLastSyncDO extends BaseDO implements Serializable, TransPojo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 交易对代码，如BTCUSDT
     */
    private String tradePairCode;

    /**
     * 交易对ID，关联exchange_trade_pair表
     */
    private Long tradePairId;

    /**
     * 数据源类型
     * 0-polygon, 1-alltick, 2-自定义
     */
    private Integer dataSource;

    /**
     * 时间范围
     * 1-1分钟, 5-5分钟, 15-15分钟, 30-30分钟, 60-1小时, 240-4小时, 1440-1天
     */
    private Integer timeRange;

    /**
     * 时间范围类型
     * 1-分钟, 2-小时, 3-日, 4-月
     */
    private Integer timeType;

    /**
     * 最后同步的时间戳（秒）
     */
    private Long lastSyncTimestamp;

    /**
     * 最后同步时间（可读格式）
     */
    private LocalDateTime lastSyncTime;

    /**
     * 同步状态
     * 0-正常, 1-暂停, 2-错误, 3-完成
     */
    private Integer syncStatus;

    /**
     * 累计同步的K线数量
     */
    private Long syncCount;

    /**
     * 同步错误次数
     */
    private Integer errorCount;

    /**
     * 最后一次错误信息
     */
    private String lastErrorMsg;

    /**
     * 最后一次错误时间
     */
    private LocalDateTime lastErrorTime;

    /**
     * 下次计划同步时间
     */
    private LocalDateTime nextSyncTime;

    /**
     * 每批次同步数量
     */
    private Integer syncBatchSize;

    /**
     * 是否启用同步
     * true-启用, false-禁用
     */
    private Boolean isEnabled;

    /**
     * 租户ID
     */
    private Long tenantId;

    // ========== 枚举定义 ==========

    /**
     * 数据源类型枚举
     */
    public enum DataSourceEnum {
        POLYGON(0, "Polygon"),
        ALLTICK(1, "AllTick"),
        CUSTOM(2, "自定义");

        private final Integer code;
        private final String name;

        DataSourceEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatusEnum {
        NORMAL(0, "正常"),
        PAUSED(1, "暂停"),
        ERROR(2, "错误"),
        COMPLETED(3, "完成");

        private final Integer code;
        private final String name;

        SyncStatusEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 时间类型枚举
     */
    public enum TimeTypeEnum {
        MINUTE(1, "分钟"),
        HOUR(2, "小时"),
        DAY(3, "日"),
        MONTH(4, "月");

        private final Integer code;
        private final String name;

        TimeTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    // ========== 便利方法 ==========

    /**
     * 是否为正常同步状态
     */
    public boolean isNormalStatus() {
        return SyncStatusEnum.NORMAL.getCode().equals(this.syncStatus);
    }

    /**
     * 是否为错误状态
     */
    public boolean isErrorStatus() {
        return SyncStatusEnum.ERROR.getCode().equals(this.syncStatus);
    }

    /**
     * 是否启用同步
     */
    public boolean isSyncEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 获取同步间隔（秒）
     * 根据时间范围计算合理的同步间隔
     */
    public long getSyncIntervalSeconds() {
        if (timeRange == null) {
            return 60; // 默认1分钟
        }
        
        // 对于分钟级别的K线，同步间隔为时间范围的一半，最小30秒
        if (TimeTypeEnum.MINUTE.getCode().equals(timeType)) {
            return Math.max(30, timeRange * 60 / 2);
        }
        
        // 对于小时级别的K线，同步间隔为时间范围的一半，最小5分钟
        if (TimeTypeEnum.HOUR.getCode().equals(timeType)) {
            return Math.max(300, timeRange * 3600 / 2);
        }
        
        // 对于日级别的K线，同步间隔为1小时
        if (TimeTypeEnum.DAY.getCode().equals(timeType)) {
            return 3600;
        }
        
        // 对于月级别的K线，同步间隔为1天
        return 86400;
    }
}
