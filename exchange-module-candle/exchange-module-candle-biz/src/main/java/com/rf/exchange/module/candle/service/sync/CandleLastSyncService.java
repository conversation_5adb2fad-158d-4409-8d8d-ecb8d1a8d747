package com.rf.exchange.module.candle.service.sync;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncPageReqVO;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncRespVO;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncSaveReqVO;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncUpdateReqVO;
import com.rf.exchange.module.candle.dal.dataobject.sync.CandleLastSyncDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * K线数据同步记录 Service 接口
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
public interface CandleLastSyncService {

    /**
     * 创建K线数据同步记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCandleLastSync(CandleLastSyncSaveReqVO createReqVO);

    /**
     * 更新K线数据同步记录
     *
     * @param updateReqVO 更新信息
     */
    void updateCandleLastSync(CandleLastSyncUpdateReqVO updateReqVO);

    /**
     * 删除K线数据同步记录
     *
     * @param id 编号
     */
    void deleteCandleLastSync(Long id);

    /**
     * 获得K线数据同步记录
     *
     * @param id 编号
     * @return K线数据同步记录
     */
    CandleLastSyncDO getCandleLastSync(Long id);

    /**
     * 获得K线数据同步记录分页
     *
     * @param pageReqVO 分页查询
     * @return K线数据同步记录分页
     */
    PageResult<CandleLastSyncRespVO> getCandleLastSyncPage(CandleLastSyncPageReqVO pageReqVO);

    /**
     * 根据交易对代码和时间参数获取同步记录
     *
     * @param tradePairCode 交易对代码
     * @param timeRange     时间范围
     * @param timeType      时间类型
     * @return 同步记录
     */
    CandleLastSyncDO getCandleLastSyncByTradePairAndTime(String tradePairCode, Integer timeRange, Integer timeType);

    /**
     * 获取指定交易对的所有同步记录
     *
     * @param tradePairCode 交易对代码
     * @return 同步记录列表
     */
    List<CandleLastSyncDO> getCandleLastSyncByTradePairCode(String tradePairCode);

    /**
     * 获取需要同步的记录
     *
     * @return 需要同步的记录列表
     */
    List<CandleLastSyncDO> getNeedSyncRecords();

    /**
     * 获取错误状态的同步记录
     *
     * @param maxErrorCount 最大错误次数
     * @return 错误记录列表
     */
    List<CandleLastSyncDO> getErrorRecords(Integer maxErrorCount);

    /**
     * 更新同步成功状态
     *
     * @param id                记录ID
     * @param lastSyncTimestamp 最后同步时间戳
     * @param syncCount         同步数量增量
     * @param nextSyncTime      下次同步时间
     */
    void updateSyncSuccess(Long id, Long lastSyncTimestamp, Long syncCount, LocalDateTime nextSyncTime);

    /**
     * 更新同步错误状态
     *
     * @param id            记录ID
     * @param errorMsg      错误信息
     * @param nextSyncTime  下次同步时间
     */
    void updateSyncError(Long id, String errorMsg, LocalDateTime nextSyncTime);

    /**
     * 重置错误状态
     *
     * @param id 记录ID
     */
    void resetErrorStatus(Long id);

    /**
     * 批量更新启用状态
     *
     * @param tradePairCodes 交易对代码列表
     * @param isEnabled      是否启用
     */
    void batchUpdateEnabledStatus(List<String> tradePairCodes, Boolean isEnabled);

    /**
     * 初始化交易对同步记录
     *
     * @param tradePairCode 交易对代码
     * @param tradePairId   交易对ID
     * @param dataSource    数据源
     */
    void initTradePairSyncRecords(String tradePairCode, Long tradePairId, Integer dataSource);

    /**
     * 获取同步统计信息
     *
     * @return 统计信息
     */
    List<CandleLastSyncDO> getSyncStatistics();

    /**
     * 清理过期的错误记录
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredErrorRecords(int days);
}
