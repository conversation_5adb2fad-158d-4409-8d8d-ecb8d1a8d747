package com.rf.exchange.module.candle.controller.admin.sync.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - K线数据同步记录分页 Request VO
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Schema(description = "管理后台 - K线数据同步记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CandleLastSyncPageReqVO extends PageParam {

    @Schema(description = "交易对代码", example = "BTCUSDT")
    private String tradePairCode;

    @Schema(description = "交易对ID", example = "1")
    private Long tradePairId;

    @Schema(description = "数据源类型", example = "0")
    private Integer dataSource;

    @Schema(description = "时间范围", example = "1")
    private Integer timeRange;

    @Schema(description = "时间范围类型", example = "1")
    private Integer timeType;

    @Schema(description = "同步状态", example = "0")
    private Integer syncStatus;

    @Schema(description = "是否启用同步", example = "true")
    private Boolean isEnabled;

    @Schema(description = "最后同步时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastSyncTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "租户ID", example = "0")
    private Long tenantId;
}
