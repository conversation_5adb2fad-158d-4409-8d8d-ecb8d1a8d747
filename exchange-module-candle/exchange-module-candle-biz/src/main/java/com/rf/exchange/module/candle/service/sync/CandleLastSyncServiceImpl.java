package com.rf.exchange.module.candle.service.sync;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncPageReqVO;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncRespVO;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncSaveReqVO;
import com.rf.exchange.module.candle.controller.admin.sync.vo.CandleLastSyncUpdateReqVO;
import com.rf.exchange.module.candle.dal.dataobject.sync.CandleLastSyncDO;
import com.rf.exchange.module.candle.dal.mysql.sync.CandleLastSyncMapper;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.candle.enums.ErrorCodeConstants.CANDLE_LAST_SYNC_NOT_EXISTS;

/**
 * K线数据同步记录 Service 实现类
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Service
@Validated
public class CandleLastSyncServiceImpl implements CandleLastSyncService {

    @Resource
    private CandleLastSyncMapper candleLastSyncMapper;

    @Override
    @Master
    public Long createCandleLastSync(CandleLastSyncSaveReqVO createReqVO) {
        // 插入
        CandleLastSyncDO candleLastSync = BeanUtils.toBean(createReqVO, CandleLastSyncDO.class);
        candleLastSyncMapper.insert(candleLastSync);
        // 返回
        return candleLastSync.getId();
    }

    @Override
    @Master
    public void updateCandleLastSync(CandleLastSyncUpdateReqVO updateReqVO) {
        // 校验存在
        validateCandleLastSyncExists(updateReqVO.getId());
        // 更新
        CandleLastSyncDO updateObj = BeanUtils.toBean(updateReqVO, CandleLastSyncDO.class);
        candleLastSyncMapper.updateById(updateObj);
    }

    @Override
    @Master
    public void deleteCandleLastSync(Long id) {
        // 校验存在
        validateCandleLastSyncExists(id);
        // 删除
        candleLastSyncMapper.deleteById(id);
    }

    private void validateCandleLastSyncExists(Long id) {
        if (candleLastSyncMapper.selectById(id) == null) {
            throw exception(CANDLE_LAST_SYNC_NOT_EXISTS);
        }
    }

    @Override
    @Slave
    public CandleLastSyncDO getCandleLastSync(Long id) {
        return candleLastSyncMapper.selectById(id);
    }

    @Override
    @Slave
    public PageResult<CandleLastSyncRespVO> getCandleLastSyncPage(CandleLastSyncPageReqVO pageReqVO) {
        PageResult<CandleLastSyncDO> pageResult = candleLastSyncMapper.selectPage(pageReqVO);
        return BeanUtils.toBean(pageResult, CandleLastSyncRespVO.class);
    }

    @Override
    @Slave
    public CandleLastSyncDO getCandleLastSyncByTradePairAndTime(String tradePairCode, Integer timeRange, Integer timeType) {
        return candleLastSyncMapper.selectByTradePairAndTime(tradePairCode, timeRange, timeType);
    }

    @Override
    @Slave
    public List<CandleLastSyncDO> getCandleLastSyncByTradePairCode(String tradePairCode) {
        return candleLastSyncMapper.selectByTradePairCode(tradePairCode);
    }

    @Override
    @Slave
    public List<CandleLastSyncDO> getNeedSyncRecords() {
        return candleLastSyncMapper.selectNeedSyncRecords(LocalDateTime.now());
    }

    @Override
    @Slave
    public List<CandleLastSyncDO> getErrorRecords(Integer maxErrorCount) {
        return candleLastSyncMapper.selectErrorRecords(maxErrorCount);
    }

    @Override
    @Master
    public void updateSyncSuccess(Long id, Long lastSyncTimestamp, Long syncCount, LocalDateTime nextSyncTime) {
        String updater = SecurityFrameworkUtils.getLoginUserNickname();
        if (updater == null) {
            updater = "system";
        }
        candleLastSyncMapper.updateSyncSuccess(id, lastSyncTimestamp, LocalDateTime.now(), syncCount, nextSyncTime, updater);
    }

    @Override
    @Master
    public void updateSyncError(Long id, String errorMsg, LocalDateTime nextSyncTime) {
        String updater = SecurityFrameworkUtils.getLoginUserNickname();
        if (updater == null) {
            updater = "system";
        }
        candleLastSyncMapper.updateSyncError(id, errorMsg, LocalDateTime.now(), nextSyncTime, updater);
    }

    @Override
    @Master
    public void resetErrorStatus(Long id) {
        String updater = SecurityFrameworkUtils.getLoginUserNickname();
        if (updater == null) {
            updater = "system";
        }
        candleLastSyncMapper.resetErrorStatus(id, updater);
    }

    @Override
    @Master
    public void batchUpdateEnabledStatus(List<String> tradePairCodes, Boolean isEnabled) {
        if (CollUtil.isEmpty(tradePairCodes)) {
            return;
        }
        String updater = SecurityFrameworkUtils.getLoginUserNickname();
        if (updater == null) {
            updater = "system";
        }
        candleLastSyncMapper.batchUpdateEnabledStatus(tradePairCodes, isEnabled, updater);
    }

    @Override
    @Master
    public void initTradePairSyncRecords(String tradePairCode, Long tradePairId, Integer dataSource) {
        // 为所有支持的时间周期创建同步记录
        List<CandleLastSyncDO> syncRecords = new ArrayList<>();
        
        // 分钟级别
        for (CandleTimeRangeEnum timeRange : CandleTimeRangeEnum.values()) {
            if (timeRange.getType().getType() == 1) { // 分钟类型
                CandleLastSyncDO syncRecord = CandleLastSyncDO.builder()
                        .tradePairCode(tradePairCode)
                        .tradePairId(tradePairId)
                        .dataSource(dataSource)
                        .timeRange(timeRange.getRange())
                        .timeType(timeRange.getType().getType())
                        .lastSyncTimestamp(0L)
                        .lastSyncTime(LocalDateTime.now().minusDays(1))
                        .syncStatus(CandleLastSyncDO.SyncStatusEnum.NORMAL.getCode())
                        .syncCount(0L)
                        .errorCount(0)
                        .syncBatchSize(1000)
                        .isEnabled(true)
                        .tenantId(0L)
                        .build();
                syncRecords.add(syncRecord);
            }
        }
        
        // 批量插入
        for (CandleLastSyncDO record : syncRecords) {
            try {
                candleLastSyncMapper.insert(record);
            } catch (Exception e) {
                log.warn("初始化同步记录失败: tradePairCode={}, timeRange={}, error={}", 
                    tradePairCode, record.getTimeRange(), e.getMessage());
            }
        }
    }

    @Override
    @Slave
    public List<CandleLastSyncDO> getSyncStatistics() {
        return candleLastSyncMapper.selectSyncStatistics();
    }

    @Override
    @Master
    public int cleanExpiredErrorRecords(int days) {
        // 这里可以实现清理逻辑，比如清理过期的错误信息
        // 暂时返回0，具体实现可以根据需求添加
        return 0;
    }
}
