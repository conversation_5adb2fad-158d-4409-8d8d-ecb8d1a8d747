package com.rf.exchange.module.candle.dal.mysql.sync;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.dal.dataobject.sync.CandleLastSyncDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * K线数据同步记录 Mapper
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Mapper
public interface CandleLastSyncMapper extends BaseMapperX<CandleLastSyncDO> {

    /**
     * 根据交易对代码和时间参数查询同步记录
     *
     * @param tradePairCode 交易对代码
     * @param timeRange     时间范围
     * @param timeType      时间类型
     * @return 同步记录
     */
    default CandleLastSyncDO selectByTradePairAndTime(String tradePairCode, Integer timeRange, Integer timeType) {
        return selectOne(new LambdaQueryWrapperX<CandleLastSyncDO>()
                .eq(CandleLastSyncDO::getTradePairCode, tradePairCode)
                .eq(CandleLastSyncDO::getTimeRange, timeRange)
                .eq(CandleLastSyncDO::getTimeType, timeType)
                .eq(CandleLastSyncDO::getDeleted, false));
    }

    /**
     * 查询指定交易对的所有同步记录
     *
     * @param tradePairCode 交易对代码
     * @return 同步记录列表
     */
    default List<CandleLastSyncDO> selectByTradePairCode(String tradePairCode) {
        return selectList(new LambdaQueryWrapperX<CandleLastSyncDO>()
                .eq(CandleLastSyncDO::getTradePairCode, tradePairCode)
                .eq(CandleLastSyncDO::getDeleted, false)
                .orderByAsc(CandleLastSyncDO::getTimeRange));
    }

    /**
     * 查询需要同步的记录
     * 条件：启用状态、正常状态、到达同步时间
     *
     * @param currentTime 当前时间
     * @return 需要同步的记录列表
     */
    default List<CandleLastSyncDO> selectNeedSyncRecords(LocalDateTime currentTime) {
        return selectList(new LambdaQueryWrapperX<CandleLastSyncDO>()
                .eq(CandleLastSyncDO::getIsEnabled, true)
                .eq(CandleLastSyncDO::getSyncStatus, CandleLastSyncDO.SyncStatusEnum.NORMAL.getCode())
                .eq(CandleLastSyncDO::getDeleted, false)
                .and(wrapper -> wrapper
                        .isNull(CandleLastSyncDO::getNextSyncTime)
                        .or()
                        .le(CandleLastSyncDO::getNextSyncTime, currentTime))
                .orderByAsc(CandleLastSyncDO::getLastSyncTimestamp));
    }

    /**
     * 查询错误状态的同步记录
     *
     * @param maxErrorCount 最大错误次数
     * @return 错误记录列表
     */
    default List<CandleLastSyncDO> selectErrorRecords(Integer maxErrorCount) {
        return selectList(new LambdaQueryWrapperX<CandleLastSyncDO>()
                .eq(CandleLastSyncDO::getSyncStatus, CandleLastSyncDO.SyncStatusEnum.ERROR.getCode())
                .le(CandleLastSyncDO::getErrorCount, maxErrorCount)
                .eq(CandleLastSyncDO::getDeleted, false)
                .orderByAsc(CandleLastSyncDO::getLastErrorTime));
    }

    /**
     * 查询指定数据源的同步记录
     *
     * @param dataSource 数据源类型
     * @return 同步记录列表
     */
    default List<CandleLastSyncDO> selectByDataSource(Integer dataSource) {
        return selectList(new LambdaQueryWrapperX<CandleLastSyncDO>()
                .eq(CandleLastSyncDO::getDataSource, dataSource)
                .eq(CandleLastSyncDO::getDeleted, false)
                .orderByAsc(CandleLastSyncDO::getTradePairCode));
    }

    /**
     * 统计同步记录数量
     *
     * @param syncStatus 同步状态
     * @param isEnabled  是否启用
     * @return 记录数量
     */
    default Long countBySyncStatus(Integer syncStatus, Boolean isEnabled) {
        return selectCount(new LambdaQueryWrapperX<CandleLastSyncDO>()
                .eqIfPresent(CandleLastSyncDO::getSyncStatus, syncStatus)
                .eqIfPresent(CandleLastSyncDO::getIsEnabled, isEnabled)
                .eq(CandleLastSyncDO::getDeleted, false));
    }

    /**
     * 更新同步成功记录
     *
     * @param id                  记录ID
     * @param lastSyncTimestamp   最后同步时间戳
     * @param lastSyncTime        最后同步时间
     * @param syncCount           同步数量增量
     * @param nextSyncTime        下次同步时间
     * @param updater             更新者
     * @return 更新行数
     */
    int updateSyncSuccess(@Param("id") Long id,
                         @Param("lastSyncTimestamp") Long lastSyncTimestamp,
                         @Param("lastSyncTime") LocalDateTime lastSyncTime,
                         @Param("syncCount") Long syncCount,
                         @Param("nextSyncTime") LocalDateTime nextSyncTime,
                         @Param("updater") String updater);

    /**
     * 更新同步错误记录
     *
     * @param id             记录ID
     * @param errorMsg       错误信息
     * @param lastErrorTime  错误时间
     * @param nextSyncTime   下次同步时间
     * @param updater        更新者
     * @return 更新行数
     */
    int updateSyncError(@Param("id") Long id,
                       @Param("errorMsg") String errorMsg,
                       @Param("lastErrorTime") LocalDateTime lastErrorTime,
                       @Param("nextSyncTime") LocalDateTime nextSyncTime,
                       @Param("updater") String updater);

    /**
     * 重置错误状态为正常
     *
     * @param id      记录ID
     * @param updater 更新者
     * @return 更新行数
     */
    int resetErrorStatus(@Param("id") Long id, @Param("updater") String updater);

    /**
     * 批量更新启用状态
     *
     * @param tradePairCodes 交易对代码列表
     * @param isEnabled      是否启用
     * @param updater        更新者
     * @return 更新行数
     */
    int batchUpdateEnabledStatus(@Param("tradePairCodes") List<String> tradePairCodes,
                                @Param("isEnabled") Boolean isEnabled,
                                @Param("updater") String updater);

    /**
     * 查询同步统计信息
     *
     * @return 统计信息
     */
    List<CandleLastSyncDO> selectSyncStatistics();
}
