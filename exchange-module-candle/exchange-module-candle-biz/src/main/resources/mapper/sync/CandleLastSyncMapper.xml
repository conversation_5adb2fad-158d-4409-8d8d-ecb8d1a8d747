<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.candle.dal.mysql.sync.CandleLastSyncMapper">

    <!-- 更新同步成功记录 -->
    <update id="updateSyncSuccess">
        UPDATE data_candle_last_sync 
        SET 
            last_sync_timestamp = #{lastSyncTimestamp},
            last_sync_time = #{lastSyncTime},
            sync_count = sync_count + #{syncCount},
            sync_status = 0,
            next_sync_time = #{nextSyncTime},
            update_time = UNIX_TIMESTAMP(NOW()),
            updater = #{updater}
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 更新同步错误记录 -->
    <update id="updateSyncError">
        UPDATE data_candle_last_sync 
        SET 
            sync_status = 2,
            error_count = error_count + 1,
            last_error_msg = #{errorMsg},
            last_error_time = #{lastErrorTime},
            next_sync_time = #{nextSyncTime},
            update_time = UNIX_TIMESTAMP(NOW()),
            updater = #{updater}
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 重置错误状态为正常 -->
    <update id="resetErrorStatus">
        UPDATE data_candle_last_sync 
        SET 
            sync_status = 0,
            error_count = 0,
            last_error_msg = NULL,
            last_error_time = NULL,
            update_time = UNIX_TIMESTAMP(NOW()),
            updater = #{updater}
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 批量更新启用状态 -->
    <update id="batchUpdateEnabledStatus">
        UPDATE data_candle_last_sync 
        SET 
            is_enabled = #{isEnabled},
            update_time = UNIX_TIMESTAMP(NOW()),
            updater = #{updater}
        WHERE trade_pair_code IN 
        <foreach collection="tradePairCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 查询同步统计信息 -->
    <select id="selectSyncStatistics" resultType="com.rf.exchange.module.candle.dal.dataobject.sync.CandleLastSyncDO">
        SELECT 
            trade_pair_code,
            data_source,
            COUNT(*) as sync_count,
            SUM(CASE WHEN sync_status = 0 THEN 1 ELSE 0 END) as normal_count,
            SUM(CASE WHEN sync_status = 1 THEN 1 ELSE 0 END) as paused_count,
            SUM(CASE WHEN sync_status = 2 THEN 1 ELSE 0 END) as error_count,
            SUM(CASE WHEN sync_status = 3 THEN 1 ELSE 0 END) as completed_count,
            MIN(last_sync_timestamp) as min_sync_timestamp,
            MAX(last_sync_timestamp) as max_sync_timestamp,
            AVG(error_count) as avg_error_count
        FROM data_candle_last_sync 
        WHERE deleted = 0 
        GROUP BY trade_pair_code, data_source
        ORDER BY trade_pair_code, data_source
    </select>

    <!-- 通用查询条件 -->
    <sql id="baseColumns">
        id, trade_pair_code, trade_pair_id, data_source, time_range, time_type,
        last_sync_timestamp, last_sync_time, sync_status, sync_count, error_count,
        last_error_msg, last_error_time, next_sync_time, sync_batch_size, is_enabled,
        tenant_id, create_time, update_time, creator, updater, deleted
    </sql>

    <!-- 基础查询条件 -->
    <sql id="baseWhere">
        <where>
            deleted = 0
            <if test="tradePairCode != null and tradePairCode != ''">
                AND trade_pair_code = #{tradePairCode}
            </if>
            <if test="tradePairId != null">
                AND trade_pair_id = #{tradePairId}
            </if>
            <if test="dataSource != null">
                AND data_source = #{dataSource}
            </if>
            <if test="timeRange != null">
                AND time_range = #{timeRange}
            </if>
            <if test="timeType != null">
                AND time_type = #{timeType}
            </if>
            <if test="syncStatus != null">
                AND sync_status = #{syncStatus}
            </if>
            <if test="isEnabled != null">
                AND is_enabled = #{isEnabled}
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>

    <!-- 查询最近同步记录 -->
    <select id="selectRecentSyncRecords" resultType="com.rf.exchange.module.candle.dal.dataobject.sync.CandleLastSyncDO">
        SELECT <include refid="baseColumns"/>
        FROM data_candle_last_sync
        WHERE deleted = 0 
          AND last_sync_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        ORDER BY last_sync_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询长时间未同步的记录 -->
    <select id="selectLongTimeNoSyncRecords" resultType="com.rf.exchange.module.candle.dal.dataobject.sync.CandleLastSyncDO">
        SELECT <include refid="baseColumns"/>
        FROM data_candle_last_sync
        WHERE deleted = 0 
          AND is_enabled = 1
          AND sync_status = 0
          AND last_sync_time &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        ORDER BY last_sync_time ASC
        LIMIT #{limit}
    </select>

    <!-- 查询高错误率的记录 -->
    <select id="selectHighErrorRateRecords" resultType="com.rf.exchange.module.candle.dal.dataobject.sync.CandleLastSyncDO">
        SELECT <include refid="baseColumns"/>
        FROM data_candle_last_sync
        WHERE deleted = 0 
          AND error_count >= #{minErrorCount}
          AND sync_count > 0
        ORDER BY (error_count * 1.0 / sync_count) DESC, error_count DESC
        LIMIT #{limit}
    </select>

</mapper>
