package com.rf.exchange.module.candle.api.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 控盘计划生成的价格点
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@Builder
public class ControlPricePointDTO {
    /**
     * 生成价格的基准价格
     */
    private BigDecimal referPrice;
    /**
     * 价格差值(有正负)
     */
    private BigDecimal priceDiff;
    /**
     * 成交量
     */
    private BigDecimal volume;
    /**
     * 时间戳
     */
    private Long timestamp;
}
