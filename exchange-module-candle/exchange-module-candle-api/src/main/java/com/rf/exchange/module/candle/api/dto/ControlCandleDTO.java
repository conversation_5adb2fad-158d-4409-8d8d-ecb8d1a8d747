package com.rf.exchange.module.candle.api.dto;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ControlCandleDTO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 时间范围
     */
    private Integer timeRange;
    /**
     * 时间范围类型 1分钟 2小时 3日 4月
     */
    private Integer timeType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 交易对编码
     */
    private String code;
    /**
     * 最高价格
     */
    private BigDecimal highPrice;
    /**
     * 最低价格
     */
    private BigDecimal lowPrice;
    /**
     * 开盘价格
     */
    private BigDecimal openPrice;
    /**
     * 收盘价格
     */
    private BigDecimal closePrice;
    /**
     * 成交量
     */
    private BigDecimal volume;
    /**
     * 成交额
     */
    private BigDecimal turnover;
    /**
     * 是否是休市数据
     */
    private Boolean isMarketClose;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
