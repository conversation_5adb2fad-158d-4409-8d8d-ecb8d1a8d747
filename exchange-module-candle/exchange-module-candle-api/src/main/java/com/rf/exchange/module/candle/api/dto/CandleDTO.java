package com.rf.exchange.module.candle.api.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-07-06
 */
@Data
public class CandleDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 时间范围
     */
    private Integer timeRange;
    /**
     * 时间范围类型
     */
    private Integer timeType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    /**
     * 开盘价格
     */
    private BigDecimal openPrice;
    /**
     * 收盘价格
     */
    private BigDecimal closePrice;
    /**
     * 交易量
     */
    private BigDecimal volume;
    /**
     * 成交金额
     */
    private BigDecimal turnover;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 是否是休市数据
     */
    private Boolean isMarketClose;
}
