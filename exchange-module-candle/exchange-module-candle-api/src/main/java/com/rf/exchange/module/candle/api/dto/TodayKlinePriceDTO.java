package com.rf.exchange.module.candle.api.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
public class TodayKlinePriceDTO {
    /**
     * 系统交易对代码
     */
    private String tradePairCode;
    /**
     * 该K线时间戳
     */
    private String timestamp;
    /**
     * 该K线开盘价
     */
    private String openPrice;
    /**
     * 该K线收盘价
     */
    private String closePrice;
    /**
     * 该K线最高价
     */
    private String highPrice;
    /**
     * 该K线最低价
     */
    private String lowPrice;
    /**
     * 该K线成交数量(从0点开始非24小时)
     */
    private String volume;
    /**
     * 该K线成交金额
     */
    private String turnover;
    /**
     * 24小时成交量
     */
    private String volume24H;
}
