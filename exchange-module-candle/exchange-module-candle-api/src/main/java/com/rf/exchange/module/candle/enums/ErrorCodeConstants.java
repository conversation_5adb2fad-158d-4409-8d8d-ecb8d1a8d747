package com.rf.exchange.module.candle.enums;

import com.rf.exchange.framework.common.exception.ErrorCode;

/**
 * k线模块的错误码
 * <p>
 * 错误码(1-015-000-000 ~ 1-016-000-000)
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
public interface ErrorCodeConstants {
    ErrorCode CANDLE_TABLE_NAME_NOT_AVAILABLE = new ErrorCode(1_015_000_000, "k线表名无效", "CANDLE_TABLE_NAME_NOT_AVAILABLE");
    ErrorCode CANDLE_BAR_VALUE_INVALID = new ErrorCode(1_015_001_000, "bar参数无效", "CANDLE_BAR_VALUE_ERROR");
    ErrorCode CANDLE_PRICE_ERROR = new ErrorCode(1_015_001_001, "获取价格错误", "CANDLE_PRICE_ERROR");

    ErrorCode NOT_CUSTOM_TRADE_PAIR = new ErrorCode(1_015_002_000, "还没有自发币", "还没有自发币");
    ErrorCode CANDLE_NOT_GENERATE = new ErrorCode(1_015_002_001, "您选择的开始时间还未生成k线，请选择未来一天以内的时间", "您选择的开始时间还未生成k线，请选择未来一天以内的时间");

    ErrorCode CANDLE_NOT_PREVIEW = new ErrorCode(1_015_003_000, "您还没有预览生成k线，请先预览保存", "您还没有预览生成k线，请先预览保存");

    ErrorCode CANDLE_MIN_START_ERROR = new ErrorCode(1_015_004_000, "控盘开始时间必须在2分钟后", "控盘开始时间必须在2分钟后");

    // ========== 控盘计划的时间节点
    ErrorCode CONTROL_PRICE_POINT_NOT_EXISTS = new ErrorCode(1_015_005_000, "控盘计划的价格节点不存在", "CONTROL_PRICE_POINT_NOT_EXISTS");
    ErrorCode CONTROL_PLAN_NOT_EXISTS = new ErrorCode(1_015_005_001, "k线控盘计划不存在", "CONTROL_PLAN_NOT_EXISTS");
    ErrorCode CONTROL_PLAN_EXISTS = new ErrorCode(1_015_005_002, "k线控盘计划已经存在", "k线控盘计划已经存在");
    ErrorCode CONTROL_PLAN_TIME_CONFLICT = new ErrorCode(1_015_005_003, "控盘时间和存在控盘计划冲突", "控盘时间和存在控盘计划冲突");
    ErrorCode CONTROL_PLAN_START_TIME_AFTER_NOW = new ErrorCode(1_015_005_004, "控盘起始时间必须要在当前时间之后", "控盘起始时间必须要在当前时间之后");
    ErrorCode CONTROL_PLAN_CROSS_DAY = new ErrorCode(1_015_005_005, "控盘时间不能跨天", "控盘时间不能跨天");
    ErrorCode CONTROL_PLAN_CANNOT_STOP = new ErrorCode(1_015_005_006, "运行中的计划不能停止", "运行中的计划不能停止");
    ErrorCode CONTROL_PLAN_CANNOT_DELETE = new ErrorCode(1_015_005_007, "非等待和结束的计划不能删除", "非等待和结束的计划不能删除");
    ErrorCode CONTROL_PLAN_EXPIRE = new ErrorCode(1_015_005_008, "控盘计划已经过期了", "控盘计划已经过期了");

    ErrorCode CONTROL_PLAN_KLINE_NOT_EXISTS = new ErrorCode(1_015_006_000, "控盘计划的k线不存在", "控盘计划的k线不存在");
    ErrorCode CONTROL_CANDLE_NOT_EXISTS = new ErrorCode(1_015_006_001, "控盘k线不存在", "CONTROL_CANDLE_NOT_EXISTS");
}
