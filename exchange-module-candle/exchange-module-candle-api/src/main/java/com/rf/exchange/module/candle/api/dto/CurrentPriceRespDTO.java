package com.rf.exchange.module.candle.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
@Data
public class CurrentPriceRespDTO {
    /**
     * 交易对id
     */
    private String tradePairCode;
    /**
     * 当前价格
     */
    private BigDecimal currentPrice;
    /**
     * 交易量
     */
    private BigDecimal volume;
    /**
     * 成交额
     */
    private BigDecimal turnover;
    /**
     * 更新时间
     * 本系统处理的时间
     */
    private Long timestamp;
    /**
     * tick时间
     * 三方数据源更新的时间
     */
    private Long tickTime;
    /**
     * 涨跌幅百分比
     */
    private BigDecimal percentage = BigDecimal.valueOf(0, 2);
    /**
     * 是否已经休市
     */
    private Boolean isMarketClose = false;
//    /**
//     * 累计生成的成交额，在自发币生成价格变动时，要记录之前已经生成了多少成交额
//     */
//    private float totalTurnover;
}
