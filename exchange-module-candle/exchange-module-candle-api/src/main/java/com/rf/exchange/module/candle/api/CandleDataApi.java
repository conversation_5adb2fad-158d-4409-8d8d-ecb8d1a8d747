package com.rf.exchange.module.candle.api;

import com.rf.exchange.module.candle.api.dto.CandleDTO;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * k线数据 API
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
public interface CandleDataApi {

    /**
     * 获取最后一条k线的数据
     *
     * @param code          交易对代码
     * @param timeRangeEnum 时间范围
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return k线数据
     */
    CandleDTO getLastCandle(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime);

    /**
     * 获取k线记录条数
     *
     * @param code          交易对代码
     * @param timeRangeEnum k线的时间范围
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 记录条数
     */
    long getCandleRecordCount(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime);

    /**
     * 获取当前的交易对的价格Map
     */
    Map<String, CurrentPriceRespDTO> getAllCurrentPriceList();

    /**
     * 获取指定交易对代码集合的当前价格
     *
     * @param codeSet 交易对id集合
     * @return 当前价格map
     */
    Map<String, CurrentPriceRespDTO> getCurrentPriceListByIdSet(Set<String> codeSet);

    /**
     * 获取指定交易对code集合的今日k线
     * @param codeSet 交易对代码集合
     * @return k线map
     */
    Map<String, TodayKlinePriceDTO> getTodayKlinePriceListByIdSet(Set<String> codeSet);

    /**
     * 获取指定交易对的当前价格信息
     * @param code 交易对代码
     * @return 价格信息
     */
    CurrentPriceRespDTO getCurrentPrice(String code);

    /**
     * 创建交易对的表
     *
     * @param code 交易对代码
     */
    void createCandleTable(String code);

    /**
     * 批量创建交易对的k线表
     */
    void createAllCandleTableIfNeed();

    /**
     * 批量插入k线数据
     *
     * @param code 交易对代码
     * @param candles k线列表
     */
    void batchInsertCandles(String code, List<CandleDTO> candles);


    void batchInsertCandlesNoUpdate(String code, List<CandleDTO> candles, boolean isCopyTrade);

}
