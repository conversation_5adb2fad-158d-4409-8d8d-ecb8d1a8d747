package com.rf.exchange.module.candle.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-17
 */
@Getter
@AllArgsConstructor
public enum CandleTimeRangeEnum {

    MIN_ONE(1, CandleTimeTypeEnum.MINUTES, "1分钟", 1440, 1, 60),
    //MIN_THREE(3, CandleTimeTypeEnum.MINUTES, "3分钟", 480),
    MIN_FIVE(5, CandleTimeTypeEnum.MINUTES, "5分钟", 288, 2, 300),
    MIN_FIFTEEN(15, CandleTimeTypeEnum.MINUTES, "15分钟", 96, 3, 900),
    MIN_THIRTY(30, CandleTimeTypeEnum.MINUTES, "30分钟", 48, 4, 1800),
    HOUR_ONE(1, CandleTimeTypeEnum.HOURS, "1小时", 24, 5, 3600),
    HOUR_TWO(2, CandleTimeTypeEnum.HOURS, "2小时", 12, 6, 7200),
    HOUR_FOUR(4, CandleTimeTypeEnum.HOURS, "4小时", 6, 7, 14400),
    //HOUR_SIX(6, CandleTimeTypeEnum.HOURS, "6小时", 4),
    //HOUR_TWELVE(12, CandleTimeTypeEnum.HOURS, "12小时", 2),
    DAY_ONE(1, CandleTimeTypeEnum.DAYS, "1日", 1, 8, 86400),
    //DAY_TWO(2, CandleTimeTypeEnum.DAYS, "2日", 0),
    //DAY_THREE(3, CandleTimeTypeEnum.DAYS, "3日", 0),
    //DAY_FIVE(5, CandleTimeTypeEnum.DAYS, "5日", 0),
    WEEK_ONE(1, CandleTimeTypeEnum.WEEKS, "1周", 1, 9, 604800),
    MONTH_ONE(1, CandleTimeTypeEnum.MONTH, "1月", 1, 10, 108000),
    ;

    private final Integer range;
    private final CandleTimeTypeEnum type;
    private final String name;
    // 一天内的k线条数
    private final int candleCountMax;
    // k线类型
    private final Integer kLineTypeAllTick;
    private final long secs;

    public boolean isMinutes() {
        return getType().equals(CandleTimeTypeEnum.MINUTES);
    }

    public boolean isHours() {
        return getType().equals(CandleTimeTypeEnum.HOURS);
    }

    public boolean isDays() {
        return getType().equals(CandleTimeTypeEnum.DAYS);
    }

    public boolean isMonth() {
        return getType().equals(CandleTimeTypeEnum.MONTH);
    }

    public String getTypeUnit() {
        return type.getUnit();
    }

    public Integer getTypeValue() {
        return type.getType();
    }

    public String getSpanName() {
        return type.getPolygonTimeSpan();
    }

    public static CandleTimeRangeEnum enumOfAllTickLineType(Integer lineType) {
        for (CandleTimeRangeEnum anEnum : values()) {
            if (anEnum.getKLineTypeAllTick().equals(lineType)) {
                return anEnum;
            }
        }
        return null;
    }

    public static CandleTimeRangeEnum enumOf(String value) {
        if (StrUtil.isEmpty(value)) {
            return null;
        }
        char unitChar = value.charAt(value.length() - 1);
        final CandleTimeTypeEnum typeEnum = CandleTimeTypeEnum.enumOf(String.valueOf(unitChar));
        if (typeEnum == null) {
            return null;
        }
        try {
            final String rangeStr = value.substring(0, value.length() - 1);
            int range = Integer.parseInt(rangeStr);
            for (CandleTimeRangeEnum anEnum : values()) {
                if (anEnum.getRange() == range && anEnum.getTypeValue() == typeEnum.getType()) {
                    return anEnum;
                }
            }
        } catch (NumberFormatException ignored) {
        }
        return null;
    }

    /**
     * 获取这个级别的时间包含几条一分钟的
     *
     * @param type
     * @return
     */
    public static int getDurationMinutes(CandleTimeRangeEnum type) {
        switch (type) {
            case MIN_FIVE:
            case MIN_FIFTEEN:
            case MIN_THIRTY: {
                return type.getRange();
            }
            case HOUR_ONE:
            case HOUR_TWO:
            case HOUR_FOUR: {
                return type.getRange() * 60;
            }
            case DAY_ONE: {
                return type.getRange() * 24 * 60;
            }
            case WEEK_ONE: {
                return type.getRange() * 7 * 24 * 60;
            }
        }
        return 0;
    }

    /**
     * 获取结束时间，因为k线的时间点的时间范围是向后包含，从开始时间往后算结束时间，单位毫秒
     *
     * @param type
     * @param startTime
     * @return
     */
    public static long getEndTs(CandleTimeRangeEnum type, long startTime) {
        int minutes = getDurationMinutes(type);
        return startTime + (minutes * 60);
    }
}
