package com.rf.exchange.module.candle.api;

import com.rf.exchange.module.candle.api.dto.ControlCandleDTO;
import com.rf.exchange.module.candle.api.dto.ControlPlanDTO;
import com.rf.exchange.module.candle.api.dto.ControlPlanKLineDTO;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 控盘计划 API
 *
 * <AUTHOR>
 * @since 2024-11-02
 */
public interface ControlPlanApi {

    /**
     * 获取运行中的控盘计划列表
     *
     * @return 计划列表
     */
    List<ControlPlanDTO> getRunningPlanList();

    /**
     * 获取等待中的控盘计划列表
     *
     * @return 计划列表
     */
    List<ControlPlanDTO> getWaitPlanList();

    /**
     * 获取价格节点
     *
     * @param planId 计划id
     * @return 价格节点
     */
    Map<Long, ControlPricePointDTO> getPricePointMap(long planId);

    /**
     * 获取单个价格节点
     * @param planId 计划id
     * @param timestamp 时间戳
     * @return 价格节点
     */
    ControlPricePointDTO getPricePoint(long planId, long timestamp);

    /**
     * 控盘计划的k线列表
     *
     * @param planId 计划id
     * @return k线列表
     */
    List<ControlPlanKLineDTO> getPlanKlineList(long planId);

    /**
     * 开始执行控盘计划
     *
     * @param planId    控盘计划
     * @param execPrice 执行计划时的价格
     */
    void startPlan(long planId, BigDecimal execPrice);

    /**
     * 结束控盘计划的运行
     * @param planId 计划id
     */
    void finishPlan(long planId);

    /**
     * 保存控盘的数据k线 用于接口实际使用
     * @param controlCandleDOS k线数据列表
     */
    void saveControlCandles(List<ControlCandleDTO> controlCandleDOS);
}
