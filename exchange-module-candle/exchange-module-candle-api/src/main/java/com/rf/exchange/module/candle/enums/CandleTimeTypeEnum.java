package com.rf.exchange.module.candle.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-17
 */
@Getter
@AllArgsConstructor
public enum CandleTimeTypeEnum {
    MINUTES(1, "分钟", "m", "minute"),
    HOURS(2, "小时", "H", "hour"),
    DAYS(3, "日", "D", "day"),
    WEEKS(4, "周", "W", "week"),
    MONTH(5, "月", "M", "month"),
    ;

    private final int type;
    private final String name;
    private final String unit;
    // polygon请求使用的时间类型
    private final String polygonTimeSpan;

    public static CandleTimeTypeEnum enumOf(String val) {
        if (StrUtil.isEmpty(val)) {
            return null;
        }
        for (CandleTimeTypeEnum type : CandleTimeTypeEnum.values()) {
            if (type.getUnit().equals(val)) {
                return type;
            }
        }
        return null;
    }
}
