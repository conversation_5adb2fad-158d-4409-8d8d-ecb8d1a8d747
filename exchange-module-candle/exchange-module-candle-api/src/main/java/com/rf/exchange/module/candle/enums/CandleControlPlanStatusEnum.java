package com.rf.exchange.module.candle.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 控盘计划的状态枚举
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Getter
@AllArgsConstructor
public enum CandleControlPlanStatusEnum {
    WAITING(0, "等待中"),
    CLOSED(1, "已关闭"),
    RUNNING(2, "运行中"),
    FINISHED(3, "已完成"),
    ;
    private final int status;
    private final String name;

    public static CandleControlPlanStatusEnum statusOf(int status) {
        for (CandleControlPlanStatusEnum anEnum : values()) {
            if (anEnum.getStatus() == status) {
                return anEnum;
            }
        }
        return null;
    }
}
