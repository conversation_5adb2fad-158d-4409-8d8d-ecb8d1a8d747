package com.rf.exchange.module.candle.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-11-03
 */
@Data
public class ControlPlanKLineDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 控盘计划的id
     */
    private Long planId;
    /**
     * 交易对编码
     */
    private String code;
    /**
     * 引用价格
     */
    private BigDecimal referPrice;
    /**
     * 时间范围
     */
    private Integer timeRange;
    /**
     * 时间范围类型 1分钟 2小时 3日 4月
     */
    private Integer timeType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 最高价格
     */
    private BigDecimal highPrice;
    /**
     * 最低价格
     */
    private BigDecimal lowPrice;
    /**
     * 开盘价格
     */
    private BigDecimal openPrice;
    /**
     * 收盘价格
     */
    private BigDecimal closePrice;
    /**
     * 成交量
     */
    private BigDecimal volume;
    /**
     * 成交额
     */
    private BigDecimal turnover;
    /**
     * 是否是休市数据
     */
    private Boolean isMarketClose;
}
