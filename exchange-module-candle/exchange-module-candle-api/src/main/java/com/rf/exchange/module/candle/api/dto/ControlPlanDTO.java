package com.rf.exchange.module.candle.api.dto;

import com.rf.exchange.module.candle.enums.CandleControlPlanStatusEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-11-02
 */
@Data
public class ControlPlanDTO {
    /**
     * 计划id
     */
    private Long id;
    /**
     * 交易对id
     */
    private Long tradePairId;
    /**
     * 交易对代码
     */
    private String tradePairCode;
    /**
     * 控盘开始时间
     */
    private Long startTime;
    /**
     * 控盘结束时间
     */
    private Long endTime;
    /**
     * 生成计划时的交易对价格
     */
    private BigDecimal referPrice;
    /**
     * 执行计划时候的价格
     */
    private BigDecimal execPrice;
    /**
     * 结束价格
     */
    private BigDecimal endPrice;
    /**
     * 最低价
     */
    private BigDecimal minPrice;
    /**
     * 最低价
     */
    private BigDecimal maxPrice;
    /**
     * 波动率
     */
    private BigDecimal fluctuation;
    /**
     * 波动时长(秒)
     */
    private Integer durationSecs;
    /**
     * 控盘计划状态 0:等待中 1:关闭 2:执行中 3:执行完成
     * {@link CandleControlPlanStatusEnum}
     */
    private Integer status;
}
