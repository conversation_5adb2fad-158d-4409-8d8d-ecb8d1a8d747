<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.rf.dev</groupId>
    <artifactId>exchange-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>基础 bom 文件，管理整个项目的依赖版本</description>

    <properties>
        <revision>1.0.0-snapshot</revision>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <!-- 统一依赖管理 -->
        <spring.boot.version>3.3.0</spring.boot.version>
        <!-- Web 相关 -->
        <springdoc.version>2.2.0</springdoc.version>
        <springdocui.version>1.6.14</springdocui.version>
        <knife4j.version>4.3.0</knife4j.version>
        <!-- DB 相关 -->
        <druid.version>1.2.21</druid.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <mybatis-plus-generator.version>3.5.5</mybatis-plus-generator.version>
        <aws-java-sdk-s3.version>1.12.201</aws-java-sdk-s3.version>
        <dynamic-datasource.version>4.3.0</dynamic-datasource.version>
        <mybatis-plus-join.version>1.4.10</mybatis-plus-join.version>
        <easy-trans.version>2.2.11</easy-trans.version>
        <redisson.version>3.26.0</redisson.version>
        <dm8.jdbc.version>********</dm8.jdbc.version>
        <!-- 消息队列 -->
        <rocketmq-spring.version>2.3.0</rocketmq-spring.version>
        <protobuf-java.version>3.25.1</protobuf-java.version>
        <!-- 定时任务 -->
        <xxl-job-core.version>2.4.1</xxl-job-core.version>
        <!-- 服务保障相关 -->
        <lock4j.version>2.2.7</lock4j.version>
        <!-- 监控相关 -->
        <skywalking.version>9.0.0</skywalking.version>
        <spring-boot-admin.version>3.2.1</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!-- Test 测试相关 -->
        <podam.version>8.0.1.RELEASE</podam.version>
        <jedis-mock.version>1.0.13</jedis-mock.version>
        <mockito-inline.version>5.2.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>7.0.1</flowable.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>2.0.3</captcha-plus.version>
        <jsoup.version>1.17.2</jsoup.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool-5.version>5.8.25</hutool-5.version>
        <hutool-6.version>6.0.0-M10</hutool-6.version>
        <easyexcel.verion>3.3.4</easyexcel.verion>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>33.0.0-jre</guava.version>
        <guava-retrying.version>2.0.0</guava-retrying.version>
        <guice.version>7.0.0</guice.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <commons-net.version>3.10.0</commons-net.version>
        <commons-compress.verion>1.26.2</commons-compress.verion>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.9.1</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
        <!-- 三方云服务相关 -->
        <okio.version>3.5.0</okio.version>
        <okhttp3.version>4.11.0</okhttp3.version>
        <commons-io.version>2.15.1</commons-io.version>
        <minio.version>8.5.10</minio.version>
        <httpclient5-version>5.3.1</httpclient5-version>
        <!--<aliyun-java-sdk-core.version>4.6.4</aliyun-java-sdk-core.version>-->
        <!--<aliyun-java-sdk-dysmsapi.version>2.2.1</aliyun-java-sdk-dysmsapi.version>-->
        <!--<tencentcloud-sdk-java.version>3.1.880</tencentcloud-sdk-java.version>-->
        <!--<bcprov-jdk18on.version>1.78.1</bcprov-jdk18on.version>-->
        <justauth.version>2.0.5</justauth.version>
        <jimureport.version>1.6.6-beta2</jimureport.version>
        <xercesImpl.version>2.12.2</xercesImpl.version>
        <weixin-java.version>4.6.0</weixin-java.version>
        <codec.version>1.15</codec.version>
        <qrext4j.version>1.3.1</qrext4j.version>
        <logging.log4j.version>2.24.1</logging.log4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 业务组件 -->
            <dependency>
                <groupId>io.github.mouzt</groupId>
                <artifactId>bizlog-sdk</artifactId>
                <version>${bizlog-sdk.version}</version>
                <exclusions>
                    <exclusion> <!-- 排除掉springboot依赖使用项目的 -->
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-biz-tenant</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-biz-data-permission</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-biz-ip</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-biz-dict</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Spring 核心 -->
            <dependency>
                <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- Web 相关 -->
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdocui.version}</version>
            </dependency>

            <!-- DB 相关 -->
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId> <!-- 多数据源 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
                <version>${mybatis-plus-join.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fhs-opensource</groupId> <!-- VO 数据翻译 -->
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>${easy-trans.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-commons</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-mybatis-plus-extend</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-anno</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>

            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>

            <!-- Job 定时任务相关 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息队列相关 -->
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-mq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>

            <!-- 服务保障相关 -->
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-protection</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>redisson-spring-boot-starter</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 监控相关 -->
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-monitor</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-opentracing</artifactId>
                <version>${skywalking.version}</version>
                <!--<exclusions>-->
                <!--    <exclusion>-->
                <!--        <artifactId>opentracing-api</artifactId>-->
                <!--        <groupId>io.opentracing</groupId>-->
                <!--    </exclusion>-->
                <!--    <exclusion>-->
                <!--        <artifactId>opentracing-util</artifactId>-->
                <!--        <groupId>io.opentracing</groupId>-->
                <!--    </exclusion>-->
                <!--</exclusions>-->
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <!-- 实现 Spring Boot Admin Server 服务端 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>de.codecentric</groupId>
                        <artifactId>spring-boot-admin-server-cloud</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 实现 Spring Boot Admin Server 服务端 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
            <dependency>
                <groupId>com.github.fppt</groupId>
                <artifactId>jedis-mock</artifactId>
                <version>${jedis-mock.version}</version>
            </dependency>

            <!-- 单元测试，随机生成 POJO 类 -->
            <dependency>
                <groupId>uk.co.jemos.podam</groupId>
                <artifactId>podam</artifactId>
                <version>${podam.version}</version>
            </dependency>

            <!-- 工作流相关 -->
            <!--            <dependency>-->
            <!--                <groupId>org.flowable</groupId>-->
            <!--                <artifactId>flowable-spring-boot-starter-process</artifactId>-->
            <!--                <version>${flowable.version}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>org.flowable</groupId>-->
            <!--                <artifactId>flowable-spring-boot-starter-actuator</artifactId>-->
            <!--                <version>${flowable.version}</version>-->
            <!--            </dependency>-->
            <!-- 工作流相关结束 -->

            <!-- 工具类相关 -->
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- use mapstruct-jdk8 for Java 8 or higher -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.hutool</groupId>
                <artifactId>hutool-extra</artifactId>
                <version>${hutool-6.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.verion}</version>
            </dependency>

            <!-- 文件类型的识别 -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>${guava-retrying.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-captcha-plus</artifactId>
                <version>${captcha-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <!-- 三方云服务相关 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- HTTP -->
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5-version}</version>
            </dependency>

            <!-- SMS SDK begin -->
            <!--<dependency>-->
            <!--    <groupId>com.aliyun</groupId>-->
            <!--    <artifactId>aliyun-java-sdk-core</artifactId>-->
            <!--    <version>${aliyun-java-sdk-core.version}</version>-->
            <!--    <exclusions>-->
            <!--        <exclusion>-->
            <!--            <artifactId>opentracing-api</artifactId>-->
            <!--            <groupId>io.opentracing</groupId>-->
            <!--        </exclusion>-->
            <!--        <exclusion>-->
            <!--            <artifactId>opentracing-util</artifactId>-->
            <!--            <groupId>io.opentracing</groupId>-->
            <!--        </exclusion>-->
            <!--        <exclusion>-->
            <!--            <groupId>org.bouncycastle</groupId>-->
            <!--            <artifactId>bcprov-jdk15on</artifactId>-->
            <!--        </exclusion>-->
            <!--    </exclusions>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--    <groupId>com.aliyun</groupId>-->
            <!--    <artifactId>aliyun-java-sdk-dysmsapi</artifactId>-->
            <!--    <version>${aliyun-java-sdk-dysmsapi.version}</version>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--    <groupId>com.tencentcloudapi</groupId>-->
            <!--    <artifactId>tencentcloud-sdk-java-sms</artifactId>-->
            <!--    <version>${tencentcloud-sdk-java.version}</version>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--    <groupId>org.bouncycastle</groupId>-->
            <!--    <artifactId>bcprov-jdk18on</artifactId>-->
            <!--    <version>${bcprov-jdk18on.version}</version>-->
            <!--</dependency>-->
            <!-- SMS SDK end -->

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
                <version>${justauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>com.github.binarywang</groupId>-->
            <!--                <artifactId>weixin-java-pay</artifactId>-->
            <!--                <version>${weixin-java.version}</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.github.binarywang</groupId>-->
            <!--                <artifactId>wx-java-mp-spring-boot-starter</artifactId>-->
            <!--                <version>${weixin-java.version}</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.github.binarywang</groupId>-->
            <!--                <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>-->
            <!--                <version>${weixin-java.version}</version>-->
            <!--            </dependency>-->

            <!-- 积木报表-->
            <!--            <dependency>-->
            <!--                <groupId>org.jeecgframework.jimureport</groupId>-->
            <!--                <artifactId>jimureport-spring-boot3-starter</artifactId>-->
            <!--                <version>${jimureport.version}</version>-->
            <!--                <exclusions>-->
            <!--                    <exclusion>-->
            <!--                        <groupId>com.alibaba</groupId>-->
            <!--                        <artifactId>druid</artifactId>-->
            <!--                    </exclusion>-->
            <!--                </exclusions>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xercesImpl.version}</version>
            </dependency>

            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-i18n</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rf.dev</groupId>
                <artifactId>exchange-spring-boot-starter-aws</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--密钥生成-->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${codec.version}</version>
            </dependency>
            <!-- 二维码依赖 -->
            <dependency>
                <groupId>org.iherus</groupId>
                <artifactId>qrext4j</artifactId>
                <version>${qrext4j.version}</version>
            </dependency>

            <!-- Log4j2日志记录框架 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${logging.log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${logging.log4j.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <!--<plugins>-->
        <!--    &lt;!&ndash; 统一 revision 版本 &ndash;&gt;-->
        <!--    <plugin>-->
        <!--        <groupId>org.codehaus.mojo</groupId>-->
        <!--        <artifactId>flatten-maven-plugin</artifactId>-->
        <!--        <version>${flatten-maven-plugin.version}</version>-->
        <!--        <configuration>-->
        <!--            <flattenMode>resolveCiFriendliesOnly</flattenMode>-->
        <!--            <updatePomFile>true</updatePomFile>-->
        <!--        </configuration>-->
        <!--        <executions>-->
        <!--            <execution>-->
        <!--                <goals>-->
        <!--                    <goal>flatten</goal>-->
        <!--                </goals>-->
        <!--                <id>flatten</id>-->
        <!--                <phase>process-resources</phase>-->
        <!--            </execution>-->
        <!--            <execution>-->
        <!--                <goals>-->
        <!--                    <goal>clean</goal>-->
        <!--                </goals>-->
        <!--                <id>flatten.clean</id>-->
        <!--                <phase>clean</phase>-->
        <!--            </execution>-->
        <!--        </executions>-->
        <!--    </plugin>-->
        <!--</plugins>-->
    </build>

</project>
