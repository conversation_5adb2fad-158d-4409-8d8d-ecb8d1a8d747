package com.rf.exchange.module.infra.api.websocket;

import com.rf.exchange.framework.common.util.json.JsonUtils;

/**
 * WebSocket 发送器的 API 接口
 * <p>
 * 对 WebSocketMessageSender 进行封装，提供给其它模块使用
 *
 * <AUTHOR>
 */
public interface WebSocketSenderApi {

    /**
     * 发送消息给指定用户
     *
     * @param tenantId       租户编号
     * @param userType       用户类型
     * @param userId         用户编号
     * @param messageType    消息类型
     * @param messageContent 消息内容，JSON 格式
     */
    void send(Long tenantId, Integer userType, Long userId, String messageType, String messageContent);

    /**
     * 发送消息给指定用户类型
     *
     * @param tenantId       租户编号
     * @param userType       用户类型
     * @param messageType    消息类型
     * @param messageContent 消息内容，JSON 格式
     */
    void send(Long tenantId, Integer userType, String messageType, String messageContent);

    /**
     * 发送消息给指定 Session
     *
     * @param sessionId      Session 编号
     * @param messageType    消息类型
     * @param messageContent 消息内容，JSON 格式
     */
    void send(String sessionId, String messageType, String messageContent);

    default void sendObject(Long tenantId, Integer userType, Long userId, String messageType, Object messageContent) {
        send(tenantId, userType, userId, messageType, JsonUtils.toJsonString(messageContent));
    }

    default void sendObject(Long tenantId, Integer userType, String messageType, Object messageContent) {
        send(tenantId, userType, messageType, JsonUtils.toJsonString(messageContent));
    }

    default void sendObject(String sessionId, String messageType, Object messageContent) {
        send(sessionId, messageType, JsonUtils.toJsonString(messageContent));
    }

}
