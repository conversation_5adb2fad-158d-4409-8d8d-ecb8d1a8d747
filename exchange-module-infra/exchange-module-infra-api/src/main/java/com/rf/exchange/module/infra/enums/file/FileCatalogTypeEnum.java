package com.rf.exchange.module.infra.enums.file;

import cn.hutool.core.util.ArrayUtil;
import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@AllArgsConstructor
@Getter
public enum FileCatalogTypeEnum implements IntArrayValuable {
    MEMBER_HEAD(0, "member/head/"),
    BANNER(1, "system/banner/"),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(FileCatalogTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String catalog;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public int getValue() {
        return type;
    }
    public static FileCatalogTypeEnum get(int value) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getType().equals(value),
                values());
    }
}
