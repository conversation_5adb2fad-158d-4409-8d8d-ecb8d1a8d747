package com.rf.exchange.module.infra.api.websocket;

import com.rf.exchange.framework.websocket.core.sender.WebSocketMessageSender;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * WebSocket 发送器的 API 实现类
 *
 * <AUTHOR>
 */
@Component
public class WebSocket<PERSON><PERSON><PERSON>piImpl implements WebSocketSenderApi {

    @Resource
    private WebSocketMessageSender webSocketMessageSender;

    @Override
    public void send(Long tenantId, Integer userType, Long userId, String messageType, String messageContent) {
        webSocketMessageSender.send(tenantId, userType, userId, messageType, messageContent);
    }

    @Override
    public void send(Long tenantId, Integer userType, String messageType, String messageContent) {
        webSocketMessageSender.send(tenantId, userType, messageType, messageContent);
    }

    @Override
    public void send(String sessionId, String messageType, String messageContent) {
        webSocketMessageSender.send(sessionId, messageType, messageContent);
    }

}
