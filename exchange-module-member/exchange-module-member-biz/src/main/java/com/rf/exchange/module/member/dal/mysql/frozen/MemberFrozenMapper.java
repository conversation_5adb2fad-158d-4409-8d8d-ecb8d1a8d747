package com.rf.exchange.module.member.dal.mysql.frozen;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.member.controller.admin.frozen.vo.*;

import java.util.List;

/**
 * 会员冻结明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberFrozenMapper extends BaseMapperX<MemberFrozenDO> {

    default PageResult<MemberFrozenDO> selectPage(MemberFrozenPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberFrozenDO>()
                .eqIfPresent(MemberFrozenDO::getUserId, reqVO.getUserId())
                .likeIfPresent(MemberFrozenDO::getUsername, reqVO.getUsername())
                .eqIfPresent(MemberFrozenDO::getFrozenAmount, reqVO.getFrozenAmount())
                .eqIfPresent(MemberFrozenDO::getFrozenReason, reqVO.getFrozenReason())
                .eqIfPresent(MemberFrozenDO::getBizOrderNo, reqVO.getBizOrderNo())
                .betweenIfPresent(MemberFrozenDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberFrozenDO::getId));
    }

    default List<MemberFrozenDO> getListByUserId(Long userId){
        return selectList(new LambdaQueryWrapperX<MemberFrozenDO>()
                .eqIfPresent(MemberFrozenDO::getUserId,userId)
                .orderByDesc(MemberFrozenDO::getId));
    }
}