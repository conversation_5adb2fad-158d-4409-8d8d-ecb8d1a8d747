package com.rf.exchange.module.member.dal.redis;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.member.dal.dataobject.favoritetradepair.MemberFavoriteTradePairDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024-06-21
 */
@Repository
public class MemberFavoriteTradePairRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取指定userId的交易对列表缓存
     *
     * @param userId 会员id
     * @return 交易对列表
     */
    public List<MemberFavoriteTradePairDO> get(Long userId) {
        String redisKey = formatKey(userId);
        return JsonUtils.parseArray(stringRedisTemplate.opsForValue().get(redisKey), MemberFavoriteTradePairDO.class);
    }

    /**
     * 缓存指定用户的收藏交易对列表
     *
     * @param list     交易对id列表
     * @param userId 租户id
     */
    public void set(List<MemberFavoriteTradePairDO> list, Long userId) {
        if (CollectionUtil.isEmpty(list)) {
            delete(userId);
            return;
        }
        String redisKey = formatKey(userId);
        String jsonString = JsonUtils.toJsonString(list);
        stringRedisTemplate.opsForValue().set(redisKey, jsonString, 1, TimeUnit.DAYS);
    }

    /**
     * 删除指定userId的收藏交易对列表缓存
     *
     * @param userId 租户id
     */
    public void delete(Long userId) {
        stringRedisTemplate.delete(formatKey(userId));
    }

    private static String formatKey(Long userId) {
        return RedisKeyConstants.MEMBER_FAVORITE_TRADE_PAIR + ":" + userId;
    }
}
