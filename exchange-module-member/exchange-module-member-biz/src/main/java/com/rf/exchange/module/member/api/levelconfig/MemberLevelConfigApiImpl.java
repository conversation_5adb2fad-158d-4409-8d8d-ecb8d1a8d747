package com.rf.exchange.module.member.api.levelconfig;

import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.member.api.levelconfig.dto.MemberLevelConfigCreateReqVO;
import com.rf.exchange.module.member.config.MemberProperties;
import com.rf.exchange.module.member.controller.admin.levelconfig.vo.LevelConfigSaveReqVO;
import com.rf.exchange.module.member.service.levelconfig.MemberLevelConfigService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class MemberLevelConfigApiImpl implements MemberLevelConfigApi {
    @Resource
    private MemberLevelConfigService memberLevelConfigService;
    @Resource
    private MemberProperties memberProperties;

    @Override
    public void createDefaultLevelConfig(Long tenantId) {
        LevelConfigSaveReqVO saveReqVO = BeanUtils.toBean(memberProperties.getDefaultLevel(), LevelConfigSaveReqVO.class);
        saveReqVO.setFirst(true);
        memberLevelConfigService.createLevelConfig(tenantId,saveReqVO);
    }
}
