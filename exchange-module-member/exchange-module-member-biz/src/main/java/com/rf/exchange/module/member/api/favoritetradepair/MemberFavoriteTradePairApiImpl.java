package com.rf.exchange.module.member.api.favoritetradepair;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.module.member.dal.dataobject.favoritetradepair.MemberFavoriteTradePairDO;
import com.rf.exchange.module.member.dal.mysql.favoritetradepair.MemberFavoriteTradePairMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 会员收藏交易对 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class MemberFavoriteTradePairApiImpl implements MemberFavoriteTradePairApi {

    @Resource
    private MemberFavoriteTradePairMapper favoriteTradePairMapper;

    @Override
    public Set<String> getFavoriteTradePairCodes(Long userId, Long tenantId) {
        if (userId == null) {
            return Collections.emptySet();
        }
        
        List<MemberFavoriteTradePairDO> favoriteList = favoriteTradePairMapper.selectListByUserId(userId);
        if (CollectionUtil.isEmpty(favoriteList)) {
            return Collections.emptySet();
        }
        
        return favoriteList.stream()
                .map(MemberFavoriteTradePairDO::getTradePairCode)
                .collect(Collectors.toSet());
    }
}
