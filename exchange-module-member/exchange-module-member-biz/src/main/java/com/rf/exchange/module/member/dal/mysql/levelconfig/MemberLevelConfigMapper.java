package com.rf.exchange.module.member.dal.mysql.levelconfig;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.member.dal.dataobject.levelconfig.MemberLevelConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.member.controller.admin.levelconfig.vo.*;

/**
 * 会员等级配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberLevelConfigMapper extends BaseMapperX<MemberLevelConfigDO> {

    default PageResult<MemberLevelConfigDO> selectPage(LevelConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberLevelConfigDO>()
                .likeIfPresent(MemberLevelConfigDO::getName, reqVO.getName())
                .orderByDesc(MemberLevelConfigDO::getId));
    }

    default MemberLevelConfigDO getDefault(){
        return selectOne(new LambdaQueryWrapperX<MemberLevelConfigDO>().eq(MemberLevelConfigDO::getFirst,true).last("limit 1"));
    }
}