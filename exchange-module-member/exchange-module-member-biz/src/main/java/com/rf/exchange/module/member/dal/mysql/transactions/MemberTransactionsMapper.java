package com.rf.exchange.module.member.dal.mysql.transactions;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.member.controller.app.transactions.vo.AppMemberTransactionsPageReqVO;
import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.member.controller.admin.transactions.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员账变 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberTransactionsMapper extends BaseMapperX<MemberTransactionsDO> {

    default PageResult<MemberTransactionsDO> selectPage(MemberTransactionsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberTransactionsDO>()
                .eqIfPresent(MemberTransactionsDO::getUserId, reqVO.getUserId())
                .likeIfPresent(MemberTransactionsDO::getUsername, reqVO.getUsername())
                .eqIfPresent(MemberTransactionsDO::getType, reqVO.getType())
                .eqIfPresent(MemberTransactionsDO::getBizOrderNo, reqVO.getBizOrderNo())
                .eqIfPresent(MemberTransactionsDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(MemberTransactionsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberTransactionsDO::getId));
    }

    default PageResult<MemberTransactionsDO> selectPage(Long userId, AppMemberTransactionsPageReqVO reqVO) {
        LambdaQueryWrapperX<MemberTransactionsDO> queryWrapperX = new LambdaQueryWrapperX<MemberTransactionsDO>();
        queryWrapperX.eq(MemberTransactionsDO::getUserId, userId)
                .in(MemberTransactionsDO::getType, MemberTransactionsTypeEnum.getCanVisitorList());
        if (reqVO.getType() != null && reqVO.getType() > 0) {
            queryWrapperX.eq(MemberTransactionsDO::getType, reqVO.getType());
        }
        return selectPage(reqVO, queryWrapperX);
    }

    default PageResult<MemberTransactionsDO> selectPageExcludeRechargeWithdraw(Long userId, AppMemberTransactionsPageReqVO reqVO) {
        LambdaQueryWrapperX<MemberTransactionsDO> queryWrapperX = new LambdaQueryWrapperX<MemberTransactionsDO>();

        // 获取所有可见类型，然后排除充值(1)和提现(2)
        List<Integer> visibleTypes = MemberTransactionsTypeEnum.getCanVisitorList()
                .stream()
                .filter(type -> !type.equals(MemberTransactionsTypeEnum.RECHARGE.getValue())
                             && !type.equals(MemberTransactionsTypeEnum.WITHDRAW.getValue()))
                .collect(Collectors.toList());

        queryWrapperX.eq(MemberTransactionsDO::getUserId, userId)
                .in(MemberTransactionsDO::getType, visibleTypes);

        if (reqVO.getType() != null && reqVO.getType() > 0) {
            queryWrapperX.eq(MemberTransactionsDO::getType, reqVO.getType());
        }
        return selectPage(reqVO, queryWrapperX);
    }

    @Select("SELECT * FROM member_transactions mt " +
            "WHERE mt.id IN (SELECT MAX(id) FROM member_transactions " +
            "WHERE create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY user_id)")
    List<MemberTransactionsDO> getLastTransactionByRange(@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}