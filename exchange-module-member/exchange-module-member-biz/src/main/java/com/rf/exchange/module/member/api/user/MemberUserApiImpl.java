package com.rf.exchange.module.member.api.user;

import com.rf.exchange.module.member.api.user.dto.MemberUserConfigRespDTO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.api.user.dto.MemberUserTotalRespVO;
import com.rf.exchange.module.member.convert.user.MemberUserConvert;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserConfigDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.service.config.MemberUserConfigService;
import com.rf.exchange.module.member.service.user.MemberUserService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * 会员用户的 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberUserApiImpl implements MemberUserApi {

    @Resource
    private MemberUserService userService;
    @Resource
    private MemberUserConfigService userConfigService;

    @Override
    public MemberUserRespDTO getUser(Long id) {
        MemberUserDO user = userService.getUser(id);
        return MemberUserConvert.INSTANCE.convert2(user);
    }

    @Override
    public MemberUserRespDTO checkUserExists(Long userId) {
        MemberUserDO userDo = userService.checkUserExists(userId);
        return MemberUserConvert.INSTANCE.convert2(userDo);
    }

    @Override
    public List<MemberUserRespDTO> getUserList(Collection<Long> ids) {
        return MemberUserConvert.INSTANCE.convertList2(userService.getUserList(ids));
    }

    @Override
    public MemberUserConfigRespDTO getUserConfigCached(Long userId) {
        MemberUserConfigDO configDO = userConfigService.getConfigCachedByUserId(userId);
        return MemberUserConvert.INSTANCE.convert4(configDO);
    }

    @Override
    public Map<Long, MemberUserConfigRespDTO> getUserConfigByIds(Collection<Long> userIds) {
        Map<Long, MemberUserConfigDO> userConfigByIds = userConfigService.getConfigListByUserIdList(userIds);
        return MemberUserConvert.INSTANCE.convertMap(userConfigByIds);
    }

    @Override
    public List<MemberUserRespDTO> getUsersOfAgentId(Long agentId, Collection<Long> descendantIds) {
        final List<MemberUserDO> userList = userService.getAgentUserList(agentId, descendantIds);
        return MemberUserConvert.INSTANCE.convertList2(userList);
    }

    @Override
    public MemberUserTotalRespVO getTotalByAgentId(List<Long> agentId) {
        return userService.totalByAgentIdList(agentId);
    }
}
