package com.rf.exchange.module.member.dal.mysql.wallet;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.member.dal.dataobject.wallet.MemberWalletDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.member.controller.admin.wallet.vo.*;

import java.util.List;

/**
 * 会员钱包 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberWalletMapper extends BaseMapperX<MemberWalletDO> {

    default PageResult<MemberWalletDO> selectPage(MemberWalletPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberWalletDO>()
                .eqIfPresent(MemberWalletDO::getUserId, reqVO.getUserId())
                .likeIfPresent(MemberWalletDO::getUsername, reqVO.getUsername())
                .eqIfPresent(MemberWalletDO::getType, reqVO.getType())
                .likeIfPresent(MemberWalletDO::getTypeName, reqVO.getTypeName())
                .likeIfPresent(MemberWalletDO::getName, reqVO.getName())
                .eqIfPresent(MemberWalletDO::getAccount, reqVO.getAccount())
                .eqIfPresent(MemberWalletDO::getBankAddress, reqVO.getBankAddress())
                .eqIfPresent(MemberWalletDO::getBankBranch, reqVO.getBankBranch())
                .eqIfPresent(MemberWalletDO::getStatus, reqVO.getStatus())
                .eqIfPresent(MemberWalletDO::getAreaId, reqVO.getAreaId())
                .likeIfPresent(MemberWalletDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(MemberWalletDO::getCurrencyId, reqVO.getCurrencyId())
                .likeIfPresent(MemberWalletDO::getCurrencyName, reqVO.getCurrencyName())
                .betweenIfPresent(MemberWalletDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberWalletDO::getId));
    }

    default List<MemberWalletDO> selectListByUserId(Long userId) {
        return selectList(MemberWalletDO::getUserId,userId);
    }

    default MemberWalletDO selectListByUserIdAndWalletId(Long userId,Long walletId) {
        return selectOne(new LambdaQueryWrapperX<MemberWalletDO>().eq(MemberWalletDO::getUserId,userId).eq(MemberWalletDO::getId,walletId));
    }
}