package com.rf.exchange.module.member.dal.mysql.total;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.member.dal.dataobject.total.MemberTotalDayDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.member.controller.admin.total.vo.*;

/**
 * 会员日统计数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberTotalDayMapper extends BaseMapperX<MemberTotalDayDO> {

    default PageResult<MemberTotalDayDO> selectPage(MemberTotalDayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberTotalDayDO>()
                .eqIfPresent(MemberTotalDayDO::getAgentId, reqVO.getAgentId())
                .likeIfPresent(MemberTotalDayDO::getAgentName, reqVO.getAgentName())
                .eqIfPresent(MemberTotalDayDO::getUserId, reqVO.getUserId())
                .likeIfPresent(MemberTotalDayDO::getUsername, reqVO.getUsername())
                .betweenIfPresent(MemberTotalDayDO::getTotalTime, reqVO.getTotalTime())
                .eqIfPresent(MemberTotalDayDO::getUsdtBalance, reqVO.getUsdtBalance())
                .eqIfPresent(MemberTotalDayDO::getProfitAmount, reqVO.getProfitAmount())
                .eqIfPresent(MemberTotalDayDO::getOrderCount, reqVO.getOrderCount())
                .betweenIfPresent(MemberTotalDayDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberTotalDayDO::getId));
    }

}