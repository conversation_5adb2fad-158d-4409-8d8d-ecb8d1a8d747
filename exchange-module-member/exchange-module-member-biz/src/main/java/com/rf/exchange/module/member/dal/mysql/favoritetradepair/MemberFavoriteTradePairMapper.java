package com.rf.exchange.module.member.dal.mysql.favoritetradepair;

import java.util.*;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.member.controller.admin.favoritetradepair.vo.MemberFavoriteTradePairPageReqVO;
import com.rf.exchange.module.member.dal.dataobject.favoritetradepair.MemberFavoriteTradePairDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 会员收藏交易对 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberFavoriteTradePairMapper extends BaseMapperX<MemberFavoriteTradePairDO> {

    default MemberFavoriteTradePairDO selectByCode(Long userId, String code) {
        return selectOne(new LambdaQueryWrapperX<MemberFavoriteTradePairDO>()
                .eqIfPresent(MemberFavoriteTradePairDO::getUserId, userId)
                .eqIfPresent(MemberFavoriteTradePairDO::getTradePairCode, code));
    }

    default PageResult<MemberFavoriteTradePairDO> selectPage(MemberFavoriteTradePairPageReqVO reqVO){
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberFavoriteTradePairDO>()
                .eqIfPresent(MemberFavoriteTradePairDO::getUserId, reqVO.getUserId())
                .likeIfPresent(MemberFavoriteTradePairDO::getUsername, reqVO.getUsername())
                .eqIfPresent(MemberFavoriteTradePairDO::getTradePairCode, reqVO.getTradePairCode())
                .eqIfPresent(MemberFavoriteTradePairDO::getTradePairName, reqVO.getTradePairName())
                .betweenIfPresent(MemberFavoriteTradePairDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberFavoriteTradePairDO::getId));
    }

    default List<MemberFavoriteTradePairDO> selectListByUserId(Long userId) {
        LambdaQueryWrapperX<MemberFavoriteTradePairDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(MemberFavoriteTradePairDO::getUserId, userId);
        return selectList(wrapper);
    }

    @Update("delete from member_favorite_trade_pair where trade_pair_code=#{code} and user_id=#{userId}")
    void deleteForceByCode(@Param("code") String code, @Param("userId") Long userId);
}