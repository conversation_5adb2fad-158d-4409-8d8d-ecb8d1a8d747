package com.rf.exchange.module.member.api.balance;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionDTO;
import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
import com.rf.exchange.module.member.service.transactions.MemberTransactionsService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class MemberTransactionApiImpl implements MemberTransactionApi {

    @Resource
    private MemberTransactionsService memberTransactionsService;

    @Override
    public List<MemberTransactionDTO> getTransactionsByRelationOrderNo(String orderNo) {
        if (StrUtil.isEmpty(orderNo)) {
            return List.of();
        }
        final List<MemberTransactionsDO> dto = memberTransactionsService.getTransactionsByRelationOrderNo(orderNo);
        return BeanUtils.toBean(dto, MemberTransactionDTO.class);
    }
}
