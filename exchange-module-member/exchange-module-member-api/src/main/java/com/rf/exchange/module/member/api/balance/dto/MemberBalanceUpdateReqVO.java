package com.rf.exchange.module.member.api.balance.dto;

import com.rf.exchange.module.member.enums.balance.MemberBalanceUpdateEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
public class MemberBalanceUpdateReqVO {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 变动金额（正数）由调用的方法内部决定正负
     */
    private BigDecimal amount;
    /**
     * 是否只处理冻结余额
     * true: 只处理冻结余额
     */
    private boolean isFrozenOnly = false;
    /**
     * 冻结金额的更新类型
     * 为null时不处理冻结金额
     */
    private MemberBalanceUpdateEnum frozenUpdateEnum;
    /**
     * 冻结金额的备注
     */
    private String frozenUpdateMark = "无备注";
    /**
     * 关联的订单号(记录冻结金额帐变的时候需要)
     */
    private String relatedOrderNo;
    /**
     * 是否增加总充值金额
     * ture: 需要将amount加到总充值字段
     */
    private boolean isIncrRecharge = false;
    /**
     * 是否增加总提款金额
     * true: 需要将amount加到总提款字段
     */
    private boolean isIncrWithdraw = false;
    /**
     * 如果余额不足是否扣减到0
     * true: 如果afterBalance小于0则最后扣减到0
     */
    private boolean isDecrToZeroIfLess = false;
}
