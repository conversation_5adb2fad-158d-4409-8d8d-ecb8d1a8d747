package com.rf.exchange.module.member.enums;

/**
 * <AUTHOR>
 * @since 2024-09-15
 */
public interface DictTypeConstants {

    /**
     * 用户密码错误统计配置
     */
    String USER_PASSWORD_WRONG_CONFIG = "user_pwd_wrong_config";
    /**
     * 是否启用
     */
    String USER_PASSWORD_WRONG_ENABLE = "enable";
    /**
     * 登录密码
     */
    String USER_PASSWORD_WRONG_LOGIN = "login_pwd";
    /**
     * 资金密码
     */
    String USER_PASSWORD_WRONG_FUND = "funds_pwd";
    /**
     * 密码错误次数阀值
     */
    String USER_PASSWORD_WRONG_THRESHOLD = "wrong_count_threshold";
    /**
     * 密码错误次数的时间有效期
     * 1. 字符串0 表示永久有效期
     * 2. ISO-8601 格式的字符串，格式为PnDTnHnMnS，比如 P1D表示一天 PT1M表示一分钟
     */
    String USER_PASSWORD_WRONG_TIME_EXPIRE = "time_expire";
}
