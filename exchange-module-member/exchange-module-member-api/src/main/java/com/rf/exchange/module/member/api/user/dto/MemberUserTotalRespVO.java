package com.rf.exchange.module.member.api.user.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MemberUserTotalRespVO {
    public MemberUserTotalRespVO() {
        totalMemberBalance = BigDecimal.ZERO;
        totalMemberRechargeAmount = BigDecimal.ZERO;
        totalMemberWithdrawAmount = BigDecimal.ZERO;
        todayNewMemberRechargeAmount = BigDecimal.ZERO;
        todayNewMemberWithdrawAmount = BigDecimal.ZERO;
        todayRechargeAmount = BigDecimal.ZERO;
        todayWithdrawAmount = BigDecimal.ZERO;
    }

    private long totalMemberCount;
    private BigDecimal totalMemberBalance;
    private BigDecimal totalMemberRechargeAmount;
    private BigDecimal totalMemberWithdrawAmount;

    private int todayNewMemberCount;
    private int todayNewMemberRechargeCount;
    private BigDecimal todayNewMemberRechargeAmount;
    private int todayNewMemberWithdrawCount;
    private BigDecimal todayNewMemberWithdrawAmount;

    private int todayMemberRechargeCount;
    private int todayRechargeCount;
    private BigDecimal todayRechargeAmount;

    private int todayMemberWithdrawCount;
    private int todayWithdrawCount;
    private BigDecimal todayWithdrawAmount;


}
