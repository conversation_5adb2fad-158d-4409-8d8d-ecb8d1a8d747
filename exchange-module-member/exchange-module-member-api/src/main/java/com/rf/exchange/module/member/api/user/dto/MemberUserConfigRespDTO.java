package com.rf.exchange.module.member.api.user.dto;

import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-07-13
 */
@Data
public class MemberUserConfigRespDTO {
    private Long memberId;
    /**
     * 盈利类型
     * {@link MemberConfigProfitTypeEnum}
     */
    private Integer profitType;
    /**
     * 随机盈利的赢率
     */
    private BigDecimal randomRate;
}
