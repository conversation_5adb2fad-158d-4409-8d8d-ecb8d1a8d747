package com.rf.exchange.module.member.context;

import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024-07-15
 */
@Getter
@Setter
public class MemberBalanceUpdateContext {
    /**
     * 用户信息
     */
    private MemberUserRespDTO memberUserDTO;
    /**
     * 余额更新信息
     */
    private MemberBalanceUpdateReqVO balanceReqVO;
    /**
     * 帐变更新信息
     */
    private MemberTransactionSaveReqVO transactionReqVO;

    public MemberBalanceUpdateContext(MemberUserRespDTO memberUserDTO,
                                      MemberBalanceUpdateReqVO balanceReqVO) {
        this(memberUserDTO, balanceReqVO, null);
    }

    public MemberBalanceUpdateContext(MemberUserRespDTO memberUserDTO,
                                      MemberBalanceUpdateReqVO balanceReqVO,
                                      MemberTransactionSaveReqVO transactionReqVO) {
        this.memberUserDTO = memberUserDTO;
        this.balanceReqVO = balanceReqVO;
        this.transactionReqVO = transactionReqVO;
    }
}
