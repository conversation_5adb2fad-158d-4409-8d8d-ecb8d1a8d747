package com.rf.exchange.module.member.enums.transactions;

import cn.hutool.core.util.ArrayUtil;
import com.rf.exchange.framework.common.core.IntArrayValuable;
import com.rf.exchange.framework.i18n.I;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-06-06
 */
@AllArgsConstructor
@Getter
public enum MemberTransactionsTypeEnum implements IntArrayValuable {

    RECHARGE(1, "充值", true),
    WITHDRAW(2, "提现", true),
    BUY_SPOT(3, "买入现货", true),
    SELL_SPOT(4, "卖出现货", true),
    BUY_MARGIN(5, "买入合约", true),
    SELL_MARGIN(6, "卖出合约", true),
    UPDATE_BALANCE(7, "修改余额", false),
    BUY_TIME_CONTRACT(8, "买入限时合约", true),
    CLOSE_TIME_CONTRACT(9, "限时合约退回", true),
    FROZEN_INCR(10, "转入冻结", false),
    FROZEN_DECR(11, "冻结转出", false),
    BALANCE_TO_FROZEN(12, "余额转入冻结", false),
    FROZEN_TO_BALANCE(13, "冻结转入余额", false),
    TIME_CONTRACT_WIN(20, "限时合约盈利", false),
    CONTRACT_MARGIN_DECR(30, "合约保证金扣除", false),
    CONTRACT_MARGIN_REFUND(31, "合约保证金退回",false),
    CONTRACT_ORDER_FEE(32, "合约委托手续费",false),
    CONTRACT_POSITION_FEE(33, "合约持仓手续费",false),
    CONTRACT_CLOSE_POSITION(34, "合约平仓结算", false),
    RE_WITHDRAW(35, "重新提现", false),
    MINING_ORDER(36, "挖矿订单下单", false),
    MINING_ORDER_REDEMPTION_PENALTY(37, "挖矿订单赎回违约金扣除", false),
    MINING_ORDER_PRINCIPAL_REFUND(38, "挖矿订单本金退还", false),
    MINING_ORDER_INCOME(39, "挖矿订单利息", false),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(MemberTransactionsTypeEnum::getValue).toArray();

    /**
     * 类型
     */
    private final Integer value;
    /**
     * 名字
     */
    private final String label;

    /**
     * 会员能不能看到
     */
    private final Boolean memberVisitor;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static MemberTransactionsTypeEnum get(int value) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getValue().equals(value),
                values());
    }

    public static List<Integer> getCanVisitorList() {
        return Arrays.stream(values())
                .filter(MemberTransactionsTypeEnum::getMemberVisitor)
                .map(MemberTransactionsTypeEnum::getValue)
                .collect(Collectors.toList());
    }

    public static String getI18n(int value) {
        MemberTransactionsTypeEnum v = get(value);
        if (v != null) {
            return I.n(v.name());
        }
        return "";
    }
}
