package com.rf.exchange.module.member.enums.fundsrecord;

import cn.hutool.core.util.ArrayUtil;
import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024-06-06
 */
@AllArgsConstructor
@Getter
public enum MemberFundsRecordOpTypeEnum implements IntArrayValuable {

    RECHARGE(1, "充值"),
    WITHDRAW(2, "提现")
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(MemberFundsRecordOpTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static MemberFundsRecordOpTypeEnum get(int value) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getType().equals(value),
                values());
    }
}
