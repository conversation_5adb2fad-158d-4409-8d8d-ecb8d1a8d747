package com.rf.exchange.module.member.api.user.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户信息 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class MemberUserRespDTO {
    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 帐号状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 创建时间（注册时间）
     */
    private Long createTime;

    // ========== 其它信息 ==========

    /**
     * 会员级别编号
     */
    private Long levelId;

    /**
     * 积分
     */
    private Integer point;

    @JsonIgnore
    private BigDecimal usdtBalance;

    @JsonIgnore
    private BigDecimal usdtFrozenBalance;

    @JsonIgnore
    private BigDecimal recharge;

    @JsonIgnore
    private BigDecimal withdraw;

    @JsonIgnore
    private Long tenantId;

    @JsonIgnore
    private Integer certificationStatus;

    /**
     * 密码错误次数
     */
    private Integer pwdWrongCount;
    /**
     * 禁止提现
     * true: 禁止
     */
    private Boolean disableWithdraw;
    /**
     * 禁止限时合约下注
     * true: 禁止
     */
    private Boolean disableTimeContract;
    /**
     * 禁止合约下单
     * true: 禁止
     */
    private Boolean disableContract;
}
