package com.rf.exchange.module.member.enums;

import com.rf.exchange.framework.common.exception.ErrorCode;

/**
 * Member 错误码枚举类
 * <p>
 * member 系统，使用 1-004-000-000 段
 */
public interface ErrorCodeConstants {
    // ========== 地区相关 1-003-001-000 ============
    ErrorCode AREA_NOT_EXISTS = new ErrorCode(1_003_001_001, "该地区不存在", "AREA_NOT_EXISTS");

    // ========== 用户相关 1-004-001-000 ============

    ErrorCode USER_ACCOUNT_NOT_EMPTY = new ErrorCode(1_004_001_001, "账号不能为空", "USER_ACCOUNT_NOT_EMPTY");
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_004_001_001, "用户不存在", "USER_NOT_EXISTS");
    ErrorCode USER_MOBILE_NOT_EXISTS = new ErrorCode(1_004_001_002, "手机号未注册用户", "USER_MOBILE_NOT_EXISTS");
    ErrorCode USER_MOBILE_USED = new ErrorCode(1_004_001_003, "该手机号已经被使用", "USER_MOBILE_USED");
    ErrorCode USER_ACCOUNT_USED = new ErrorCode(1_004_001_004, "账号已被使用", "USER_ACCOUNT_USED");
    ErrorCode USER_EMAIL_USED = new ErrorCode(1_004_001_005, "邮箱已被使用", "USER_EMAIL_USED");
    ErrorCode USER_EMAIL_NOT_EXISTS = new ErrorCode(1_004_001_006, "邮箱不存在", "USER_EMAIL_NOT_EXISTS");
    ErrorCode USER_OLD_PASSWORD_NOT_MATCH = new ErrorCode(1_004_001_007, "旧密码错误", "USER_OLD_PASSWORD_NOT_MATCH");
    ErrorCode USER_BALANCE_ERROR = new ErrorCode(1_004_001_008, "用户余额错误", "USER_BALANCE_ERROR");
    ErrorCode USER_FORBIDDEN_WITHDRAW = new ErrorCode(1_004_013_008, "提现功能已禁用", "USER_FORBIDDEN_FUNC");
    ErrorCode USER_FORBIDDEN_CONTRACT = new ErrorCode(1_004_013_009, "合约功能已禁用", "USER_FORBIDDEN_FUNC");
    ErrorCode USER_FORBIDDEN_TIME_CONTRACT = new ErrorCode(1_004_013_010, "限时功能已禁用", "USER_FORBIDDEN_FUNC");

    // ========== 用户配置相关 ==========
    ErrorCode USER_CONFIG_NOT_SUPPORTED = new ErrorCode(1_004_002_000, "暂不支持的用户配置项", "USER_CONFIG_NOT_SUPPORTED");

    // ========== AUTH 模块 1-004-003-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_004_003_000, "登录失败，账号密码不正确", "AUTH_LOGIN_BAD_CREDENTIALS");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_004_003_001, "登录失败，账号被禁用", "AUTH_LOGIN_USER_DISABLED");
    ErrorCode AUTH_ACCOUNT_FORMAT_ERROR = new ErrorCode(1_004_003_003, "认证账号格式错误", "AUTH_ACCOUNT_FORMAT_ERROR"); // 账号、手机号码和邮箱三者必须有一个

    // ========== 用户标签 1-004-006-000 ==========
    ErrorCode TAG_NOT_EXISTS = new ErrorCode(1_004_006_000, "标签不存在", "TAG_NOT_EXISTS");
    ErrorCode TAG_NAME_EXISTS = new ErrorCode(1_004_006_001, "标签已存在", "TAG_NAME_EXISTS");
    ErrorCode TAG_HAS_USER = new ErrorCode(1_004_006_002, "用户标签下存在用户，无法删除", "TAG_HAS_USER");

    // ========== 积分记录 1-004-008-000 ==========
    ErrorCode POINT_RECORD_BIZ_NOT_SUPPORT = new ErrorCode(1_004_008_000, "用户积分记录业务类型不支持", "POINT_RECORD_BIZ_NOT_SUPPORT");

    // ========== 签到配置 1-004-009-000 ==========
    ErrorCode SIGN_IN_CONFIG_NOT_EXISTS = new ErrorCode(1_004_009_000, "签到规则不存在", "SIGN_IN_CONFIG_NOT_EXISTS");
    ErrorCode SIGN_IN_CONFIG_EXISTS = new ErrorCode(1_004_009_001, "签到天数规则已存在", "SIGN_IN_CONFIG_EXISTS");

    // ========== 签到记录 1-004-010-000 ==========
    ErrorCode SIGN_IN_RECORD_TODAY_EXISTS = new ErrorCode(1_004_010_000, "今日已签到，请勿重复签到", "SIGN_IN_RECORD_TODAY_EXISTS");

    // ========== 用户等级 1-004-011-000 ==========
    ErrorCode LEVEL_NOT_EXISTS = new ErrorCode(1_004_011_000, "用户等级不存在", "LEVEL_NOT_EXISTS");
    ErrorCode LEVEL_NAME_EXISTS = new ErrorCode(1_004_011_001, "用户等级名称已被使用", "LEVEL_NAME_EXISTS");
    ErrorCode LEVEL_VALUE_EXISTS = new ErrorCode(1_004_011_002, "用户等级值已被使用", "LEVEL_VALUE_EXISTS");
    ErrorCode LEVEL_EXPERIENCE_MIN = new ErrorCode(1_004_011_003, "升级经验必须大于上一个等级设置的升级经验", "LEVEL_EXPERIENCE_MIN");
    ErrorCode LEVEL_EXPERIENCE_MAX = new ErrorCode(1_004_011_004, "升级经验必须小于下一个等级设置的升级经验", "LEVEL_EXPERIENCE_MAX");
    ErrorCode LEVEL_HAS_USER = new ErrorCode(1_004_011_005, "用户等级下存在用户，无法删除", "LEVEL_HAS_USER");
    ErrorCode EXPERIENCE_BIZ_NOT_SUPPORT = new ErrorCode(1_004_011_201, "用户经验业务类型不支持", "EXPERIENCE_BIZ_NOT_SUPPORT");

    // ========== 用户分组 1-004-012-000 ==========
    ErrorCode GROUP_NOT_EXISTS = new ErrorCode(1_004_012_000, "用户分组不存在", "GROUP_NOT_EXISTS");
    ErrorCode GROUP_HAS_USER = new ErrorCode(1_004_012_001, "用户分组存在用户，无法删除", "GROUP_HAS_USER");

    // ========== 用户提现 1-004-013-000 ==========
    ErrorCode USER_WITHDRAW_NOT_EXISTS = new ErrorCode(1_004_013_001, "会员提现不存在", "USER_WITHDRAW_NOT_EXISTS");
    ErrorCode USER_BALANCE_NOT_ENOUGH = new ErrorCode(1_004_013_002, "余额不足", "USER_BALANCE_NOT_ENOUGH");
    ErrorCode USER_WITHDRAW_HAS_HANDLE = new ErrorCode(1_004_013_003, "提现已处理", "USER_WITHDRAW_HAS_HANDLE");
    ErrorCode USER_WITHDRAW_LESS_MIN_AMOUNT = new ErrorCode(1_004_013_004, "提现必须大于规定金额", "USER_WITHDRAW_LESS_MIN_AMOUNT");
    ErrorCode USER_WITHDRAW_LESS_MAX_AMOUNT = new ErrorCode(1_004_013_005, "提现必须小于规定金额", "USER_WITHDRAW_LESS_MAX_AMOUNT");
    ErrorCode USER_WITHDRAW_LESS_MAX_PROCESS = new ErrorCode(1_004_013_006, "您有笔订单正在提现中", "USER_WITHDRAW_LESS_MAX_PROCESS");
    ErrorCode USER_FROZEN_BALANCE_NOT_ENOUGH = new ErrorCode(1_004_013_007, "余额不足", "USER_FROZEN_BALANCE_NOT_ENOUGH");
    ErrorCode USER_WITHDRAW_HAS_SUCCESS = new ErrorCode(1_004_013_008, "禁止修改已经成功的订单", "USER_WITHDRAW_HAS_SUCCESS");
    ErrorCode USER_WITHDRAW_NOT_WAIT_HANDLE = new ErrorCode(1_004_013_009, "提现订单非待处理状态", "USER_WITHDRAW_NOT_WAIT_HANDLE");

    // ========== 用户资产 1-004-014-000 ==========
    ErrorCode USER_ASSETS_SPOT_NOT_EXISTS = new ErrorCode(1_004_014_001, "用户资产不存在", "USER_ASSETS_SPOT_NOT_EXISTS");

    // ========== 用户收藏交易对 1-004-016-000 ==========
    ErrorCode USER_FAVORITE_TRADE_PAIR_EXISTS = new ErrorCode(1_004_016_001, "收藏交易对已存在", "USER_FAVORITE_TRADE_PAIR_EXISTS");
    ErrorCode USER_FAVORITE_TRADE_PAIR_NOT_EXISTS = new ErrorCode(1_004_016_002, "收藏交易对不存在", "USER_FAVORITE_TRADE_PAIR_NOT_EXISTS");

    // ========== 用户充值 1-004-017-000 ==========
    ErrorCode USER_RECHARGE_NOT_EXISTS = new ErrorCode(1_004_017_001, "充值记录不存在", "USER_RECHARGE_NOT_EXISTS");
    ErrorCode USER_RECHARGE_HAS_HANDLE = new ErrorCode(1_004_017_002, "充值记录已处理", "USER_RECHARGE_HAS_HANDLE");

    // ========== 用户钱包 1-004-018-000 ==========
    ErrorCode USER_WALLET_NOT_EXISTS = new ErrorCode(1_004_018_001, "会员钱包不存在", "USER_WALLET_NOT_EXISTS");

    // ========== 用户现货订单 1-004-019-000 ==========
    ErrorCode USER_SPOT_ORDER_NOT_EXISTS = new ErrorCode(1_004_019_001, "会员现货订单不存在", "USER_SPOT_ORDER_NOT_EXISTS");

    // ========== 用户账单 1-004-020-000 ==========
    ErrorCode USER_TRANSACTIONS_NOT_EXISTS = new ErrorCode(1_004_020_001, "会员账单不存在", "USER_TRANSACTIONS_NOT_EXISTS");

    // ========== 用户合约订单 1-004-021-000 ==========
    ErrorCode USER_MARGIN_ORDER_NOT_EXISTS = new ErrorCode(1_004_021_001, "会员合约订单不存在", "USER_MARGIN_ORDER_NOT_EXISTS");

    // ========== 用户合约配置 1-004-022-000 ==========
    ErrorCode USER_MARGIN_CONFIG_NOT_EXISTS = new ErrorCode(1_004_022_001, "会员合约配置不存在", "USER_MARGIN_CONFIG_NOT_EXISTS");

    // ========== 用户认证信息 1-004-023-000 ==========
    ErrorCode USER_CERTIFICATION_NOT_EXISTS = new ErrorCode(1_004_023_001, "会员认证信息不存在", "USER_CERTIFICATION_NOT_EXISTS");
    ErrorCode USER_CERTIFICATION_BEEN_HANDLE = new ErrorCode(1_004_023_002, "会员认证信息已处理", "USER_CERTIFICATION_BEEN_HANDLE");
    ErrorCode USER_CERTIFICATION_STATUS_SUCCESS = new ErrorCode(1_004_023_003, "会员认证信息已认证", "USER_CERTIFICATION_STATUS_SUCCESS");
    ErrorCode USER_CERTIFICATION_STATUS_HANDLING = new ErrorCode(1_004_023_004, "会员认证信息审核中", "USER_CERTIFICATION_STATUS_HANDLING");
    ErrorCode USER_CERTIFICATION_NOT_VERIFY = new ErrorCode(1_004_023_005, "身份[{}],不允许操作", "USER_CERTIFICATION_NOT_VERIFY");

    ErrorCode USER_CERTIFICATION_VERIFYING = new ErrorCode(1_004_023_006, "身份认证中,不允许操作", "USER_CERTIFICATION_VERIFYING");
    ErrorCode USER_CERTIFICATION_VERIFY_FAILURE = new ErrorCode(1_004_023_007, "身份认证失敗,不允许操作", "USER_CERTIFICATION_VERIFY_FAILURE");

    // ========== 用户等级配置 1-004-024-000 ==========
    ErrorCode LEVEL_CONFIG_NOT_EXISTS = new ErrorCode(1_004_024_001, "会员等级配置不存在", "LEVEL_CONFIG_NOT_EXISTS");
    ErrorCode LEVEL_CONFIG_DEFAULT_DELETED_FORBID = new ErrorCode(1_004_024_002, "会员默认等级配置不能删除", "LEVEL_CONFIG_DEFAULT_DELETED_FORBID");
    ErrorCode LEVEL_CONFIG_NAME_EXISTS = new ErrorCode(1_004_024_003, "等级名称已存在", "LEVEL_CONFIG_NAME_EXISTS");

    // ========== 用户资金密码 1-004-025-000 ==========
    ErrorCode USER_FUND_PASSWORD_NOT_EXISTS = new ErrorCode(1_004_025_001, "您未设置资金密码,不能修改", "USER_FUND_PASSWORD_NOT_EXISTS");
    ErrorCode USER_FUND_PASSWORD_ERROR = new ErrorCode(1_004_025_002, "资金密码错误", "USER_FUND_PASSWORD_ERROR");

    // ========== 资金记录 1-004-026-000 ==========
    ErrorCode FUNDS_RECORD_NOT_EXISTS = new ErrorCode(1_004_026_001, "资金记录不存在", "FUNDS_RECORD_NOT_EXISTS");
    ErrorCode USER_RECHARGE_LESS_MAX_PROCESS = new ErrorCode(1_004_027_001, "您有[{}]笔订单正在提现中", "USER_RECHARGE_LESS_MAX_PROCESS");
    ErrorCode FUNDS_RECORD_CANNOT_DELETE = new ErrorCode(1_004_027_002, "只能删除成功和失败的订单", "");


}
