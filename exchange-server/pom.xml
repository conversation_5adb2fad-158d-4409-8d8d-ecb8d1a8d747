<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange</artifactId>
        <version>${revision}</version>
    </parent>

    <modules>
        <!-- 管理后台 API服务 -->
        <module>exchange-server-api-admin</module>
        <!-- APP API服务 -->
        <module>exchange-server-api-app</module>
    </modules>

    <artifactId>exchange-server</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>
        项目主server
    </description>

</project>