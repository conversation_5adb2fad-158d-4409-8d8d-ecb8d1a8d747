<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange-server</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>exchange-server-api-app</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-infra-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-member-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-exc-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-candle-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-trade-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!--<dependency>-->
        <!--    <groupId>com.rf.dev</groupId>-->
        <!--    <artifactId>exchange-module-i18n</artifactId>-->
        <!--    <version>${revision}</version>-->
        <!--</dependency>-->

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- MQ -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-i18n</artifactId>
            <scope>runtime</scope>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 资源文件 -->
            <!--<plugin>-->
            <!--    <groupId>org.apache.maven.plugins</groupId>-->
            <!--    <artifactId>maven-remote-resources-plugin</artifactId>-->
            <!--    <version>${maven-remote-resources-plugin.version}</version>-->
            <!--    <executions>-->
            <!--        <execution>-->
            <!--            <goals>-->
            <!--                <goal>bundle</goal>-->
            <!--            </goals>-->
            <!--        </execution>-->
            <!--    </executions>-->
            <!--    <configuration>-->
            <!--        <resourceBundles>com.rf.exchange:</resourceBundles>-->
            <!--        <includes>-->
            <!--            <include>**/*.properties</include>-->
            <!--        </includes>-->
            <!--    </configuration>-->
            <!--</plugin>-->
        </plugins>
    </build>

</project>