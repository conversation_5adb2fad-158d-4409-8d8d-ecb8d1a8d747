debug: false

server:
  tomcat:
    basedir: /opt/spring-boot/data/tmp # 指定一个目录防止tomcat的临时目录被服务器清理掉导致文件上传失败

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration # 默认 local 环境，不开启 Quartz 的自动配置
      - de.codecentric.boot.admin.server.config.AdminServerAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
      - de.codecentric.boot.admin.server.ui.config.AdminServerUiAutoConfiguration # 禁用 Spring Boot Admin 的 Server UI 的自动配置
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration # 禁用 Spring Boot Admin 的 Client 的自动配置
  application:
    name: exchange-server-api-app
  messages:
    basename: i18n/messages
    encoding: utf-8
  profiles:
    active: local
  ext:
    i18n:
      enable: true
      default-locale: en
      data-source: main
      config-code-column: code #代码列名
      config-lang-column: lang #语言列名
      config-message-column: text #文本列名
      config-table: system_i18n_text
      mark: false
      useLocale: true

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类
#    throw-exception-if-no-handler-found: true # 404 错误时抛出异常，方便统一处理
#    static-path-pattern: /static/** # 静态资源路径; 注意：如果不配置，则 throw-exception-if-no-handler-found 不生效！！！ TODO：不能配置，会导致 swagger 不生效

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

--- #################### 日志配置 ####################

logging:
  config: classpath:logback-spring-json.xml

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: false
    path: /v3/api-docs
  swagger-ui:
    enabled: false
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: false
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${exchange.info.base-package}.module.*.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

mybatis-plus-join:
  banner: false # 是否打印 mybatis plus join banner，默认true
  sub-table-logic: true # 全局启用副表逻辑删除，默认true。关闭后关联查询不会加副表逻辑删除
  ms-cache: true # 拦截器MappedStatement缓存，默认 true
  table-alias: t # 表别名(默认 t)
  logic-del-type: on # 副表逻辑删除条件的位置，支持 WHERE、ON，默认 ON

# Spring Data Redis 配置
spring:
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口
  dict-use-redis : true # 启用redis缓存
  is-enable-cloud: false # 禁用 TransType.RPC 微服务模式

--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: RFDEV # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  # Producer 配置项
  producer:
    group: ${spring.application.name}_PRODUCER # 生产者分组

--- #################### 交易所相关配置 ####################

exchange:
  info:
    version: 1.0.0
    base-package: com.rf.exchange
  rocketmq:
    topic: 'exchange-server-api-admin-biz' # 管理后台业务消息的topic
    tag-biz: 'bizMsg' # 业务消息
    contract:
      topic: ${spring.application.name}-contract # 合约业务的topic
      tag-order: 'contract-order' # 合约委托订单
      consumer-group: ${spring.application.name}-contract-consumer
    candle:
      topic: 'exchange-server-api-app-candle'
      tag-price: 'price'
      tag-price-custom: 'custom-price' # 自发币价格
      consumer-group:  ${spring.application.name}-candle-consumer

  web:
    admin-ui:
      url: http:// # Admin 管理后台 UI 的地址
  security:
    permit-all_urls:
      # 不需要登录的url配置在这个
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /ws #websocket的连接不需要认证，握手之后再去认证
  websocket:
    path: /ws # 路径
    heart-time-out: 30 # 心跳超时时间30秒
    sender-type: rocketmq # 消息发送的类型，可选值为 local、redis、rocketmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      tag-websocket: 'appWebsocket'
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
  swagger:
    title: 交易所接口文档
    description: 提供管理后台、用户 App 的所有功能
    version: ${exchange.info.version}
    url: ${exchange.web.admin-ui.url}
    email: <EMAIL>
    license: MIT
    license-url: https://www.google.com
  captcha:
    enable: true # 验证码的开关，默认为 true
  codegen:
    base-package: ${exchange.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/system/tenant/get-id-by-code # 基于code获取租户，不许带租户编号
      - /admin-api/system/tenant/get-by-website # 基于域名获取租户，不许带租户编号
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
    ignore-tables:
      - system_lang
      - system_lang_content
      - system_currency
      - system_tenant
      - system_tenant_package
      - system_tenant_server_name
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_template
      - system_mail_code
      - system_mail_log_detail
      - system_notify_template
      - system_agent_tree
      - infra_codegen_column
      - infra_codegen_table
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
      - infra_api_error_log
      - exchange_trade_pair
      - member_user_config
      - member_margin_config
      - system_role
      - system_role_menu
      - system_user_role
      - exchange_trade_transaction_time
      - candle_control_plan
      - candle_control_price_point
      - candle_control_plan_kline
    ignore-table-regex: 'data_candle_([a-z|A-Z|_])+'
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1s
    send-maximum-quantity-per-day: 10
    begin-code: 123589
    end-code: 999989
  mail-code: # 邮箱验证码相关的配置项
    expire-times: 10m
    send-frequency: 1s
    send-maximum-quantity-per-day: 10
    begin-code: 123589
    end-code: 999989
  member:
    default-avatars: # 默认头像配置
      - https://jys-java-images.s3.ap-east-1.amazonaws.com/icon/defaultavatar.png
    default-level:
      level: 1
      name: default
      icon: https://
  time-contract:
    earlier-sec: 1
  system:
    agent-data-permission:
      enable: true
      match-agent-id-tables:
        - member_user
      match-user-id-tables:
        - member_frozen
        - member_wallet
        - member_funds_record
        - trade_time_contract_record
        - member_total_day
        - member_certification
        - member_transactions