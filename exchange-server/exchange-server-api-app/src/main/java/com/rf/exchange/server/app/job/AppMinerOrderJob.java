package com.rf.exchange.server.app.job;

import com.rf.exchange.framework.xxljob.core.log.XJLog;
import com.rf.exchange.module.system.service.miner.MinerService;
import com.rf.exchange.server.app.service.AppServerInstanceService;
import com.rf.exchange.server.app.service.AppTimeContractService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-07-19
 */
@Component
public class AppMinerOrderJob {

    @Resource
    private MinerService minerService;
    /**
     * 限时合约的订单结算任务
     *
     * @throws Exception 发生异常
     */
    @XxlJob("minerOrderIncomeJob")
    public void minerOrderIncomeJob() throws Exception {
        XJLog.log("开始发放挖矿订单收益");
        try {
            // 扫描限时合约订单
            minerService.minerOrderIncomeJob();
            XxlJobHelper.handleSuccess("结束发放挖矿订单收益");
        } catch (Exception e) {
        // 打印完整堆栈信息
        XJLog.error("发放挖矿订单收益出现异常", e);
        // 返回简明异常信息给调度中心，可加 e.getMessage()
        XxlJobHelper.handleFail("异常 发放挖矿订单收益: " + e.getMessage());
    }

}

    @XxlJob("minerOrderExpireJob")
    public void minerOrderExpireJob() throws Exception {
        XJLog.log("开始执行订单自动结束");
        try {
            // 自动结束订单
            minerService.minerOrderExpireJob();
            XxlJobHelper.handleSuccess("执行结束订单自动结束");
        } catch (Exception e) {
            XJLog.error("订单自动结束 出现异常 {}", e.getMessage());
            XxlJobHelper.handleFail("异常 订单自动结束: " + e.getMessage());
        }
    }
}
