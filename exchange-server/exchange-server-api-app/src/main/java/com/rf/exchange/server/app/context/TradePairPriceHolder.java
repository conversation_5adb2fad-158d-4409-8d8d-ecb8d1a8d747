package com.rf.exchange.server.app.context;

import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentPriceRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @since 2024-07-13
 */
public class TradePairPriceHolder {

    /**
     * 所有交易对昨日收盘价格
     */
    private static final ConcurrentMap<String, CurrentPriceRespDTO> DAY_CLOSE_PRICE_MAP = new ConcurrentHashMap<>();

    /**
     * 所有交易对的当前价格
     */
    private static final ConcurrentMap<String, CurrentPriceRespDTO> ALL_PRICE_MAP = new ConcurrentHashMap<>();

    /**
     * 所有交易对的今日k线
     */
    private static final ConcurrentMap<String, TodayKlinePriceDTO> TODAY_KLINE_MAP = new ConcurrentHashMap<>();

    private static CandleCurrentPriceRedisDAO currentPriceRedisDAO;

    private static CandleTodayKlinePriceRedisDAO todayKlinePriceRedisDAO;

    public static void init(CandleCurrentPriceRedisDAO currentPriceRedisDAO, CandleTodayKlinePriceRedisDAO todayKlineRedisDAO) {
        TradePairPriceHolder.currentPriceRedisDAO = currentPriceRedisDAO;
        TradePairPriceHolder.todayKlinePriceRedisDAO = todayKlineRedisDAO;
    }

    /**
     * 设置所有价格映射
     */
    public static void reloadAllPriceMap(Map<String, CurrentPriceRespDTO> allPriceMap, Map<String, TodayKlinePriceDTO> todayKlineMap) {
        ALL_PRICE_MAP.putAll(allPriceMap);
        TODAY_KLINE_MAP.putAll(todayKlineMap);
    }

    /**
     * 获取所有价格映射的副本
     */
    public static ConcurrentMap<String, CurrentPriceRespDTO> getAllCurrentPriceMap() {
        if (ALL_PRICE_MAP.isEmpty()) {
            ALL_PRICE_MAP.putAll(currentPriceRedisDAO.getAll());
        }
        return ALL_PRICE_MAP;
    }

    /**
     * 获取k线的副本
     */
    public static ConcurrentMap<String, TodayKlinePriceDTO> getAllTodayKlineMap() {
        if (TODAY_KLINE_MAP.isEmpty()) {
            TODAY_KLINE_MAP.putAll(todayKlinePriceRedisDAO.getAll());
        }
        return TODAY_KLINE_MAP;
    }

    /**
     * 根据键获取价格
     *
     * @param code 交易对代码
     */
    public static CurrentPriceRespDTO getPrice(String code) {
        return ALL_PRICE_MAP.get(code);
    }

    /**
     * 更新或添加单个价格
     *
     * @param code  交易对代码
     * @param price 价格信息
     */
    public static void updatePrice(String code, CurrentPriceRespDTO price) {
        ALL_PRICE_MAP.put(code, price);
    }
}
