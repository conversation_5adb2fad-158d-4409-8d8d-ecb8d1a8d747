package com.rf.exchange.server.app;

import com.rf.exchange.module.candle.dal.redis.CandleCurrentPriceRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.trade.matchengine.ContractOrderMatchEngine;
import com.rf.exchange.server.app.context.TradePairPriceHolder;
import com.rf.exchange.server.app.service.AppServerInstanceService;
import jakarta.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${exchange.info.base-package}
@SpringBootApplication(scanBasePackages = {
        "${exchange.info.base-package}.server",
        "${exchange.info.base-package}.module",
        "${exchange.info.base-package}.framework.i18n"
})
public class ExchangeAppApplication implements CommandLineRunner {

    @Resource
    private CandleCurrentPriceRedisDAO priceRedisDAO;
    @Resource
    private CandleTodayKlinePriceRedisDAO todayKlinePriceRedisDAO;
    @Resource
    private AppServerInstanceService appServerInstanceService;
    @Resource
    private ContractOrderMatchEngine matchEngine;

    public static void main(String[] args) {
        SpringApplication.run(ExchangeAppApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        // 优先初始化价格避免定时任务执行时无法获取到价格数据
        TradePairPriceHolder.init(priceRedisDAO, todayKlinePriceRedisDAO);

        // 先更新一次服务器的ping date
        appServerInstanceService.updateServerPingDate();

        // 初始化合约匹配引擎使用的服务实例
        if (appServerInstanceService.tryPreemptMatchEngineInstance()) {
            // 加载所有未完成的合约委托订单
            matchEngine.loadUnfinishedContractOrder();
        }
    }
}
