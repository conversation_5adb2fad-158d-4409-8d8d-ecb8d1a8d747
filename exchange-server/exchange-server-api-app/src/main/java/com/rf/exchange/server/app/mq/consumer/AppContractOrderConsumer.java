package com.rf.exchange.server.app.mq.consumer;

import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractPositionDO;
import com.rf.exchange.module.trade.enums.ContractOrderMQMessageType;
import com.rf.exchange.module.trade.enums.ContractOrderStatusEnum;
import com.rf.exchange.module.trade.enums.ContractOrderTypeEnum;
import com.rf.exchange.module.trade.matchengine.ContractLiquidateEngine;
import com.rf.exchange.module.trade.matchengine.ContractOrderMatchEngine;
import com.rf.exchange.module.trade.mq.message.ContractOrderMQMessage;
import com.rf.exchange.module.trade.service.contract.ContractOrderService;
import com.rf.exchange.module.trade.service.contract.ContractPositionService;
import com.rf.exchange.server.app.service.AppServerInstanceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

/**
 * 合约委托订单创建消息 消费者
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Slf4j
@Service
@RocketMQMessageListener(
        topic = "${exchange.rocketmq.contract.topic}",
        consumerGroup = "${exchange.rocketmq.contract.consumer-group}",
        selectorExpression = "${exchange.rocketmq.contract.tag-order}",
        messageModel = MessageModel.BROADCASTING // 设置为广播模式，保证每个实例都能收到消息
)
public class AppContractOrderConsumer implements RocketMQListener<ContractOrderMQMessage> {

    @Resource
    private ContractOrderService contractOrderService;
    @Resource
    private ContractPositionService contractPositionService;
    @Resource
    private ContractOrderMatchEngine matchEngine;
    @Resource
    private ContractLiquidateEngine liquidateEngine;
    @Resource
    private AppServerInstanceService appServerInstanceService;

    @Override
    public void onMessage(ContractOrderMQMessage message) {
        log.info("[MQ] 收到合约委托订单消息: {}", message);

        // 如果不是MatchEngine运行的实例则返回
        if (appServerInstanceService.isMatchEngineInstance()) {
            processMessage(message);
        }
    }

    private void processMessage(ContractOrderMQMessage message) {
        TenantContextHolder.setIgnore(true);
        final ContractOrderDO order = contractOrderService.getContractOrderRecord(message.getId());
        if (order == null) {
            log.error("没有找到对应的合约委托订单 orderId:[{}]", message.getId());
            return;
        }
        if (ContractOrderMQMessageType.CANCEL.getType() == message.getMessageType() ||
                ContractOrderMQMessageType.MANUAL_STOP.getType() == message.getMessageType()) {
            matchEngine.removeContractOrderingOrder(order, null);
        } else if (ContractOrderMQMessageType.UPDATE.getType() == message.getMessageType()) {
            matchEngine.updateContractOrderingOrder(order);
        } else {
            matchEngine.addContractOrderingOrder(order);

            // 如果是强平委托订单则需要添加持仓到强平引擎中
            if (ContractOrderTypeEnum.LIQUIDATE_ORDER.getType().equals(order.getOrderType())
                    && ContractOrderStatusEnum.ORDERING.getStatus().equals(order.getOrderStatus())) {
                final ContractPositionDO position = contractPositionService.getContractPositionByNo(order.getPositionRecordNo());
                liquidateEngine.addOpenedPosition(position);
            }
        }
    }
}