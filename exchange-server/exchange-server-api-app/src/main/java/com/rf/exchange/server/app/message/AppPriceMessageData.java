package com.rf.exchange.server.app.message;

import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppPriceMessageData implements Serializable {
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 价格
     */
    private String price;
    /**
     * 开盘价格
     */
    private String openPrice;
    /**
     * 收盘价格
     */
    private String closePrice;
    /**
     * 今日最高
     */
    private String highPrice;
    /**
     * 今日最低
     */
    private String lowPrice;
    /**
     * 涨跌百分比
     */
    private String percentage;
    /**
     * 成交量
     */
    private String volume;
    /**
     * 24小时成交量
     */
    private String volume24H;
    /**
     * 成交额
     */
    private String turnOver;
    /**
     * 时间戳
     */
    private long ts;
    /**
     * 买单列表
     */
    private List<CandleOrderBookDTO> bids;
    /**
     * 卖单列表
     */
    private List<CandleOrderBookDTO> asks;
}
