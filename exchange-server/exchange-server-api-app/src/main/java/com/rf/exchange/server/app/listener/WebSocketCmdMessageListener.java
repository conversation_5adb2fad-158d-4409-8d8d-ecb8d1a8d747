package com.rf.exchange.server.app.listener;

import cn.hutool.core.bean.BeanUtil;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.security.core.filter.TokenAuthenticationFilter;
import com.rf.exchange.framework.websocket.core.listener.WebSocketMessageListener;
import com.rf.exchange.framework.websocket.core.message.WebSocketRecvMessage;
import com.rf.exchange.framework.websocket.core.session.WebSocketSessionManager;
import com.rf.exchange.framework.websocket.core.util.WebSocketRespMessageUtil;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgCmdEnum;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgCodeEnum;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgTypeEnum;
import com.rf.exchange.server.app.message.AppSubscribeMessageData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

/**
 * <AUTHOR>
 * @since 2024-07-10
 */
@Slf4j
@Component
public class WebSocketCmdMessageListener implements WebSocketMessageListener<WebSocketRecvMessage> {

    @Resource
    private WebSocketSessionManager sessionManager;
    @Resource
    private TokenAuthenticationFilter tokenAuthenticationFilter;

    @Override
    public void onMessage(WebSocketSession session, WebSocketRecvMessage message, Long tenantId) {
        log.info("Recv message: {} session:{}", message, session.getId());
        if (WebSocketMsgCmdEnum.AUTH.getCmd() == message.getCmd()) {
            String token = (String) message.getData().get("token");
            final LoginUser loginUser = tokenAuthenticationFilter.verifyToken(token);
            if (loginUser != null) {
                sessionManager.addAuthSession(session, loginUser);
                WebSocketRespMessageUtil.sendResponse(session, WebSocketMsgCodeEnum.SUCCESS.getCode(), "");
            } else {
                WebSocketRespMessageUtil.sendResponse(session, WebSocketMsgCodeEnum.TOKEN_INVALID.getCode(), "");
            }
        } else if (WebSocketMsgCmdEnum.SUBSCRIBE.getCmd() == message.getCmd()) {
            AppSubscribeMessageData subscribe = BeanUtil.toBean(message.getData(), AppSubscribeMessageData.class);
            sessionManager.subscribeTradePair(session.getId(), subscribe.getCode());
            WebSocketRespMessageUtil.sendResponse(session, WebSocketMsgCodeEnum.SUCCESS.getCode(), "");
        }
    }

    @Override
    public String getType() {
        return WebSocketMsgTypeEnum.CMD.getType();
    }
}
