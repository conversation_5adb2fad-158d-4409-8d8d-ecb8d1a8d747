package com.rf.exchange.server.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.rf.exchange.server.app.redis.AppServerPingRedisDAO;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-08-26
 */
@Slf4j
@Service
public class AppServerInstanceServiceImpl implements AppServerInstanceService {

    private static String SERVER_INSTANCE_ID;

    @Resource
    private AppServerPingRedisDAO appServerPingRedisDAO;

    @PostConstruct
    public void init() {
        // 不要开启VPN，否则无法获取mac地址
        final String localMacAddress = NetUtil.getLocalMacAddress();
        final String instanceKey = SecureUtil.md5(localMacAddress);
        log.info("当前APP服务的实例Key:{} mac:[{}]", instanceKey, localMacAddress);
        SERVER_INSTANCE_ID = instanceKey;
    }

    @Override
    public boolean tryPreemptMatchEngineInstance() {
        final String lastMatchEngineInstance = appServerPingRedisDAO.getLastMatchEngineInstance();
        if (StrUtil.isNotEmpty(lastMatchEngineInstance)) {
            final Set<String> validInstances = getValidServerInstances();
            if (CollUtil.isEmpty(validInstances)) {
                appServerPingRedisDAO.setMatchEngineInstance(SERVER_INSTANCE_ID);
                return true;
            } else {
                if (validInstances.contains(lastMatchEngineInstance)) {
                    return isMatchEngineInstance();
                } else {
                    appServerPingRedisDAO.setMatchEngineInstance(SERVER_INSTANCE_ID);
                    return true;
                }
            }
        } else {
            appServerPingRedisDAO.setMatchEngineInstance(SERVER_INSTANCE_ID);
            return true;
        }
    }

    @Override
    public boolean isMatchEngineInstance() {
        final String lastMatchEngineInstance = getLastMatchEngineInstance();
        if (StrUtil.isEmpty(lastMatchEngineInstance)) {
            return false;
        }
        return lastMatchEngineInstance.equals(SERVER_INSTANCE_ID);
    }

    @Override
    public String getLastMatchEngineInstance() {
        return appServerPingRedisDAO.getLastMatchEngineInstance();
    }

    @Override
    public String getCurrentInstanceIdentify() {
        return SERVER_INSTANCE_ID;
    }

    @Override
    public Set<String> getValidServerInstances() {
        final Map<String, Long> allDates = appServerPingRedisDAO.getAllPingDates();
        long nowTS = System.currentTimeMillis();
        Set<String> validKeys = allDates.keySet();
        Set<String> unValidKeys = new HashSet<>(validKeys.size());
        for (Map.Entry<String, Long> entry : allDates.entrySet()) {
            final Long lastPingTS = entry.getValue();
            //log.info("服务实例:[{}] ping:[{}] now:[{}]", entry.getKey(), lastPingTS, nowTS);
            // 如果server的最后ping date与当前时间戳相差2秒则认为非有效的server实例
            if (lastPingTS + 2000 <= nowTS) {
                unValidKeys.add(entry.getKey());
            }
        }
        appServerPingRedisDAO.cleanUp(unValidKeys);
        validKeys.removeAll(unValidKeys);
        return validKeys;
    }

    @Override
    public int getValidServerInstanceCount() {
        return getValidServerInstances().size();
    }

    @Override
    public Map<String, Long> getAllServerInstances() {
        return appServerPingRedisDAO.getAllPingDates();
    }

    @Override
    public void updateServerPingDate() {
        if (SERVER_INSTANCE_ID.isEmpty()) {
            log.error("当前服务的实例为空");
            return;
        }
        appServerPingRedisDAO.setPingDate(SERVER_INSTANCE_ID);
    }

    @Override
    public void deleteServerPingDate() {
        log.info("删除服务实例的Ping date: {}", SERVER_INSTANCE_ID);
        appServerPingRedisDAO.deletePingDate(SERVER_INSTANCE_ID);
    }
}
