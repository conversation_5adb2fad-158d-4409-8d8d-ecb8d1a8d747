package com.rf.exchange.server.app.mq.consumer;

import com.rf.exchange.module.trade.matchengine.ContractLiquidateEngine;
import com.rf.exchange.module.trade.matchengine.ContractOrderMatchEngine;
import com.rf.exchange.server.app.mq.message.CandlePriceMessage;
import com.rf.exchange.server.app.service.AppServerInstanceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-08-26
 */
@Slf4j
@Service
@RocketMQMessageListener(
        topic = "${exchange.rocketmq.candle.topic}",
        selectorExpression = "${exchange.rocketmq.candle.tag-price} || ${exchange.rocketmq.candle.tag-price-custom}",
        consumerGroup = "${exchange.rocketmq.candle.consumer-group}",
        messageModel = MessageModel.BROADCASTING // 设置为广播模式，保证每个实例都能收到消息
)
public class AppCandlePriceConsumer implements RocketMQListener<CandlePriceMessage> {

    @Resource
    private ContractOrderMatchEngine matchEngine;
    @Resource
    private ContractLiquidateEngine liquidateEngine;
    @Resource
    private AppServerInstanceService serverInstanceService;

    @Override
    public void onMessage(CandlePriceMessage message) {
        try {
            BigDecimal price = new BigDecimal(String.valueOf(message.getPrice()));
            matchEngine.updateCandlePrice(message.getCode(), price);

            // 如果是MatchEngine运行的实例才重新计算持仓的强平价格
            if (serverInstanceService.isMatchEngineInstance()) {
                liquidateEngine.recalculateLiquidationPrice(message.getCode(), price);
                matchEngine.handlePriceChanged(message.getCode(), price);
            }
        } catch (Exception ex) {
            log.error("[MQ] 价格消息消费失败 msgPrice:[{}] message:[{}] error:[{}]", message.getPrice(), message, ex.getMessage(), ex);
        }
    }
}
