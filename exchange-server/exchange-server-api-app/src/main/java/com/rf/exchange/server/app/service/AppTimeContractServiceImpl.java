package com.rf.exchange.server.app.service;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.websocket.core.message.WebSocketPushMessage;
import com.rf.exchange.framework.websocket.core.session.WebSocketSessionManager;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgCmdEnum;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgTypeEnum;
import com.rf.exchange.framework.xxljob.core.log.XJLog;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.infra.api.websocket.WebSocketSenderApi;
import com.rf.exchange.module.member.api.user.MemberUserApi;
import com.rf.exchange.module.member.api.user.dto.MemberUserConfigRespDTO;
import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import com.rf.exchange.module.trade.api.TradeApi;
import com.rf.exchange.module.trade.api.dto.TimeContractRecordDTO;
import com.rf.exchange.module.trade.enums.TimeContractOrderStatusEnum;
import com.rf.exchange.server.app.context.TradePairPriceHolder;
import com.rf.exchange.server.app.message.AppTimeContractOrderMessageData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @since 2024-07-13
 */
@Slf4j
@Service
public class AppTimeContractServiceImpl implements AppTimeContractService {

    // 提前多少秒中开始结算
    @Value("${exchange.time-contract.earlier-sec:1}")
    private Integer earlierSec;
    @Resource
    private TradeApi tradeApi;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private WebSocketSenderApi webSocketSenderApi;
    @Resource
    private WebSocketSessionManager sessionManager;

    @Override
    public void asyncScanAllTimeContractOrder() {
        XJLog.info("进入 异步限时合约订单结算流程");
        TenantContextHolder.setIgnore(true);
        List<TimeContractRecordDTO> recordList = tradeApi.getWaitingTimeContractRecordList();
        if (CollectionUtil.isEmpty(recordList)) {
            XJLog.info("没有任何需要结算的订单");
            return;
        }
        XJLog.info("未结算的限时合约订单数量:{}", recordList.size());

        // 订单状态为非等待结算的订单集合
        Set<String> notWaitOrderNoSet = new HashSet<>(recordList.size());
        long now = System.currentTimeMillis() / 1000;
        for (TimeContractRecordDTO record : recordList) {
            if (TimeContractOrderStatusEnum.WAIT_DRAW.getStatus() != record.getTradeStatus()) {
                // 如果是订单已经结算成功则移除
                if (TimeContractOrderStatusEnum.DRAW_SUCCESS.getStatus() == record.getTradeStatus()) {
                    notWaitOrderNoSet.add(record.getTradeNo());
                }
                XJLog.warn("跳过结算 限时合约订单 userId:{} recordNo:{} status:{}", record.getUserId(), record.getTradeNo(), record.getTradeStatus());
                continue;
            }
            // 获取用户的输赢配置
            MemberUserConfigRespDTO userConfig = memberUserApi.getUserConfigCached(record.getUserId());
            if (((userConfig == null || userConfig.getProfitType() == null) && record.getProfitType() == null)
                    || record.getProfitRate() == null) {
                XJLog.error("无法计算输赢 输赢配置不全 userId:{} recordNo:{}", record.getUserId(), record.getTradeNo());
                continue;
            }
            ConcurrentMap<String, CurrentPriceRespDTO> allPriceMap = TradePairPriceHolder.getAllCurrentPriceMap();
            CurrentPriceRespDTO priceDto = allPriceMap.get(record.getCode());
            if (priceDto != null) {
                // 如果没有到限时合约的结束时间，则跳过
                if ((record.getSendTime() / 1000 + record.getDuration() - earlierSec <= now)) {
                    beginDrawOrder(record, userConfig, priceDto);
                }
            } else {
                XJLog.log("错误 交易对的价格无法完成计算 userId:{} recordNo:{}", record.getUserId(), record.getTradeNo());
            }
        }

        // 从redis中移除已经不属于等待状态的订单号
        tradeApi.removeRedisNotWaitOrderNos(notWaitOrderNoSet);
        TenantContextHolder.clear();
        XJLog.log("退出 异步限时合约订单结算流程");
    }

    /**
     * 开始计算订单的盈利结果
     *
     * @param record     限时合约记录信息
     * @param userConfig 用户配置
     * @param priceDto   价格信息
     */
    private void beginDrawOrder(TimeContractRecordDTO record, MemberUserConfigRespDTO userConfig, CurrentPriceRespDTO priceDto) {
        // 更新订单的状态为“开奖中”
        record.setTradeStatus(TimeContractOrderStatusEnum.DRAWING.getStatus());
        tradeApi.updateTimeContractRecordStatus(record);
        // 优先获取订单中的输赢配置
        if (!MemberConfigProfitTypeEnum.NONE.getType().equals(record.getProfitType())) {

            final TimeContractRecordDTO resultRecord = tradeApi.calculateTimeContractRecordResult(record, priceDto.getCurrentPrice(), record.getProfitType());
            sendTimeContractRecordStatusChangeMessage(resultRecord);
            XJLog.log("结束 计算限时订单的盈亏金额 使用订单盈亏配置 userID:{} orderNo:{} profitType:{}", record.getUserId(), record.getTradeNo(), record.getProfitType());
        } else {
            if (userConfig == null) {
                XJLog.error("无法计算输赢 用户输赢配置不全 userId:{} recordNo:{}", record.getUserId(), record.getTradeNo());
                record.setTradeStatus(TimeContractOrderStatusEnum.DRAW_FAIL.getStatus());
                record.setFailReason("无法获取正确的盈亏配置无法计算");
                tradeApi.updateTimeContractRecordStatus(record);
            } else {
                final TimeContractRecordDTO resultRecord = tradeApi.calculateTimeContractRecordResult(record, priceDto.getCurrentPrice(), userConfig.getProfitType());
                sendTimeContractRecordStatusChangeMessage(resultRecord);
                XJLog.log("结束 计算限时订单的盈亏金额 使用用户盈亏配置 userId:{} orderNo:{} now:{} profitType:{}", record.getUserId(), record.getTradeNo(), userConfig.getProfitType());
            }
        }
    }

    /**
     * 发送限时合约订单状态改变的消息
     *
     * @param recordDTO 限时合约记录信息
     */
    private void sendTimeContractRecordStatusChangeMessage(TimeContractRecordDTO recordDTO) {
        if (TimeContractOrderStatusEnum.WAIT_DRAW.getStatus() != recordDTO.getTradeStatus()) {
            final Collection<String> sessionIdSet = sessionManager.getSessionIds(recordDTO.getTenantId(), UserTypeEnum.MEMBER.getValue(), recordDTO.getUserId());
            final AppTimeContractOrderMessageData messageData = BeanUtils.toBean(recordDTO, AppTimeContractOrderMessageData.class);
            WebSocketPushMessage<AppTimeContractOrderMessageData> message = new WebSocketPushMessage<>();
            message.setType(WebSocketMsgTypeEnum.PUSH.getType());
            message.setCmd(WebSocketMsgCmdEnum.TIME_CONTRACT_ORDER_STATUS.getCmd());
            message.setTs(System.currentTimeMillis());
            message.setData(messageData);

            for (String sessionId : sessionIdSet) {
                webSocketSenderApi.sendObject(sessionId, WebSocketMsgTypeEnum.PUSH.getType(), message);
            }
        }
    }
}
