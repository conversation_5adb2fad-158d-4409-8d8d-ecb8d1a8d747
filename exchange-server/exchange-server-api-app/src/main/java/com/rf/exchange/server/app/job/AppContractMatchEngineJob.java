package com.rf.exchange.server.app.job;

import com.rf.exchange.framework.xxljob.core.log.XJLog;
import com.rf.exchange.module.trade.matchengine.ContractOrderMatchEngine;
import com.rf.exchange.server.app.service.AppServerInstanceService;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-09-02
 */
@Component
public class AppContractMatchEngineJob {

    @Resource
    private AppServerInstanceService appServerInstanceService;
    @Resource
    private ContractOrderMatchEngine matchEngine;

    /**
     * 合约的撮合引擎抢占实例任务
     */
    @XxlJob("preemptMatchEnginJob")
    public void preemptMatchEnginJob() {
        appServerInstanceService.updateServerPingDate();

        // 之前MatchEngine运行的实例
        final String lastInstance = appServerInstanceService.getLastMatchEngineInstance();

        // 抢占MatchEngine的服务运行实例
        if (appServerInstanceService.tryPreemptMatchEngineInstance()) {
            XJLog.info("抢占到MatchEngine服务实例 当前实例标识:[{}]", appServerInstanceService.getCurrentInstanceIdentify());
            // 加载所有未完成的合约委托订单
            matchEngine.loadUnfinishedOrderIfNeed();
        }
    }
}
