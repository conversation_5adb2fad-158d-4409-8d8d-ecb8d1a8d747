server:
  port: 48080

--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: admin # 控制台管理用户名和密码
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
          db-type: mysql
        wall:
          config:
            multi-statement-allow: true
        slf4j:
          enabled: true
          statement-log-enabled: true
          statement-create-after-log-enabled: false
          statement-close-after-log-enabled: false
          result-set-open-after-log-enabled: false
          result-set-close-after-log-enabled: false
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 0 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ******************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: exchange
          password: 'MhxcadY@1sdi^sd'
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ******************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: exchange
          password: 'MhxcadY@1sdi^sd'

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6379 # 端口
      database: 0 # 数据库索引
      password: 'MxhBsd@12&s*^d' # 密码，建议生产环境开启

--- #################### 接口文档相关配置 ####################
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui
  default-flat-param-object: true

knife4j:
  enable: true
  setting:
    language: zh_cn

--- #################### xxl-job相关配置 ####################
xxl:
  job:
    enable: true
    access-token: 'xT4/idpxifGEqgZf11an28eDqVuXZqF2'
    admin:
      addresses: http://127.0.0.1:8989/xxl-job-admin
    executor:
      app-name: ${spring.application.name}
      address:
      port:
      ip:
      log-path: ${logging.file.path}/xxl-job/jobhandler

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  name-server: 127.0.0.1:9876 # RocketMQ Namesrv

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: health # 只暴露必要的端点
  endpoint:
    health:
      show-details: never
    metrics:
      enabled: false
    prometheus:
      enabled: false

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  config: classpath:logback-spring-plain.xml
  file:
    path: ${user.home}/logs # 日志文件目录
    name: ${logging.file.path}/${spring.application.name}.log # 日志文件名，全路径
  level:
    # MyBatis相关日志
    com.baomidou.mybatisplus: debug
    org.mybatis: debug
    java.sql: debug
    java.sql.Connection: debug
    java.sql.Statement: debug
    java.sql.PreparedStatement: debug
    java.sql.ResultSet: debug
    # Druid相关日志
    druid.sql: debug
    druid.sql.DataSource: debug
    druid.sql.Connection: debug
    druid.sql.Statement: debug
    druid.sql.ResultSet: debug
    # 项目相关日志
    com.rf.exchange: debug
    # Spring相关日志
    org.springframework.jdbc.core: debug
    org.springframework.transaction: debug

--- #################### 交易所相关配置 ####################

# 交易所配置项，设置当前项目所有自定义的配置
exchange:
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  pay:
    order-notify-url: http://yunai.natapp1.cc/admin-api/pay/notify/order # 支付渠道的【支付】回调地址
    refund-notify-url: http://yunai.natapp1.cc/admin-api/pay/notify/refund # 支付渠道的【退款】回调地址
