package com.rf.exchange.server.admin.job;

import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.tenant.core.aop.TenantIgnore;
import com.rf.exchange.module.member.controller.admin.total.vo.MemberTotalDaySaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO;
import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordOpTypeEnum;
import com.rf.exchange.module.member.service.fundsrecord.MemberFundsRecordService;
import com.rf.exchange.module.member.service.total.MemberTotalDayService;
import com.rf.exchange.module.member.service.transactions.MemberTransactionsService;
import com.rf.exchange.module.member.service.user.MemberUserService;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentTreeDO;
import com.rf.exchange.module.system.service.agent.AgentService;
import com.rf.exchange.module.system.service.agent.AgentStatisticService;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;
import com.rf.exchange.module.trade.service.timecontract.TimeContractRecordService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AdminMemberUserJob {
    @Resource
    private MemberUserService memberUserService;
    @Resource
    private AgentService agentService;
    @Resource
    private AgentStatisticService agentStatisticService;
    @Resource
    private MemberTransactionsService memberTransactionsService;
    @Resource
    private MemberTotalDayService memberTotalDayService;
    @Resource
    private TimeContractRecordService timeContractRecordService;
    @Resource
    private MemberFundsRecordService memberFundsRecordService;

    /**
     * 统计实际余额
     *
     * @throws Exception 异常信息
     */
    @TenantIgnore
    @XxlJob("totalUserBalance")
    public void totalActualBalance() throws Exception {
        XxlJobHelper.log("统计余额 分片索引:{} 总分片:{}", XxlJobHelper.getShardIndex(), XxlJobHelper.getShardTotal());
        //所有会员的余额
        List<MemberUserDO> memberUserList = memberUserService.getAllMemberBalance();
        Map<Long, BigDecimal> agentBalanceMap = memberUserList.stream()
                .collect(Collectors.groupingBy(
                        MemberUserDO::getAgentId,
                        Collectors.mapping(
                                MemberUserDO::getUsdtBalance,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));

        List<AgentTreeDO> treeList = agentService.getAllTree();
        Map<Long, List<Long>> childrenMap = new HashMap<>();
        // 构建子节点映射
        for (AgentTreeDO tree : treeList) {
            childrenMap.computeIfAbsent(tree.getAncestor(), k -> new ArrayList<>()).add(tree.getDescendant());
        }

        //统计每个代理会员及子代理会员余额
        for (Map.Entry<Long, List<Long>> item : childrenMap.entrySet()) {
            BigDecimal totalBalance = calculateTotalPoints(item.getValue(), agentBalanceMap);
            agentStatisticService.setTotalBalance(item.getKey(), totalBalance);
        }
    }

    public static BigDecimal calculateTotalPoints(List<Long> agentSet, Map<Long, BigDecimal> agentPointsMap) {
        BigDecimal total = BigDecimal.ZERO;

        for (Long agentId : agentSet) {
            BigDecimal points = agentPointsMap.get(agentId);
            if (points != null) {
                total = total.add(points);
            }
        }

        return total;
    }

    /**
     * 统计当天余额
     */
    @TenantIgnore
    @XxlJob("totalUserDay")
    public void totalDayBalance() {
        // 所有会员的余额，如果没有发生账变时，就用会员余额做为当日余额
        List<MemberUserDO> memberUserList = memberUserService.getAllMemberBalance();
        Long startTime = DateUtils.getStartOfYesterday();
        Long endTime = DateUtils.getEndOfYesterday();

        // 昨日账变记录
        List<MemberTransactionsDO> transactionsDOList = memberTransactionsService.getLastTransactionByRange(startTime, endTime);

        // 昨日充值提现记录
        List<MemberFundsRecordDO> fundsRecordDOList = memberFundsRecordService.getMemberFundsRecordByRange(startTime, endTime);

        // 昨日交易订单
        List<TimeContractRecordDO> contractRecordDOList = timeContractRecordService.getListByRange(startTime, endTime);

        // 使用并行流来处理会员数据
        memberUserList.parallelStream().forEach(item -> {
            // 构建初始请求对象
            MemberTotalDaySaveReqVO reqVO = MemberTotalDaySaveReqVO.builder()
                    .userId(item.getId())
                    .username(item.getUsername())
                    .agentId(item.getAgentId())
                    .agentName(item.getAgentName())
                    .totalTime(startTime)
                    .usdtBalance(item.getUsdtBalance())
                    .profitAmount(BigDecimal.ZERO)
                    .recharge(BigDecimal.ZERO)
                    .withdraw(BigDecimal.ZERO)
                    .orderCount(0).build();

            // 查找昨天是否有账变
            Optional<MemberTransactionsDO> transactionsDO = transactionsDOList.parallelStream()
                    .filter(t -> Objects.equals(t.getUserId(), item.getId()))
                    .findFirst();
            transactionsDO.ifPresent(memberTransactionsDO -> reqVO.setUsdtBalance(memberTransactionsDO.getAfterBalance()));

            // 统计充值
            BigDecimal recharge = fundsRecordDOList.parallelStream()
                    .filter(f -> Objects.equals(f.getUserId(), item.getId()) && Objects.equals(f.getOpType(), MemberFundsRecordOpTypeEnum.RECHARGE.getType()))
                    .map(MemberFundsRecordDO::getUsdtAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 统计提现
            BigDecimal withdraw = fundsRecordDOList.parallelStream()
                    .filter(f -> Objects.equals(f.getUserId(), item.getId()) && Objects.equals(f.getOpType(), MemberFundsRecordOpTypeEnum.WITHDRAW.getType()))
                    .map(MemberFundsRecordDO::getUsdtAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 统计实际利润
            List<TimeContractRecordDO> myContractRecordList = contractRecordDOList.parallelStream()
                    .filter(f -> Objects.equals(f.getUserId(), item.getId()))
                    .toList();
            BigDecimal profit = myContractRecordList.parallelStream()
                    .map(TimeContractRecordDO::getProfitResult)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            reqVO.setRecharge(recharge);
            reqVO.setWithdraw(withdraw);
            reqVO.setProfitAmount(profit);
            reqVO.setOrderCount(myContractRecordList.size());

            memberTotalDayService.createDayTotal(reqVO);
        });
    }
}
