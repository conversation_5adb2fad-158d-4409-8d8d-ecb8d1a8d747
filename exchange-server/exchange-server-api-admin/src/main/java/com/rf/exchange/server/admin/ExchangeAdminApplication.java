package com.rf.exchange.server.admin;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;


/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${exchange.info.base-package}
@SpringBootApplication(scanBasePackages = {
        "${exchange.info.base-package}.server",
        "${exchange.info.base-package}.module",
        "${exchange.info.base-package}.framework.i18n"
})
public class ExchangeAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(ExchangeAdminApplication.class, args);
    }
}
