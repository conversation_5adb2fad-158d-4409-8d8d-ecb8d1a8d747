package com.rf.exchange.server.admin.mq.consumer;

import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.websocket.core.message.WebSocketPushMessage;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgTypeEnum;
import com.rf.exchange.module.infra.api.websocket.WebSocketSenderApi;
import com.rf.exchange.module.member.message.user.MemberUserMQMessage;
import com.rf.exchange.module.member.mq.message.AdminBizMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

/**
 * 真正发送到websocket的消息处理
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@Service
@RocketMQMessageListener(
        topic = "${exchange.rocketmq.topic}",
        consumerGroup = "${exchange.rocketmq.consumer-group}",
        selectorExpression = "${exchange.rocketmq.tag-biz}"
)
public class MemberUserMQBizConsumer implements RocketMQListener<MemberUserMQMessage> {

    @Resource
    private WebSocketSenderApi webSocketSenderApi;

    @Override
    public void onMessage(MemberUserMQMessage mqMessage) {
        Long tenantId = mqMessage.getTenantId();
        if (tenantId == null) {
            return;
        }
        WebSocketPushMessage<AdminBizMessage> wsMessage = new WebSocketPushMessage<>();
        AdminBizMessage data = new AdminBizMessage();
        data.setUserId(mqMessage.getUserId());
        wsMessage.setData(data);
        wsMessage.setCmd(mqMessage.getCmdId());
        wsMessage.setType(WebSocketMsgTypeEnum.PUSH.getType());
        wsMessage.setTs(System.currentTimeMillis());
        webSocketSenderApi.sendObject(mqMessage.getTenantId(), UserTypeEnum.ADMIN.getValue(), WebSocketMsgTypeEnum.PUSH.getType(), wsMessage);
    }
}
