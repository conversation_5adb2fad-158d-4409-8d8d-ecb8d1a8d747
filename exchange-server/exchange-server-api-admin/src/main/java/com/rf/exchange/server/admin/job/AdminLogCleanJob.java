package com.rf.exchange.server.admin.job;

import com.rf.exchange.framework.tenant.core.aop.TenantIgnore;
import com.rf.exchange.module.infra.service.logger.ApiAccessLogService;
import com.rf.exchange.module.infra.service.logger.ApiErrorLogService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Component
public class AdminLogCleanJob {
    /**
     * 清理超过（7）天的日志
     */
    private static final Integer JOB_CLEAN_RETAIN_DAY = 7;

    /**
     * 每次删除间隔的条数，如果值太高可能会造成数据库的压力过大
     */
    private static final Integer DELETE_LIMIT = 100;

    @Resource
    private ApiAccessLogService apiAccessLogService;
    @Resource
    private ApiErrorLogService apiErrorLogService;

    /**
     * 访问日志清理定时任务
     */
    @TenantIgnore
    @XxlJob("cleanAccessLogJob")
    public void executeCleanApiAccessLog() {
        XxlJobHelper.log("开始 定时清理访问日志");
        Integer count = apiAccessLogService.cleanAccessLog(JOB_CLEAN_RETAIN_DAY, DELETE_LIMIT);
        XxlJobHelper.log("结束 清理访问日志 ({}) 个", count);
    }

    /**
     * 错误日志清理任务
     */
    @TenantIgnore
    @XxlJob("cleanErrorLog")
    public void executeCleanErrorLog() {
        XxlJobHelper.log("开始 定时清理错误日志");
        Integer count = apiErrorLogService.cleanErrorLog(JOB_CLEAN_RETAIN_DAY, DELETE_LIMIT);
        XxlJobHelper.log("结束 清理错误日志 ({}) 个", count);
    }
}
