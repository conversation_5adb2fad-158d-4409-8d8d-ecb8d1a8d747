package com.rf.exchange.server.admin.job;

import com.rf.exchange.framework.websocket.core.session.WebSocketSessionManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 踢出过期ws的过期用户
 * <p>
 * 广播的定时任务
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Slf4j
@Component
public class AdminWebSocketJob {

    @Resource
    private WebSocketSessionManager sessionManager;

    @XxlJob("wsAdminKickOutJob")
    public void wsAdminKickOutJob() throws Exception {
        XxlJobHelper.log("wsAdminKickOutJob 清理ws超时连接 分片索引:{} 总分片:{}", XxlJobHelper.getShardIndex(), XxlJobHelper.getShardTotal());
        sessionManager.kickOutAllExpireSessions();
    }
}