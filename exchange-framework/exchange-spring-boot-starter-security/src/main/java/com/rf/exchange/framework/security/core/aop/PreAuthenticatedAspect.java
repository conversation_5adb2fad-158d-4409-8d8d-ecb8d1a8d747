package com.rf.exchange.framework.security.core.aop;

import com.rf.exchange.framework.common.util.google.auth.Authenticator;
import com.rf.exchange.framework.common.util.number.NumberUtils;
import com.rf.exchange.framework.security.core.annotations.AuthCode;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.lang.reflect.Field;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.GOOGLE_AUTH_CODE_ERROR;
import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;

@Aspect
@Slf4j
public class PreAuthenticatedAspect {

    @Around("@annotation(preAuthenticated)")
    public Object around(ProceedingJoinPoint joinPoint, PreAuthenticated preAuthenticated) throws Throwable {
        if (SecurityFrameworkUtils.getLoginUser() == null) {
            throw exception(UNAUTHORIZED);
        }
        if (preAuthenticated.needAuthCode()) {
            Object[] args = joinPoint.getArgs();
            for (Object arg : args) {
                Field[] fields = arg.getClass().getDeclaredFields();
                for (Field field : fields) {
                    if (field.isAnnotationPresent(AuthCode.class)) {
                        field.setAccessible(true);
                        Long authCodeValue = NumberUtils.parseLong((String)field.get(arg));
                        if (authCodeValue == null) {
                            throw exception(GOOGLE_AUTH_CODE_ERROR);
                        }
                        String secret = SecurityFrameworkUtils.getLoginUser().getSecret();
                        boolean suc = Authenticator.checkCode(secret, authCodeValue, System.currentTimeMillis());
                        if (!suc) {
                            throw exception(GOOGLE_AUTH_CODE_ERROR);
                        }
                    }
                }
            }
        }
        return joinPoint.proceed();
    }

}
