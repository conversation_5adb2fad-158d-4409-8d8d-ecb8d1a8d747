package com.rf.exchange.framework.security.core;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.system.UserInfo;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.*;

/**
 * 登录用户信息
 *
 * <AUTHOR>
 */
@Data
public class LoginUser {

    public static final String INFO_KEY_USERNAME = "username";
    public static final String INFO_KEY_NICKNAME = "nickname";
    public static final String INFO_KEY_DEPT_ID = "deptId";
    public static final String INFO_KEY_AGENT_ID = "agentId";
    public static final String INFO_KEY_AGENT_DESCENDANT_ID = "descendantId";
    public static final String INFO_KEY_ROLE_ID = "roleId";

    /**
     * 用户编号
     */
    private Long id;
    /**
     * 代理id
     */
    private Long agentId;
    /**
     * 子代理id集合
     */
    private Collection<Long> descendantAgentIds;
    /**
     * 用户类型
     * <p>
     * 关联 {@link UserTypeEnum}
     */
    private Integer userType;
    /**
     * 额外的用户信息
     */
    private Map<String, String> info;
    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 谷歌验证密钥
     */
    private String secret;
    /**
     * 授权范围
     */
    private List<String> scopes;
    /**
     * 登录用户的角色id编号数组
     */
    public List<Long> roleIds;

    // ========== 上下文 ==========
    /**
     * 上下文字段，不进行持久化
     * <p>
     * 1. 用于基于 LoginUser 维度的临时缓存
     */
    @JsonIgnore
    private Map<String, Object> context;

    public void setContext(String key, Object value) {
        if (context == null) {
            context = new HashMap<>();
        }
        context.put(key, value);
    }

    public <T> T getContext(String key, Class<T> type) {
        return MapUtil.get(context, key, type);
    }

    public void removeContext(String key) {
        context.remove(key);
    }
}
