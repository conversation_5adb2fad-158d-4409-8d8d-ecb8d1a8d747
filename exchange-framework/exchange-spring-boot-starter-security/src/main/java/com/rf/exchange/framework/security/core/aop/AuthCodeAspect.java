package com.rf.exchange.framework.security.core.aop;

import com.rf.exchange.framework.common.util.google.auth.Authenticator;
import com.rf.exchange.framework.security.core.annotations.AuthCode;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.lang.reflect.Field;
import java.lang.reflect.Parameter;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.GOOGLE_AUTH_CODE_ERROR;
import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;

@Aspect
@Slf4j
public class AuthCodeAspect {
    @Before("@annotation(authCode)")
    public void before(JoinPoint joinPoint, AuthCode authCode) throws Throwable {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            Field[] fields = arg.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(AuthCode.class)) {
                    field.setAccessible(true);
                    Long authCodeValue = (Long) field.get(arg);
                    if (authCodeValue == null) {
                        throw exception(GOOGLE_AUTH_CODE_ERROR);
                    }
                    String secret = SecurityFrameworkUtils.getLoginUser().getSecret();
                    boolean suc = Authenticator.checkCode(secret, authCodeValue, System.currentTimeMillis());
                    if (!suc) {
                        throw exception(GOOGLE_AUTH_CODE_ERROR);
                    }
                }
            }
        }
    }
}
