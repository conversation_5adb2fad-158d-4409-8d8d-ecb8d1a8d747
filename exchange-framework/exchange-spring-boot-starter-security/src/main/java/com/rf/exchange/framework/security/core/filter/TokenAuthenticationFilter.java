package com.rf.exchange.framework.security.core.filter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.exception.ServiceException;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.common.util.servlet.ServletUtils;
import com.rf.exchange.framework.security.config.SecurityProperties;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import com.rf.exchange.framework.web.core.handler.GlobalExceptionHandler;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.system.api.oauth2.OAuth2TokenApi;
import com.rf.exchange.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * Token 过滤器，验证 token 的有效性
 * 验证通过后，获得 {@link LoginUser} 信息，并加入到 Spring Security 上下文
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    private final SecurityProperties securityProperties;

    private final GlobalExceptionHandler globalExceptionHandler;

    private final OAuth2TokenApi oauth2TokenApi;

    @Override
    @SuppressWarnings("NullableProblems")
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotEmpty(token)) {
            Integer userType = WebFrameworkUtils.getLoginUserType(request);
            try {
                // 基于 token 构建登录用户
                LoginUser loginUser = buildLoginUserByToken(request, token, userType);
                //// 1.2 模拟 Login 功能，方便日常开发调试
                //if (loginUser == null) {
                //    loginUser = mockLoginUser(request, token, userType);
                //}
                // 2. 设置当前用户
                if (loginUser != null) {
                    SecurityFrameworkUtils.setLoginUser(loginUser, request);
                }
            } catch (Throwable ex) {
                CommonResult<?> result = globalExceptionHandler.allExceptionHandler(request, ex);
                ServletUtils.writeJSON(response, result);
                return;
            }
        }

        // 继续过滤链
        chain.doFilter(request, response);
    }

    public LoginUser verifyToken(String token) {
        if (StrUtil.isEmpty(token)) {
            return null;
        }
        try {
            OAuth2AccessTokenCheckRespDTO accessToken = oauth2TokenApi.checkAccessToken(token);
            if (accessToken == null) {
                return null;
            }
            // 构建登录用户
            return new LoginUser()
                    .setId(accessToken.getUserId())
                    .setUserType(accessToken.getUserType())
                    .setInfo(accessToken.getUserInfo()) // 额外的用户信息
                    .setTenantId(accessToken.getTenantId())
                    .setScopes(accessToken.getScopes());
        } catch (ServiceException serviceException) {
            // 校验 Token 不通过时，考虑到一些接口是无需登录的，所以直接返回 null 即可
            return null;
        }
    }

    private LoginUser buildLoginUserByToken(HttpServletRequest request, String token, Integer userType) {
        try {
            OAuth2AccessTokenCheckRespDTO accessToken = oauth2TokenApi.checkAccessToken(token);
            if (accessToken == null) {
                return null;
            }
            // 发送请求的用户类型
            WebFrameworkUtils.setRequestUserType(request, accessToken.getUserType());
            // 用户类型不匹配，无权限
            // 注意：只有 /admin-api/* 和 /app-api/* 有 userType，才需要比对用户类型
            // 类似 WebSocket 的 /ws/* 连接地址，是不需要比对用户类型的
            if (userType != null && ObjectUtil.notEqual(accessToken.getUserType(), userType)) {
                throw new AccessDeniedException("错误的用户类型");
            }
            // 角色id数组
            List<Long> roleIds = new ArrayList<>();
            // 代理id（不是会员所属的代理id，而是登录账号绑定的代理id）
            Long agentId = null;
            // 子代理id集合
            Collection<Long> childAgentIds = Collections.emptySet();
            Map<String, String> userInfo = accessToken.getUserInfo();
            if (CollectionUtil.isNotEmpty(userInfo)) {
                String agentIdStr = userInfo.get(LoginUser.INFO_KEY_AGENT_ID);
                if (StrUtil.isNotEmpty(agentIdStr)) {
                    agentId = Long.valueOf(agentIdStr);
                }
                String descendantIdStr = userInfo.get(LoginUser.INFO_KEY_AGENT_DESCENDANT_ID);
                if (StrUtil.isNotEmpty(descendantIdStr)) {
                    childAgentIds = JsonUtils.parseArray(descendantIdStr, Long.class);
                }
                String roleIdStr = userInfo.get(LoginUser.INFO_KEY_ROLE_ID);
                if (StrUtil.isNotEmpty(roleIdStr)) {
                    roleIds = JsonUtils.parseArray(roleIdStr, Long.class);
                }
            }
            // 构建登录用户
            return new LoginUser()
                    .setAgentId(agentId)
                    .setDescendantAgentIds(childAgentIds)
                    .setRoleIds(roleIds)
                    .setId(accessToken.getUserId())
                    .setUserType(accessToken.getUserType())
                    .setInfo(accessToken.getUserInfo()) // 额外的用户信息
                    .setTenantId(accessToken.getTenantId())
                    .setScopes(accessToken.getScopes());

        } catch (ServiceException serviceException) {
            // 校验 Token 不通过时，考虑到一些接口是无需登录的，所以直接返回 null 即可
            return null;
        }
    }

    /**
     * 模拟登录用户，方便日常开发调试
     *
     * 注意，在线上环境下，一定要关闭该功能！！！
     *
     * @param request 请求
     * @param token 模拟的 token，格式为 {@link SecurityProperties#getMockSecret()} + 用户编号
     * @param userType 用户类型
     * @return 模拟的 LoginUser
     */
    private LoginUser mockLoginUser(HttpServletRequest request, String token, Integer userType) {
        return null;
        //if (!securityProperties.getMockEnable()) {
        //    return null;
        //}
        //// 必须以 mockSecret 开头
        //if (!token.startsWith(securityProperties.getMockSecret())) {
        //    return null;
        //}
        //// 构建模拟用户
        //Long userId = Long.valueOf(token.substring(securityProperties.getMockSecret().length()));
        //return new LoginUser().setId(userId).setUserType(userType)
        //        .setTenantId(WebFrameworkUtils.getHeaderTenantId(request));
    }

}
