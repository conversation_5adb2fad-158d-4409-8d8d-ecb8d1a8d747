package com.rf.exchange.framework.mq.redis.config;

import com.rf.exchange.framework.mq.rocketmq.core.producer.RocketMQProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024-07-21
 */
@Configuration
public class ExchangeRocketMQAutoConfiguration {

    @Bean
    public RocketMQProducer rocketMQProducer() {
        return new RocketMQProducer();
    }
}
