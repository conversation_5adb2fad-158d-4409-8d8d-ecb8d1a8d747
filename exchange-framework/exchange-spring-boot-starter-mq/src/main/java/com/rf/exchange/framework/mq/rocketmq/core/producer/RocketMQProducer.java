package com.rf.exchange.framework.mq.rocketmq.core.producer;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@Component
public class RocketMQProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    public void send(String topic, String tag, String msg) {
        sendObject(topic, tag, msg);
    }

    public void sendObject(String topic, String tag, Object message) {
        String destTopic = topic + ":" + tag;
        SendResult sendResult = rocketMQTemplate.syncSend(destTopic, message);
        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            log.error("RocketMQ 发送消息失败 status:[{}] msgId:[{}] message:[{}]", sendResult.getSendStatus(), sendResult.getMsgId(), message);
        }
    }

    public void asyncSend(String topic, String tag, String msg) {
        asyncSendObject(topic, tag, msg);
    }

    public void asyncSendObject(String topic, String tag, Object message) {
        String destTopic = topic + ":" + tag;
        rocketMQTemplate.asyncSend(destTopic, message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("RocketMQ 发送消息成功 topic:{}, tag:{}, msg:{}", topic, tag, message);
            }

            @Override
            public void onException(Throwable throwable) {
                log.info("RocketMQ 发送消息失败 topic:{}, tag:{}, msg:{}, error:{}", topic, tag, message, throwable.getMessage());
            }
        });
    }
}
