COUNTRY_TH=Tha\u00EFlande
COUNTRY_JP=Japon
COUNTRY_KR=Cor\u00E9e du Sud
COUNTRY_PH=Philippines
COUNTRY_VN=Vietnam
COUNTRY_CN=Chine
COUNTRY_GB=Royaume-Uni
COUNTRY_FR=France
COUNTRY_US=\u00C9tats-Unis
COUNTRY_ES=Espagne
COUNTRY_DE=Allemagne
COUNTRY_RU=Russie
COUNTRY_CA=Canada
COUNTRY_TR=Turquie
COUNTRY_PT=Portugal
COUNTRY_MA=Maroc
COUNTRY_DZ=Alg\u00E9rie
COUNTRY_IT=Italie
COUNTRY_CO=Colombie
COUNTRY_MX=Mexique
COUNTRY_CH=Suisse
COUNTRY_BE=Belgique
COUNTRY_AR=Argentine
COUNTRY_NO=Norv\u00E8ge
COUNTRY_HK=Hong Kong
BANK=Carte Bancaire
CRYPTO=Cryptomonnaie
USER_CERTIFIED_STATUS_NOT=Non Certifi\u00E9
USER_CERTIFIED_STATUS_HANDLING=En cours de certification
USER_CERTIFIED_STATUS_SUCCESS=Certifi\u00E9
USER_CERTIFIED_STATUS_FAIL=\u00C9chec de la certification
ACCOUNT_NOT_EMPTY=Le compte ne peut pas \u00EAtre vide
PASSWORD_NOT_EMPTY=Le mot de passe ne peut pas \u00EAtre vide
PASSWORD_NOT_LENGTH_6_16=La longueur du mot de passe doit \u00EAtre de 6-16 caract\u00E8res
PASSWORD_FORMATTER_ERROR=Le format du mot de passe doit contenir des chiffres et des lettres
ACCOUNT_TYPE_ERROR=Type de compte incorrect
MAIL_FORMATTER_ERROR=Format d'email incorrect
MOBILE_FORMATTER_ERROR=Format de num\u00E9ro de t\u00E9l\u00E9phone incorrect
AREA_NOT_EMPTY=La zone ne peut pas \u00EAtre vide
MAIL_SCENE_ERROR=Erreur de sc\u00E9nario d'envoi d'email
SMS_SCENE_ERROR=Erreur de sc\u00E9nario d'envoi de SMS
REAL_NAME_NOT_EMPTY=Le nom r\u00E9el ne peut pas \u00EAtre vide
CERTIFICATION_TYPE_ERROR=Type de certification d'identit\u00E9 incorrect
CERTIFICATION_CODE_NOT_EMPTY=Le code du document ne peut pas \u00EAtre vide
CERTIFICATION_FRONT_NOT_EMPTY=La photo recto du document ne peut pas \u00EAtre vide
CERTIFICATION_BACK_NOT_EMPTY=La photo verso du document ne peut pas \u00EAtre vide
TRADE_PAIR_NOT_EMPTY=Veuillez s\u00E9lectionner une paire de trading
FUNDS_RECORD_OP_TYPE=Type de fonds incorrect
CURRENCY_NOT_EMPTY=Veuillez s\u00E9lectionner une devise
PAY_METHOD_ERROR=Veuillez s\u00E9lectionner une m\u00E9thode de paiement
RECHARGE_AMOUNT_ERROR=Veuillez saisir le montant de la recharge
WITHDRAW_AMOUNT_ERROR=Veuillez saisir le montant du retrait
FUNDS_PASSWORD_ERROR=Mot de passe des fonds incorrect
WALLET_NOT_EMPTY=Veuillez s\u00E9lectionner un portefeuille
AUTH_CODE_NOT_EMPTY=Veuillez saisir le code de v\u00E9rification
AVATAR_FORMATTER_ERROR=Veuillez s\u00E9lectionner un avatar
WALLET_TYPE_ERROR=Type de portefeuille incorrect
WALLET_NAME_NOT_EMPTY=Veuillez saisir le nom du portefeuille
WALLET_ACCOUNT_NOT_EMPTY=Veuillez saisir le compte du portefeuille
WALLET_TYPE_NAME_NOT_EMPTY=Veuillez saisir le nom de la banque
ASSET_Type_ERROR=Type d'actif incorrect
KEY_NOT_EMPTY=Le mot-cl\u00E9 ne peut pas \u00EAtre vide
AMOUNT_NOT_EMPTY=Erreur de montant
TRADE_DIRECT_ERROR=Direction de trading incorrecte
TRADE_DURATION_ERROR=Limite de temps de trading incorrecte
TRADE_SEND_TIME_ERROR=Heure d'envoi de la transaction incorrecte
TRADE_PRICE_TIME_ERROR=Heure du prix de la transaction incorrecte
TRADE_PAGE_TYPE_ERROR=Cat\u00E9gorie incorrecte
SUCCESS=Succ\u00E8s
WAITHANDLE=En cours de traitement
FAILURE=\u00C9chec
PENDING=En cours de traitement
BAD_REQUEST=Requ\u00EAte incorrecte
UNKNOW_AUTHORIZED=Type d'autorisation inconnu [{}]
TOKEN_NOT_SUPPORT_MODE=L'interface Token ne supporte pas le mode d'autorisation [{}]
CLIENT_ERROR=client_id ou client_secret n'ont pas \u00E9t\u00E9 transmis correctement
TOKEN_REFRESH_INVALID=Token de rafra\u00EEchissement invalide
TOKEN_REFRESH_CLIENT_ERROR=ID client du token de rafra\u00EEchissement incorrect
TOKEN_REFRESH_EXPIRE=Token de rafra\u00EEchissement expir\u00E9
TOKEN_NOT_EXISTS=Token d'acc\u00E8s n'existe pas
TOKEN_EXPIRE=Token d'acc\u00E8s expir\u00E9
GRANT_RESPONSE_TYPE_ERROR=La valeur du param\u00E8tre response_type n'autorise que code et token
UNAUTHORIZED=Compte non connect\u00E9
FORBIDDEN=Pas de permission pour cette op\u00E9ration
NOT_FOUND=La ressource demand\u00E9e n'existe pas
METHOD_NOT_ALLOWED=M\u00E9thode de requ\u00EAte non autoris\u00E9e
LOCKED=Verrouill\u00E9
TOO_MANY_REQUESTS=Trop de requ\u00EAtes, veuillez r\u00E9essayer plus tard
INTERNAL_SERVER_ERROR=Erreur syst\u00E8me
NOT_IMPLEMENTED=Fonctionnalit\u00E9 non impl\u00E9ment\u00E9e/non activ\u00E9e
REPEATED_REQUESTS=Requ\u00EAte r\u00E9p\u00E9t\u00E9e, veuillez r\u00E9essayer plus tard
DEMO_DENY=Mode d\u00E9mo, op\u00E9rations d'\u00E9criture interdites
VALUE_ERROR=Erreur de valeur
GOOGLE_AUTH_NOT_BIND=Authentificateur Google non li\u00E9
GOOGLE_AUTH_CODE_ERROR=Code d'authentificateur Google incorrect
SCHEDULER_JOB_STOP=[T\u00E2che Programm\u00E9e - D\u00E9sactiv\u00E9e][Consultez https://doc.iocoder.cn/job/ pour activer]
CANDLE_TABLE_NAME_NOT_AVAILABLE=Nom de table K-line invalide
CANDLE_BAR_VALUE_ERROR=Param\u00E8tre bar invalide
CANDLE_PRICE_ERROR=Erreur lors de l'obtention du prix
TRADE_PAIR_NOT_EXISTS=La paire de trading n'existe pas
TRADE_PAIR_EXISTS=La paire de trading existe d\u00E9j\u00E0
TRADE_PAIR_TENANT_NOT_EXISTS=Le locataire ne poss\u00E8de pas cette paire de trading
TRADE_PAIR_TENANT_EXISTS=La paire de trading du locataire existe d\u00E9j\u00E0
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=La configuration du type d'actif de la paire de trading du locataire n'existe pas
TRADE_TENANT_NEED_DEFAULT=Il doit y avoir une paire de trading par d\u00E9faut du locataire
TRADE_TENANT_DUPLICATE_DEFAULT=La paire de trading par d\u00E9faut du locataire existe d\u00E9j\u00E0
CONFIG_NOT_EXISTS=La configuration du param\u00E8tre n'existe pas
CONFIG_KEY_DUPLICATE=Cl\u00E9 de configuration de param\u00E8tre dupliqu\u00E9e
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=Impossible de supprimer la configuration de param\u00E8tre de type syst\u00E8me int\u00E9gr\u00E9
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=\u00C9chec de l'obtention de la configuration du param\u00E8tre, raison : il n'est pas permis d'obtenir une configuration invisible
JOB_NOT_EXISTS=La t\u00E2che programm\u00E9e n'existe pas
JOB_HANDLER_EXISTS=Le gestionnaire de la t\u00E2che programm\u00E9e existe d\u00E9j\u00E0
JOB_CHANGE_STATUS_INVALID=Il n'est permis de modifier que vers un statut ouvert ou ferm\u00E9
JOB_CHANGE_STATUS_EQUALS=La t\u00E2che programm\u00E9e est d\u00E9j\u00E0 dans ce statut, pas besoin de modifier
JOB_UPDATE_ONLY_NORMAL_STATUS=Seules les t\u00E2ches avec un statut ouvert peuvent \u00EAtre modifi\u00E9es
JOB_CRON_EXPRESSION_VALID=Expression CRON incorrecte
JOB_HANDLER_BEAN_NOT_EXISTS=Le Bean du gestionnaire de la t\u00E2che programm\u00E9e n'existe pas
JOB_HANDLER_BEAN_TYPE_ERROR=Type du Bean du gestionnaire de la t\u00E2che programm\u00E9e incorrect, n'impl\u00E9mente pas l'interface JobHandler
API_ERROR_LOG_NOT_FOUND=Le journal d'erreur de l'API n'existe pas
API_ERROR_LOG_PROCESSED=Le journal d'erreur de l'API a d\u00E9j\u00E0 \u00E9t\u00E9 trait\u00E9
FILE_PATH_EXISTS=Le chemin du fichier existe d\u00E9j\u00E0
FILE_NOT_EXISTS=Le fichier n'existe pas
FILE_IS_EMPTY=Le fichier est vide
CODEGEN_TABLE_EXISTS=La d\u00E9finition de la table existe d\u00E9j\u00E0
CODEGEN_IMPORT_TABLE_NULL=La table import\u00E9e n'existe pas
CODEGEN_IMPORT_COLUMNS_NULL=Les champs import\u00E9s n'existent pas
CODEGEN_TABLE_NOT_EXISTS=La d\u00E9finition de la table n'existe pas
CODEGEN_COLUMN_NOT_EXISTS=La d\u00E9finition du champ n'existe pas
CODEGEN_SYNC_COLUMNS_NULL=Les champs synchronis\u00E9s n'existent pas
CODEGEN_SYNC_NONE_CHANGE=\u00C9chec de la synchronisation, aucun changement
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Commentaire de la table de base de donn\u00E9es non renseign\u00E9
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Commentaire du champ de la table de base de donn\u00E9es ({}) non renseign\u00E9
CODEGEN_MASTER_TABLE_NOT_EXISTS=La d\u00E9finition de la table principale (id={}) n'existe pas, veuillez v\u00E9rifier
CODEGEN_SUB_COLUMN_NOT_EXISTS=Le champ de la sous-table (id={}) n'existe pas, veuillez v\u00E9rifier
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=\u00C9chec de la g\u00E9n\u00E9ration de code de la table principale, raison : elle n'a pas de sous-table
FILE_CONFIG_NOT_EXISTS=La configuration du fichier n'existe pas
FILE_CONFIG_DELETE_FAIL_MASTER=Cette configuration de fichier ne peut pas \u00EAtre supprim\u00E9e, raison : c'est la configuration principale, la supprimer emp\u00EAchera le t\u00E9l\u00E9chargement de fichiers
DATA_SOURCE_CONFIG_NOT_EXISTS=La configuration de la source de donn\u00E9es n'existe pas
DATA_SOURCE_CONFIG_NOT_OK=Configuration de la source de donn\u00E9es incorrecte, impossible de se connecter
DEMO01_CONTACT_NOT_EXISTS=Le contact d'exemple n'existe pas
DEMO02_CATEGORY_NOT_EXISTS=La cat\u00E9gorie d'exemple n'existe pas
DEMO02_CATEGORY_EXITS_CHILDREN=Il existe des sous-cat\u00E9gories d'exemple, impossible de supprimer
DEMO02_CATEGORY_PARENT_NOT_EXITS=La cat\u00E9gorie parent d'exemple n'existe pas
DEMO02_CATEGORY_PARENT_ERROR=Impossible de se d\u00E9finir comme cat\u00E9gorie parent d'exemple
DEMO02_CATEGORY_NAME_DUPLICATE=Il existe d\u00E9j\u00E0 une cat\u00E9gorie d'exemple avec ce nom
DEMO02_CATEGORY_PARENT_IS_CHILD=Impossible de d\u00E9finir sa propre sous-cat\u00E9gorie d'exemple comme cat\u00E9gorie parent d'exemple
DEMO03_STUDENT_NOT_EXISTS=L'\u00E9tudiant n'existe pas
DEMO03_GRADE_NOT_EXISTS=La classe de l'\u00E9tudiant n'existe pas
DEMO03_GRADE_EXISTS=La classe de l'\u00E9tudiant existe d\u00E9j\u00E0
AREA_NOT_EXISTS=Cette zone n'existe pas
USER_NOT_EXISTS=L'utilisateur n'existe pas
USER_MOBILE_NOT_EXISTS=Num\u00E9ro de t\u00E9l\u00E9phone non enregistr\u00E9 pour un utilisateur
USER_MOBILE_USED=Ce num\u00E9ro de t\u00E9l\u00E9phone est d\u00E9j\u00E0 utilis\u00E9
USER_ACCOUNT_USED=Le compte est d\u00E9j\u00E0 utilis\u00E9
USER_EMAIL_USED=L'email est d\u00E9j\u00E0 utilis\u00E9
USER_POINT_NOT_ENOUGH=Solde de points de l'utilisateur insuffisant
USER_OLD_PASSWORD_NOT_MATCH=Ancien mot de passe incorrect
USER_BALANCE_ERROR=Erreur de solde de l'utilisateur
USER_CONFIG_NOT_SUPPORTED=\u00C9l\u00E9ment de configuration utilisateur temporairement non pris en charge
AUTH_LOGIN_BAD_CREDENTIALS=\u00C9chec de la connexion, compte ou mot de passe incorrect
AUTH_LOGIN_USER_DISABLED=\u00C9chec de la connexion, compte d\u00E9sactiv\u00E9
AUTH_ACCOUNT_FORMAT_ERROR=Format du compte d'authentification incorrect
TAG_NOT_EXISTS=Le tag n'existe pas
TAG_NAME_EXISTS=Le tag existe d\u00E9j\u00E0
TAG_HAS_USER=Il y a des utilisateurs dans le tag utilisateur, impossible de supprimer
POINT_RECORD_BIZ_NOT_SUPPORT=Type de m\u00E9tier d'enregistrement de points utilisateur non pris en charge
SIGN_IN_CONFIG_NOT_EXISTS=La r\u00E8gle de pointage n'existe pas
SIGN_IN_CONFIG_EXISTS=La r\u00E8gle de jours de pointage existe d\u00E9j\u00E0
SIGN_IN_RECORD_TODAY_EXISTS=Pointage d\u00E9j\u00E0 effectu\u00E9 aujourd'hui, ne r\u00E9p\u00E9tez pas le pointage
LEVEL_NOT_EXISTS=Le niveau utilisateur n'existe pas
LEVEL_NAME_EXISTS=Le nom du niveau utilisateur est d\u00E9j\u00E0 utilis\u00E9
LEVEL_VALUE_EXISTS=La valeur du niveau utilisateur est d\u00E9j\u00E0 utilis\u00E9e
LEVEL_EXPERIENCE_MIN=L'exp\u00E9rience de mise \u00E0 niveau doit \u00EAtre sup\u00E9rieure \u00E0 l'exp\u00E9rience de mise \u00E0 niveau d\u00E9finie au niveau pr\u00E9c\u00E9dent
LEVEL_EXPERIENCE_MAX=L'exp\u00E9rience de mise \u00E0 niveau doit \u00EAtre inf\u00E9rieure \u00E0 l'exp\u00E9rience de mise \u00E0 niveau d\u00E9finie au niveau suivant
LEVEL_HAS_USER=Il y a des utilisateurs dans le niveau utilisateur, impossible de supprimer
EXPERIENCE_BIZ_NOT_SUPPORT=Type de m\u00E9tier d'exp\u00E9rience utilisateur non pris en charge
GROUP_NOT_EXISTS=Le groupe d'utilisateurs n'existe pas
GROUP_HAS_USER=Le groupe d'utilisateurs contient des utilisateurs, impossible de supprimer
USER_WITHDRAW_NOT_EXISTS=Le retrait du membre n'existe pas
USER_BALANCE_NOT_ENOUGH=Solde insuffisant
USER_WITHDRAW_HAS_HANDLE=Retrait d\u00E9j\u00E0 trait\u00E9
USER_WITHDRAW_LESS_MIN_AMOUNT=Le retrait doit \u00EAtre sup\u00E9rieur \u00E0 [{}]
USER_WITHDRAW_LESS_MAX_AMOUNT=Le retrait doit \u00EAtre inf\u00E9rieur \u00E0 [{}]
USER_WITHDRAW_LESS_MAX_PROCESS=Vous avez une commande en cours de traitement pour retrait
USER_FROZEN_BALANCE_NOT_ENOUGH=Solde insuffisant
USER_ASSETS_SPOT_NOT_EXISTS=L'actif de l'utilisateur n'existe pas
USER_FAVORITE_TRADE_PAIR_EXISTS=La paire de trading favorite existe d\u00E9j\u00E0
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=La paire de trading favorite n'existe pas
USER_RECHARGE_NOT_EXISTS=L'enregistrement de recharge n'existe pas
USER_RECHARGE_HAS_HANDLE=L'enregistrement de recharge a d\u00E9j\u00E0 \u00E9t\u00E9 trait\u00E9
USER_WALLET_NOT_EXISTS=Le portefeuille du membre n'existe pas
USER_SPOT_ORDER_NOT_EXISTS=L'ordre spot du membre n'existe pas
USER_TRANSACTIONS_NOT_EXISTS=La transaction du membre n'existe pas
USER_MARGIN_ORDER_NOT_EXISTS=L'ordre de contrat du membre n'existe pas
USER_MARGIN_CONFIG_NOT_EXISTS=La configuration de contrat du membre n'existe pas
USER_CERTIFICATION_NOT_EXISTS=Les informations de certification du membre n'existent pas
USER_CERTIFICATION_BEEN_HANDLE=Les informations de certification du membre ont d\u00E9j\u00E0 \u00E9t\u00E9 trait\u00E9es
USER_CERTIFICATION_STATUS_SUCCESS=Les informations de certification du membre ont d\u00E9j\u00E0 \u00E9t\u00E9 certifi\u00E9es
USER_CERTIFICATION_STATUS_HANDLING=Les informations de certification du membre sont en cours d'audit
USER_CERTIFICATION_NOT_VERIFY=Identit\u00E9 [{}], op\u00E9ration non autoris\u00E9e
USER_CERTIFICATION_VERIFYING=Certification d'identit\u00E9 en cours, op\u00E9ration non autoris\u00E9e
USER_CERTIFICATION_VERIFY_FAILURE=\u00C9chec de la certification d'identit\u00E9, op\u00E9ration non autoris\u00E9e
LEVEL_CONFIG_NOT_EXISTS=La configuration de niveau de membre n'existe pas
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=La configuration de niveau par d\u00E9faut du membre ne peut pas \u00EAtre supprim\u00E9e
LEVEL_CONFIG_NAME_EXISTS=Le nom du niveau existe d\u00E9j\u00E0
USER_FUND_PASSWORD_NOT_EXISTS=Vous n'avez pas d\u00E9fini de mot de passe de fonds, impossible de modifier
USER_FUND_PASSWORD_ERROR=Mot de passe de fonds incorrect
FUNDS_RECORD_NOT_EXISTS=L'enregistrement de fonds n'existe pas
USER_RECHARGE_LESS_MAX_PROCESS=Vous avez [{}] commandes en cours de traitement pour retrait
AUTH_LOGIN_CAPTCHA_CODE_ERROR=Code de v\u00E9rification incorrect, raison : {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token expir\u00E9
AUTH_MOBILE_NOT_EXISTS=Le num\u00E9ro de t\u00E9l\u00E9phone n'existe pas
MENU_NAME_DUPLICATE=Il existe d\u00E9j\u00E0 un menu avec ce nom
MENU_PARENT_NOT_EXISTS=Le menu parent n'existe pas
MENU_PARENT_ERROR=Impossible de se d\u00E9finir comme menu parent
MENU_NOT_EXISTS=Le menu n'existe pas
MENU_EXISTS_CHILDREN=Il y a des sous-menus, impossible de supprimer
MENU_PARENT_NOT_DIR_OR_MENU=Le type du menu parent doit \u00EAtre r\u00E9pertoire ou menu
ROLE_NOT_EXISTS=Le r\u00F4le n'existe pas
ROLE_NAME_DUPLICATE=Il existe d\u00E9j\u00E0 un r\u00F4le avec le nom [{}]
ROLE_CODE_DUPLICATE=Il existe d\u00E9j\u00E0 un r\u00F4le avec le code [{}]
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=Impossible d'op\u00E9rer sur des r\u00F4les de type syst\u00E8me int\u00E9gr\u00E9
ROLE_IS_DISABLE=Le r\u00F4le avec le nom [{}] a \u00E9t\u00E9 d\u00E9sactiv\u00E9
ROLE_ADMIN_CODE_ERROR=Le code [{}] ne peut pas \u00EAtre utilis\u00E9
USER_USERNAME_EXISTS=Le compte existe d\u00E9j\u00E0
USER_MOBILE_EXISTS=Le num\u00E9ro de t\u00E9l\u00E9phone existe d\u00E9j\u00E0
USER_EMAIL_EXISTS=L'email existe d\u00E9j\u00E0
USER_IMPORT_LIST_IS_EMPTY=Les donn\u00E9es utilisateur import\u00E9es ne peuvent pas \u00EAtre vides
USER_PASSWORD_FAILED=\u00C9chec de la validation du mot de passe utilisateur
USER_IS_DISABLE=L'utilisateur avec le nom [{}] a \u00E9t\u00E9 d\u00E9sactiv\u00E9
USER_COUNT_MAX=\u00C9chec de la cr\u00E9ation d'utilisateur, raison : d\u00E9passe le quota maximum du locataire [{}]
DEPT_NAME_DUPLICATE=Il existe d\u00E9j\u00E0 un d\u00E9partement avec ce nom
DEPT_PARENT_NOT_EXITS=Le d\u00E9partement parent n'existe pas
DEPT_NOT_FOUND=Le d\u00E9partement actuel n'existe pas
DEPT_EXITS_CHILDREN=Il y a des sous-d\u00E9partements, impossible de supprimer
DEPT_PARENT_ERROR=Impossible de se d\u00E9finir comme d\u00E9partement parent
DEPT_EXISTS_USER=Il y a des employ\u00E9s dans le d\u00E9partement, impossible de supprimer
DEPT_NOT_ENABLE=Le d\u00E9partement [{}] n'est pas en statut actif, il n'est pas permis de s\u00E9lectionner
DEPT_PARENT_IS_CHILD=Impossible de d\u00E9finir son propre sous-d\u00E9partement comme d\u00E9partement parent
POST_NOT_FOUND=Le poste actuel n'existe pas
POST_NOT_ENABLE=Le poste [{}] n'est pas en statut actif, il n'est pas permis de s\u00E9lectionner
POST_NAME_DUPLICATE=Il existe d\u00E9j\u00E0 un poste avec ce nom
POST_CODE_DUPLICATE=Il existe d\u00E9j\u00E0 un poste avec cet identifiant
DICT_TYPE_NOT_EXISTS=Le type de dictionnaire actuel n'existe pas
DICT_TYPE_NOT_ENABLE=Le type de dictionnaire n'est pas en statut actif, il n'est pas permis de s\u00E9lectionner
DICT_TYPE_NAME_DUPLICATE=Il existe d\u00E9j\u00E0 un type de dictionnaire avec ce nom
DICT_TYPE_TYPE_DUPLICATE=Il existe d\u00E9j\u00E0 un type de dictionnaire avec ce type
DICT_TYPE_HAS_CHILDREN=Impossible de supprimer, ce type de dictionnaire a encore des donn\u00E9es de dictionnaire
DICT_DATA_NOT_EXISTS=Les donn\u00E9es de dictionnaire actuelles n'existent pas
DICT_DATA_NOT_ENABLE=Les donn\u00E9es de dictionnaire [{}] ne sont pas en statut actif, il n'est pas permis de s\u00E9lectionner
DICT_DATA_VALUE_DUPLICATE=Il existe d\u00E9j\u00E0 des donn\u00E9es de dictionnaire avec cette valeur
NOTICE_NOT_FOUND=L'annonce actuelle n'existe pas
SMS_CHANNEL_NOT_EXISTS=Le canal SMS n'existe pas
SMS_CHANNEL_DISABLE=Le canal SMS n'est pas en statut actif, il n'est pas permis de s\u00E9lectionner
SMS_CHANNEL_HAS_CHILDREN=Impossible de supprimer, ce canal SMS a encore des mod\u00E8les SMS
SMS_TEMPLATE_NOT_EXISTS=Le mod\u00E8le SMS n'existe pas
SMS_TEMPLATE_CODE_DUPLICATE=Il existe d\u00E9j\u00E0 un mod\u00E8le SMS avec le code [{}]
SMS_TEMPLATE_API_ERROR=\u00C9chec de l'appel de l'API du mod\u00E8le SMS, raison : {}
SMS_TEMPLATE_API_AUDIT_CHECKING=Le mod\u00E8le SMS de l'API ne peut pas \u00EAtre utilis\u00E9, raison : en cours d'audit
SMS_TEMPLATE_API_AUDIT_FAIL=Le mod\u00E8le SMS de l'API ne peut pas \u00EAtre utilis\u00E9, raison : audit non pass\u00E9, {}
SMS_TEMPLATE_API_NOT_FOUND=Le mod\u00E8le SMS de l'API ne peut pas \u00EAtre utilis\u00E9, raison : mod\u00E8le n'existe pas
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Param\u00E8tre du mod\u00E8le [{}] manquant
SMS_CODE_NOT_FOUND=Le code de v\u00E9rification n'existe pas
SMS_CODE_EXPIRED=Le code de v\u00E9rification a expir\u00E9
SMS_CODE_USED=Le code de v\u00E9rification a d\u00E9j\u00E0 \u00E9t\u00E9 utilis\u00E9
SMS_CODE_NOT_CORRECT=Code de v\u00E9rification incorrect
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=D\u00E9pass\u00E9 la quantit\u00E9 quotidienne d'envoi de SMS
SMS_CODE_SEND_TOO_FAST=Envoi de SMS trop fr\u00E9quent
TENANT_NOT_EXISTS=Le locataire n'existe pas
TENANT_DISABLE=Le locataire avec le nom [{}] a \u00E9t\u00E9 d\u00E9sactiv\u00E9
TENANT_EXPIRE=Le locataire avec le nom [{}] a expir\u00E9
TENANT_CAN_NOT_UPDATE_SYSTEM=Le locataire syst\u00E8me ne peut pas \u00EAtre modifi\u00E9, supprim\u00E9 ou autres op\u00E9rations !
TENANT_CODE_DUPLICATE=Le locataire avec le code [{}] existe d\u00E9j\u00E0
TENANT_WEBSITE_DUPLICATE=Le locataire avec le domaine [{}] existe d\u00E9j\u00E0
TENANT_PACKAGE_NOT_EXISTS=Le package du locataire n'existe pas
TENANT_PACKAGE_USED=Le locataire utilise ce package, red\u00E9finissez le package du locataire avant d'essayer de supprimer
TENANT_PACKAGE_DISABLE=Le package du locataire avec le nom [{}] a \u00E9t\u00E9 d\u00E9sactiv\u00E9
OAUTH2_CLIENT_NOT_EXISTS=Le client OAuth2 n'existe pas
OAUTH2_CLIENT_EXISTS=L'ID du client OAuth2 existe d\u00E9j\u00E0
OAUTH2_CLIENT_DISABLE=Le client OAuth2 a \u00E9t\u00E9 d\u00E9sactiv\u00E9
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=Ne prend pas en charge ce type d'autorisation
OAUTH2_CLIENT_SCOPE_OVER=Port\u00E9e d'autorisation trop large
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=redirect_uri invalide : [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=client_secret invalide : {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id ne correspond pas
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=redirect_uri ne correspond pas
OAUTH2_GRANT_STATE_MISMATCH=state ne correspond pas
OAUTH2_GRANT_CODE_NOT_EXISTS=code n'existe pas
OAUTH2_CODE_NOT_EXISTS=code n'existe pas
OAUTH2_CODE_EXPIRE=Code de v\u00E9rification expir\u00E9
MAIL_ACCOUNT_NOT_EXISTS=Le compte email n'existe pas
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=Impossible de supprimer, ce compte email a encore des mod\u00E8les d'email
MAIL_TEMPLATE_NOT_EXISTS=Le mod\u00E8le d'email n'existe pas
MAIL_TEMPLATE_CODE_EXISTS=Le code du mod\u00E8le d'email [{}] existe d\u00E9j\u00E0
MAIL_SEND_TEMPLATE_PARAM_MISS=Param\u00E8tre du mod\u00E8le [{}] manquant
MAIL_SEND_MAIL_NOT_EXISTS=L'email n'existe pas
MAIL_CODE_SEND_TOO_FAST=Envoi d'email trop fr\u00E9quent
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=D\u00E9pass\u00E9 la quantit\u00E9 quotidienne d'envoi d'emails
MAIL_CODE_NOT_FOUND=Le code de v\u00E9rification n'existe pas
MAIL_CODE_EXPIRED=Le code de v\u00E9rification a expir\u00E9
MAIL_CODE_USED=Le code de v\u00E9rification a d\u00E9j\u00E0 \u00E9t\u00E9 utilis\u00E9
MAIL_CODE_NOT_CORRECT=Code de v\u00E9rification incorrect
MAIL_IS_EXISTS=L'adresse email est d\u00E9j\u00E0 utilis\u00E9e
NOTIFY_TEMPLATE_NOT_EXISTS=Le mod\u00E8le de message interne n'existe pas
NOTIFY_TEMPLATE_CODE_DUPLICATE=Il existe d\u00E9j\u00E0 un mod\u00E8le de message interne avec le code [{}]
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Param\u00E8tre du mod\u00E8le [{}] manquant
AGENT_NOT_EXISTS=L'agent n'existe pas
AGENT_INVITE_CODE_EXISTS=Le code d'invitation existe d\u00E9j\u00E0
AGENT_HAS_DESCENDANT=L'agent a encore des sous-agents, impossible de supprimer
AGENT_ANCESTOR_NOT_AVAILABLE=Agent parent non disponible
AUTH_NOT_EXISTS=La certification du membre n'existe pas
CURRENCY_NOT_EXISTS=La devise n'existe pas
CURRENCYNOTRATE=Cette devise n'a temporairement pas de taux de change
BANNER_NOT_EXISTS=BANNER n'existe pas
TENANT_SERVER_NAME_NOT_EXISTS=Le nom de domaine du locataire syst\u00E8me n'existe pas
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Les donn\u00E9es de dictionnaire du locataire n'existent pas
TIME_CONTRACT_AMOUNT_LESS=Montant d'achat inf\u00E9rieur au montant minimum
TIME_CONTRACT_RECORD_NOT_EXISTS=L'enregistrement de transaction de contrat \u00E0 dur\u00E9e limit\u00E9e n'existe pas
TRADE_DURATION_NOT_EXISTS=La configuration de dur\u00E9e de commande n'existe pas
TRADE_MULTIPLE_NOT_EXISTS=La configuration de multiplicateur de commande n'existe pas
USER_ACCOUNT_NOT_EMPTY=Le compte ne peut pas \u00EAtre vide
AGENT_HAS_NOT_ANCESTOR=L'agent d\u00E9plac\u00E9 n'a aucun agent parent
USER_EMAIL_NOT_EXISTS=L'email n'existe pas
Mail_CODE_SEND_FAIL=\u00C9chec de l'envoi du code de v\u00E9rification par email
#******** Google verification related
GOOGLE_SECRET_BINDING_EXPIRED=La cl\u00E9 secr\u00E8te Google li\u00E9e a expir\u00E9, veuillez obtenir \u00E0 nouveau
GOOGLE_CODE_ERROR=Code de v\u00E9rification Google incorrect
ACCOUNT_IS_ERROR=\u00C9chec de la connexion, le compte n'existe pas
GOOGLE_SECRET_IS_NOT_BINDING=Cl\u00E9 secr\u00E8te Google non li\u00E9e, veuillez lier d'abord
TRADE_CONTRACT_RECORD_NOT_EXISTS=L'enregistrement de commande de contrat n'existe pas
TRADE_CONTRACT_CONFIG_ERROR=Erreur de configuration de contrat
ARG_ORDER_STATUS_IS_EMPTY=Le statut de commande ne peut pas \u00EAtre vide
ARG_LEVERAGE_IS_EMPTY=Le param\u00E8tre de levier ne peut pas \u00EAtre vide
ARG_ORDER_TYPE_IS_EMPTY=Le param\u00E8tre de type de commande ne peut pas \u00EAtre vide
ARG_VALUE_ERROR=Erreur de valeur de param\u00E8tre
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=Commande de contrat d\u00E9j\u00E0 annul\u00E9e
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=Commande de contrat d\u00E9j\u00E0 ex\u00E9cut\u00E9e
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=Contrat d\u00E9j\u00E0 ferm\u00E9
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Erreur de taux de change de devise de commande
USER_FORBIDDEN_FUNC=Cette fonctionnalit\u00E9 a \u00E9t\u00E9 d\u00E9sactiv\u00E9e
IMAGE_URL_ERROR=Erreur d'adresse de lien d'image
USER_WITHDRAW_HAS_SUCCESS=Interdit de modifier une commande d\u00E9j\u00E0 r\u00E9ussie 