COUNTRY_TH=Tailandia
COUNTRY_JP=Jap\u00F3n
COUNTRY_KR=Corea del Sur
COUNTRY_PH=Filipinas
COUNTRY_VN=Vietnam
COUNTRY_CN=China
COUNTRY_GB=Reino Unido
COUNTRY_FR=Francia
COUNTRY_US=Estados Unidos
COUNTRY_ES=Espa\u00F1a
COUNTRY_DE=Alemania
COUNTRY_RU=Rusia
COUNTRY_CA=Canad\u00E1
COUNTRY_TR=Turqu\u00EDa
COUNTRY_PT=Portugal
COUNTRY_MA=Marruecos
COUNTRY_DZ=Argelia
COUNTRY_IT=Italia
COUNTRY_CO=Colombia
COUNTRY_MX=M\u00E9xico
COUNTRY_CH=Suiza
COUNTRY_BE=B\u00E9lgica
COUNTRY_AR=Argentina
COUNTRY_NO=Noruega
COUNTRY_HK=Hong Kong
BANK=Tarjeta Bancaria
CRYPTO=Criptomoneda
USER_CERTIFIED_STATUS_NOT=No Certificado
USER_CERTIFIED_STATUS_HANDLING=En Certificaci\u00F3n
USER_CERTIFIED_STATUS_SUCCESS=Certificado
USER_CERTIFIED_STATUS_FAIL=Fall\u00F3 la Certificaci\u00F3n
ACCOUNT_NOT_EMPTY=La cuenta no puede estar vac\u00EDa
PASSWORD_NOT_EMPTY=La contrase\u00F1a no puede estar vac\u00EDa
PASSWORD_NOT_LENGTH_6_16=La longitud de la contrase\u00F1a debe ser de 6-16 caracteres
PASSWORD_FORMATTER_ERROR=El formato de la contrase\u00F1a debe contener n\u00FAmeros y letras
ACCOUNT_TYPE_ERROR=Tipo de cuenta incorrecto
MAIL_FORMATTER_ERROR=Formato de correo incorrecto
MOBILE_FORMATTER_ERROR=Formato de n\u00FAmero de tel\u00E9fono incorrecto
AREA_NOT_EMPTY=El \u00E1rea no puede estar vac\u00EDa
MAIL_SCENE_ERROR=Error en escenario de env\u00EDo de correo
SMS_SCENE_ERROR=Error en escenario de env\u00EDo de SMS
REAL_NAME_NOT_EMPTY=El nombre real no puede estar vac\u00EDo
CERTIFICATION_TYPE_ERROR=Tipo de certificaci\u00F3n de identidad incorrecto
CERTIFICATION_CODE_NOT_EMPTY=El c\u00F3digo del documento no puede estar vac\u00EDo
CERTIFICATION_FRONT_NOT_EMPTY=La foto frontal del documento no puede estar vac\u00EDa
CERTIFICATION_BACK_NOT_EMPTY=La foto trasera del documento no puede estar vac\u00EDa
TRADE_PAIR_NOT_EMPTY=Por favor seleccione un par de trading
FUNDS_RECORD_OP_TYPE=Tipo de fondo incorrecto
CURRENCY_NOT_EMPTY=Por favor seleccione una moneda
PAY_METHOD_ERROR=Por favor seleccione un m\u00E9todo de pago
RECHARGE_AMOUNT_ERROR=Por favor ingrese el monto de recarga
WITHDRAW_AMOUNT_ERROR=Por favor ingrese el monto de retiro
FUNDS_PASSWORD_ERROR=Contrase\u00F1a de fondos incorrecta
WALLET_NOT_EMPTY=Por favor seleccione una billetera
AUTH_CODE_NOT_EMPTY=Por favor ingrese el c\u00F3digo de verificaci\u00F3n
AVATAR_FORMATTER_ERROR=Por favor seleccione un avatar
WALLET_TYPE_ERROR=Tipo de billetera incorrecto
WALLET_NAME_NOT_EMPTY=Por favor ingrese el nombre de la billetera
WALLET_ACCOUNT_NOT_EMPTY=Por favor ingrese la cuenta de la billetera
WALLET_TYPE_NAME_NOT_EMPTY=Por favor ingrese el nombre del banco
ASSET_Type_ERROR=Tipo de activo incorrecto
KEY_NOT_EMPTY=La palabra clave no puede estar vac\u00EDa
AMOUNT_NOT_EMPTY=Error en cantidad
TRADE_DIRECT_ERROR=Direcci\u00F3n de trading incorrecta
TRADE_DURATION_ERROR=L\u00EDmite de tiempo de trading incorrecto
TRADE_SEND_TIME_ERROR=Hora de env\u00EDo de trading incorrecta
TRADE_PRICE_TIME_ERROR=Hora de precio de trading incorrecta
TRADE_PAGE_TYPE_ERROR=Categor\u00EDa incorrecta
SUCCESS=\u00C9xito
WAITHANDLE=En procesamiento
FAILURE=Fallo
PENDING=En procesamiento
BAD_REQUEST=Solicitud incorrecta
UNKNOW_AUTHORIZED=Tipo de autorizaci\u00F3n desconocido [{}]
TOKEN_NOT_SUPPORT_MODE=La interfaz Token no soporta el modo de autorizaci\u00F3n [{}]
CLIENT_ERROR=client_id o client_secret no fueron pasados correctamente
TOKEN_REFRESH_INVALID=Token de actualizaci\u00F3n inv\u00E1lido
TOKEN_REFRESH_CLIENT_ERROR=ID de cliente del token de actualizaci\u00F3n incorrecto
TOKEN_REFRESH_EXPIRE=Token de actualizaci\u00F3n expirado
TOKEN_NOT_EXISTS=Token de acceso no existe
TOKEN_EXPIRE=Token de acceso expirado
GRANT_RESPONSE_TYPE_ERROR=El valor del par\u00E1metro response_type solo permite code y token
UNAUTHORIZED=Cuenta no logueada
FORBIDDEN=Sin permiso para esta operaci\u00F3n
NOT_FOUND=El recurso solicitado no existe
METHOD_NOT_ALLOWED=M\u00E9todo de solicitud no permitido
LOCKED=Bloqueado
TOO_MANY_REQUESTS=Demasiadas solicitudes, intente de nuevo m\u00E1s tarde
INTERNAL_SERVER_ERROR=Error del sistema
NOT_IMPLEMENTED=Funcionalidad no implementada/no habilitada
REPEATED_REQUESTS=Solicitud repetida, intente de nuevo m\u00E1s tarde
DEMO_DENY=Modo demo, operaciones de escritura prohibidas
VALUE_ERROR=Error de valor
GOOGLE_AUTH_NOT_BIND=Autenticador Google no vinculado
GOOGLE_AUTH_CODE_ERROR=C\u00F3digo del autenticador Google incorrecto
SCHEDULER_JOB_STOP=[Trabajo Programado - Deshabilitado][Consulte https://doc.iocoder.cn/job/ para habilitar]
CANDLE_TABLE_NAME_NOT_AVAILABLE=Nombre de tabla K-line inv\u00E1lido
CANDLE_BAR_VALUE_ERROR=Par\u00E1metro bar inv\u00E1lido
CANDLE_PRICE_ERROR=Error al obtener precio
TRADE_PAIR_NOT_EXISTS=El par de trading no existe
TRADE_PAIR_EXISTS=El par de trading ya existe
TRADE_PAIR_TENANT_NOT_EXISTS=El inquilino no posee este par de trading
TRADE_PAIR_TENANT_EXISTS=El par de trading del inquilino ya existe
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=La configuraci\u00F3n del tipo de activo del par de trading del inquilino no existe
TRADE_TENANT_NEED_DEFAULT=Debe haber un par de trading predeterminado del inquilino
TRADE_TENANT_DUPLICATE_DEFAULT=El par de trading predeterminado del inquilino ya existe
CONFIG_NOT_EXISTS=La configuraci\u00F3n del par\u00E1metro no existe
CONFIG_KEY_DUPLICATE=Clave de configuraci\u00F3n de par\u00E1metro duplicada
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=No se puede eliminar la configuraci\u00F3n de par\u00E1metro de tipo sistema integrado
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Fallo al obtener configuraci\u00F3n de par\u00E1metro, raz\u00F3n: no se permite obtener configuraci\u00F3n invisible
JOB_NOT_EXISTS=El trabajo programado no existe
JOB_HANDLER_EXISTS=El manejador del trabajo programado ya existe
JOB_CHANGE_STATUS_INVALID=Solo se permite modificar a estado abierto o cerrado
JOB_CHANGE_STATUS_EQUALS=El trabajo programado ya est\u00E1 en este estado, no es necesario modificar
JOB_UPDATE_ONLY_NORMAL_STATUS=Solo trabajos con estado abierto pueden ser modificados
JOB_CRON_EXPRESSION_VALID=Expresi\u00F3n CRON incorrecta
JOB_HANDLER_BEAN_NOT_EXISTS=El Bean del manejador del trabajo programado no existe
JOB_HANDLER_BEAN_TYPE_ERROR=Tipo de Bean del manejador del trabajo programado incorrecto, no implementa interfaz JobHandler
API_ERROR_LOG_NOT_FOUND=El log de error de API no existe
API_ERROR_LOG_PROCESSED=El log de error de API ya fue procesado
FILE_PATH_EXISTS=La ruta del archivo ya existe
FILE_NOT_EXISTS=El archivo no existe
FILE_IS_EMPTY=El archivo est\u00E1 vac\u00EDo
CODEGEN_TABLE_EXISTS=La definici\u00F3n de la tabla ya existe
CODEGEN_IMPORT_TABLE_NULL=La tabla importada no existe
CODEGEN_IMPORT_COLUMNS_NULL=Los campos importados no existen
CODEGEN_TABLE_NOT_EXISTS=La definici\u00F3n de la tabla no existe
CODEGEN_COLUMN_NOT_EXISTS=La definici\u00F3n del campo no existe
CODEGEN_SYNC_COLUMNS_NULL=Los campos sincronizados no existen
CODEGEN_SYNC_NONE_CHANGE=Fallo en sincronizaci\u00F3n, no hay cambios
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Comentario de tabla de base de datos no completado
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Comentario de campo de tabla de base de datos ({}) no completado
CODEGEN_MASTER_TABLE_NOT_EXISTS=La definici\u00F3n de la tabla principal (id={}) no existe, por favor verifique
CODEGEN_SUB_COLUMN_NOT_EXISTS=El campo de la subtabla (id={}) no existe, por favor verifique
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=Fallo en generaci\u00F3n de c\u00F3digo de tabla principal, raz\u00F3n: no tiene subtabla
FILE_CONFIG_NOT_EXISTS=La configuraci\u00F3n de archivo no existe
FILE_CONFIG_DELETE_FAIL_MASTER=Esta configuraci\u00F3n de archivo no puede ser eliminada, raz\u00F3n: es la configuraci\u00F3n principal, eliminarla impedir\u00E1 la subida de archivos
DATA_SOURCE_CONFIG_NOT_EXISTS=La configuraci\u00F3n de fuente de datos no existe
DATA_SOURCE_CONFIG_NOT_OK=Configuraci\u00F3n de fuente de datos incorrecta, no se puede conectar
DEMO01_CONTACT_NOT_EXISTS=El contacto demo no existe
DEMO02_CATEGORY_NOT_EXISTS=La categor\u00EDa demo no existe
DEMO02_CATEGORY_EXITS_CHILDREN=Existen subcategor\u00EDas demo, no se puede eliminar
DEMO02_CATEGORY_PARENT_NOT_EXITS=La categor\u00EDa padre demo no existe
DEMO02_CATEGORY_PARENT_ERROR=No se puede establecer a s\u00ED mismo como categor\u00EDa padre demo
DEMO02_CATEGORY_NAME_DUPLICATE=Ya existe una categor\u00EDa demo con este nombre
DEMO02_CATEGORY_PARENT_IS_CHILD=No se puede establecer su propia subcategor\u00EDa demo como categor\u00EDa padre demo
DEMO03_STUDENT_NOT_EXISTS=El estudiante no existe
DEMO03_GRADE_NOT_EXISTS=El grado del estudiante no existe
DEMO03_GRADE_EXISTS=El grado del estudiante ya existe
AREA_NOT_EXISTS=Esta \u00E1rea no existe
USER_NOT_EXISTS=El usuario no existe
USER_MOBILE_NOT_EXISTS=N\u00FAmero de tel\u00E9fono no registrado para usuario
USER_MOBILE_USED=Este n\u00FAmero de tel\u00E9fono ya est\u00E1 siendo usado
USER_ACCOUNT_USED=La cuenta ya est\u00E1 siendo usada
USER_EMAIL_USED=El correo ya est\u00E1 siendo usado
USER_POINT_NOT_ENOUGH=Saldo de puntos del usuario insuficiente
USER_OLD_PASSWORD_NOT_MATCH=Contrase\u00F1a anterior incorrecta
USER_BALANCE_ERROR=Error en saldo del usuario
USER_CONFIG_NOT_SUPPORTED=Elemento de configuraci\u00F3n de usuario temporalmente no soportado
AUTH_LOGIN_BAD_CREDENTIALS=Fallo en login, cuenta o contrase\u00F1a incorrecta
AUTH_LOGIN_USER_DISABLED=Fallo en login, cuenta deshabilitada
AUTH_ACCOUNT_FORMAT_ERROR=Formato de cuenta de autenticaci\u00F3n incorrecto
TAG_NOT_EXISTS=La etiqueta no existe
TAG_NAME_EXISTS=La etiqueta ya existe
TAG_HAS_USER=Hay usuarios en la etiqueta de usuario, no se puede eliminar
POINT_RECORD_BIZ_NOT_SUPPORT=Tipo de negocio de registro de puntos de usuario no soportado
SIGN_IN_CONFIG_NOT_EXISTS=La regla de check-in no existe
SIGN_IN_CONFIG_EXISTS=La regla de d\u00EDas de check-in ya existe
SIGN_IN_RECORD_TODAY_EXISTS=Ya hizo check-in hoy, no repita el check-in
LEVEL_NOT_EXISTS=El nivel de usuario no existe
LEVEL_NAME_EXISTS=El nombre del nivel de usuario ya est\u00E1 siendo usado
LEVEL_VALUE_EXISTS=El valor del nivel de usuario ya est\u00E1 siendo usado
LEVEL_EXPERIENCE_MIN=La experiencia de actualizaci\u00F3n debe ser mayor que la experiencia de actualizaci\u00F3n establecida en el nivel anterior
LEVEL_EXPERIENCE_MAX=La experiencia de actualizaci\u00F3n debe ser menor que la experiencia de actualizaci\u00F3n establecida en el siguiente nivel
LEVEL_HAS_USER=Hay usuarios en el nivel de usuario, no se puede eliminar
EXPERIENCE_BIZ_NOT_SUPPORT=Tipo de negocio de experiencia de usuario no soportado
GROUP_NOT_EXISTS=El grupo de usuario no existe
GROUP_HAS_USER=El grupo de usuario contiene usuarios, no se puede eliminar
USER_WITHDRAW_NOT_EXISTS=El retiro del miembro no existe
USER_BALANCE_NOT_ENOUGH=Saldo insuficiente
USER_WITHDRAW_HAS_HANDLE=Retiro ya procesado
USER_WITHDRAW_LESS_MIN_AMOUNT=El retiro debe ser mayor que [{}]
USER_WITHDRAW_LESS_MAX_AMOUNT=El retiro debe ser menor que [{}]
USER_WITHDRAW_LESS_MAX_PROCESS=Tiene una orden siendo procesada para retiro
USER_FROZEN_BALANCE_NOT_ENOUGH=Saldo insuficiente
USER_ASSETS_SPOT_NOT_EXISTS=El activo del usuario no existe
USER_FAVORITE_TRADE_PAIR_EXISTS=El par de trading favorito ya existe
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=El par de trading favorito no existe
USER_RECHARGE_NOT_EXISTS=El registro de recarga no existe
USER_RECHARGE_HAS_HANDLE=El registro de recarga ya fue procesado
USER_WALLET_NOT_EXISTS=La billetera del miembro no existe
USER_SPOT_ORDER_NOT_EXISTS=La orden spot del miembro no existe
USER_TRANSACTIONS_NOT_EXISTS=La transacci\u00F3n del miembro no existe
USER_MARGIN_ORDER_NOT_EXISTS=La orden de contrato del miembro no existe
USER_MARGIN_CONFIG_NOT_EXISTS=La configuraci\u00F3n de contrato del miembro no existe
USER_CERTIFICATION_NOT_EXISTS=La informaci\u00F3n de certificaci\u00F3n del miembro no existe
USER_CERTIFICATION_BEEN_HANDLE=La informaci\u00F3n de certificaci\u00F3n del miembro ya fue procesada
USER_CERTIFICATION_STATUS_SUCCESS=La informaci\u00F3n de certificaci\u00F3n del miembro ya est\u00E1 certificada
USER_CERTIFICATION_STATUS_HANDLING=La informaci\u00F3n de certificaci\u00F3n del miembro est\u00E1 en auditor\u00EDa
USER_CERTIFICATION_NOT_VERIFY=Identidad [{}], operaci\u00F3n no permitida
USER_CERTIFICATION_VERIFYING=Certificaci\u00F3n de identidad en progreso, operaci\u00F3n no permitida
USER_CERTIFICATION_VERIFY_FAILURE=Fallo en certificaci\u00F3n de identidad, operaci\u00F3n no permitida
LEVEL_CONFIG_NOT_EXISTS=La configuraci\u00F3n de nivel de miembro no existe
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=La configuraci\u00F3n de nivel predeterminado de miembro no puede ser eliminada
LEVEL_CONFIG_NAME_EXISTS=El nombre del nivel ya existe
USER_FUND_PASSWORD_NOT_EXISTS=No ha establecido contrase\u00F1a de fondos, no se puede modificar
USER_FUND_PASSWORD_ERROR=Contrase\u00F1a de fondos incorrecta
FUNDS_RECORD_NOT_EXISTS=El registro de fondos no existe
USER_RECHARGE_LESS_MAX_PROCESS=Tiene [{}] \u00F3rdenes siendo procesadas para retiro
AUTH_LOGIN_CAPTCHA_CODE_ERROR=C\u00F3digo de verificaci\u00F3n incorrecto, raz\u00F3n: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token expirado
AUTH_MOBILE_NOT_EXISTS=El n\u00FAmero de tel\u00E9fono no existe
MENU_NAME_DUPLICATE=Ya existe un men\u00FA con este nombre
MENU_PARENT_NOT_EXISTS=El men\u00FA padre no existe
MENU_PARENT_ERROR=No se puede establecer a s\u00ED mismo como men\u00FA padre
MENU_NOT_EXISTS=El men\u00FA no existe
MENU_EXISTS_CHILDREN=Hay submen\u00FAs, no se puede eliminar
MENU_PARENT_NOT_DIR_OR_MENU=El tipo del men\u00FA padre debe ser directorio o men\u00FA
ROLE_NOT_EXISTS=El rol no existe
ROLE_NAME_DUPLICATE=Ya existe un rol con nombre [{}]
ROLE_CODE_DUPLICATE=Ya existe un rol con c\u00F3digo [{}]
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=No se puede operar en roles de tipo sistema integrado
ROLE_IS_DISABLE=El rol con nombre [{}] ha sido deshabilitado
ROLE_ADMIN_CODE_ERROR=El c\u00F3digo [{}] no puede ser usado
USER_USERNAME_EXISTS=La cuenta ya existe
USER_MOBILE_EXISTS=El n\u00FAmero de tel\u00E9fono ya existe
USER_EMAIL_EXISTS=El correo ya existe
USER_IMPORT_LIST_IS_EMPTY=Los datos de usuario importados no pueden estar vac\u00EDos
USER_PASSWORD_FAILED=Fallo en validaci\u00F3n de contrase\u00F1a de usuario
USER_IS_DISABLE=El usuario con nombre [{}] ha sido deshabilitado
USER_COUNT_MAX=Fallo en creaci\u00F3n de usuario, raz\u00F3n: excede la cuota m\u00E1xima del inquilino [{}]
DEPT_NAME_DUPLICATE=Ya existe un departamento con este nombre
DEPT_PARENT_NOT_EXITS=El departamento padre no existe
DEPT_NOT_FOUND=El departamento actual no existe
DEPT_EXITS_CHILDREN=Hay subdepartamentos, no se puede eliminar
DEPT_PARENT_ERROR=No se puede establecer a s\u00ED mismo como departamento padre
DEPT_EXISTS_USER=Hay empleados en el departamento, no se puede eliminar
DEPT_NOT_ENABLE=El departamento [{}] no est\u00E1 en estado activo, no se permite seleccionar
DEPT_PARENT_IS_CHILD=No se puede establecer su propio subdepartamento como departamento padre
POST_NOT_FOUND=El puesto actual no existe
POST_NOT_ENABLE=El puesto [{}] no est\u00E1 en estado activo, no se permite seleccionar
POST_NAME_DUPLICATE=Ya existe un puesto con este nombre
POST_CODE_DUPLICATE=Ya existe un puesto con este identificador
DICT_TYPE_NOT_EXISTS=El tipo de diccionario actual no existe
DICT_TYPE_NOT_ENABLE=El tipo de diccionario no est\u00E1 en estado activo, no se permite seleccionar
DICT_TYPE_NAME_DUPLICATE=Ya existe un tipo de diccionario con este nombre
DICT_TYPE_TYPE_DUPLICATE=Ya existe un tipo de diccionario con este tipo
DICT_TYPE_HAS_CHILDREN=No se puede eliminar, este tipo de diccionario a\u00FAn tiene datos de diccionario
DICT_DATA_NOT_EXISTS=Los datos de diccionario actuales no existen
DICT_DATA_NOT_ENABLE=Los datos de diccionario [{}] no est\u00E1n en estado activo, no se permite seleccionar
DICT_DATA_VALUE_DUPLICATE=Ya existen datos de diccionario con este valor
NOTICE_NOT_FOUND=El aviso actual no existe
SMS_CHANNEL_NOT_EXISTS=El canal SMS no existe
SMS_CHANNEL_DISABLE=El canal SMS no est\u00E1 en estado activo, no se permite seleccionar
SMS_CHANNEL_HAS_CHILDREN=No se puede eliminar, este canal SMS a\u00FAn tiene plantillas SMS
SMS_TEMPLATE_NOT_EXISTS=La plantilla SMS no existe
SMS_TEMPLATE_CODE_DUPLICATE=Ya existe una plantilla SMS con c\u00F3digo [{}]
SMS_TEMPLATE_API_ERROR=Fallo en llamada de API de plantilla SMS, raz\u00F3n: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=La plantilla SMS de API no puede ser usada, raz\u00F3n: en auditor\u00EDa
SMS_TEMPLATE_API_AUDIT_FAIL=La plantilla SMS de API no puede ser usada, raz\u00F3n: auditor\u00EDa no pas\u00F3, {}
SMS_TEMPLATE_API_NOT_FOUND=La plantilla SMS de API no puede ser usada, raz\u00F3n: plantilla no existe
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Par\u00E1metro de plantilla [{}] faltante
SMS_CODE_NOT_FOUND=El c\u00F3digo de verificaci\u00F3n no existe
SMS_CODE_EXPIRED=El c\u00F3digo de verificaci\u00F3n ha expirado
SMS_CODE_USED=El c\u00F3digo de verificaci\u00F3n ya fue usado
SMS_CODE_NOT_CORRECT=C\u00F3digo de verificaci\u00F3n incorrecto
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Excedi\u00F3 la cantidad diaria de env\u00EDo de SMS
SMS_CODE_SEND_TOO_FAST=Env\u00EDo de SMS demasiado frecuente
TENANT_NOT_EXISTS=El inquilino no existe
TENANT_DISABLE=El inquilino con nombre [{}] ha sido deshabilitado
TENANT_EXPIRE=El inquilino con nombre [{}] ha expirado
TENANT_CAN_NOT_UPDATE_SYSTEM=\u00A1El inquilino del sistema no puede ser modificado, eliminado u otras operaciones!
TENANT_CODE_DUPLICATE=El inquilino con c\u00F3digo [{}] ya existe
TENANT_WEBSITE_DUPLICATE=El inquilino con dominio [{}] ya existe
TENANT_PACKAGE_NOT_EXISTS=El paquete del inquilino no existe
TENANT_PACKAGE_USED=El inquilino est\u00E1 usando este paquete, restablezca el paquete del inquilino antes de intentar eliminar
TENANT_PACKAGE_DISABLE=El paquete del inquilino con nombre [{}] ha sido deshabilitado
OAUTH2_CLIENT_NOT_EXISTS=El cliente OAuth2 no existe
OAUTH2_CLIENT_EXISTS=El ID del cliente OAuth2 ya existe
OAUTH2_CLIENT_DISABLE=El cliente OAuth2 ha sido deshabilitado
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=No soporta este tipo de autorizaci\u00F3n
OAUTH2_CLIENT_SCOPE_OVER=\u00C1mbito de autorizaci\u00F3n demasiado amplio
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=redirect_uri inv\u00E1lido: [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=client_secret inv\u00E1lido: {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id no coincide
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=redirect_uri no coincide
OAUTH2_GRANT_STATE_MISMATCH=state no coincide
OAUTH2_GRANT_CODE_NOT_EXISTS=code no existe
OAUTH2_CODE_NOT_EXISTS=code no existe
OAUTH2_CODE_EXPIRE=C\u00F3digo de verificaci\u00F3n expirado
MAIL_ACCOUNT_NOT_EXISTS=La cuenta de correo no existe
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=No se puede eliminar, esta cuenta de correo a\u00FAn tiene plantillas de correo
MAIL_TEMPLATE_NOT_EXISTS=La plantilla de correo no existe
MAIL_TEMPLATE_CODE_EXISTS=El c\u00F3digo de plantilla de correo [{}] ya existe
MAIL_SEND_TEMPLATE_PARAM_MISS=Par\u00E1metro de plantilla [{}] faltante
MAIL_SEND_MAIL_NOT_EXISTS=El correo no existe
MAIL_CODE_SEND_TOO_FAST=Env\u00EDo de correo demasiado frecuente
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Excedi\u00F3 la cantidad diaria de env\u00EDo de correos
MAIL_CODE_NOT_FOUND=El c\u00F3digo de verificaci\u00F3n no existe
MAIL_CODE_EXPIRED=El c\u00F3digo de verificaci\u00F3n ha expirado
MAIL_CODE_USED=El c\u00F3digo de verificaci\u00F3n ya fue usado
MAIL_CODE_NOT_CORRECT=C\u00F3digo de verificaci\u00F3n incorrecto
MAIL_IS_EXISTS=La direcci\u00F3n de correo ya est\u00E1 siendo usada
NOTIFY_TEMPLATE_NOT_EXISTS=La plantilla de mensaje interno no existe
NOTIFY_TEMPLATE_CODE_DUPLICATE=Ya existe una plantilla de mensaje interno con c\u00F3digo [{}]
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Par\u00E1metro de plantilla [{}] faltante
AGENT_NOT_EXISTS=El agente no existe
AGENT_INVITE_CODE_EXISTS=El c\u00F3digo de invitaci\u00F3n ya existe
AGENT_HAS_DESCENDANT=El agente a\u00FAn tiene sub-agentes, no se puede eliminar
AGENT_ANCESTOR_NOT_AVAILABLE=Agente padre no disponible
AUTH_NOT_EXISTS=La certificaci\u00F3n del miembro no existe
CURRENCY_NOT_EXISTS=La moneda no existe
CURRENCYNOTRATE=Esta moneda temporalmente no tiene tasa de cambio
BANNER_NOT_EXISTS=BANNER no existe
TENANT_SERVER_NAME_NOT_EXISTS=El nombre de dominio del inquilino del sistema no existe
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Los datos de diccionario del inquilino no existen
TIME_CONTRACT_AMOUNT_LESS=Monto de compra menor que el monto m\u00EDnimo
TIME_CONTRACT_RECORD_NOT_EXISTS=El registro de transacci\u00F3n de contrato de tiempo limitado no existe
TRADE_DURATION_NOT_EXISTS=La configuraci\u00F3n de duraci\u00F3n de orden no existe
TRADE_MULTIPLE_NOT_EXISTS=La configuraci\u00F3n de multiplicador de orden no existe
USER_ACCOUNT_NOT_EMPTY=La cuenta no puede estar vac\u00EDa
AGENT_HAS_NOT_ANCESTOR=El agente movido no tiene ning\u00FAn agente padre
USER_EMAIL_NOT_EXISTS=El correo no existe
Mail_CODE_SEND_FAIL=Fallo en env\u00EDo de c\u00F3digo de verificaci\u00F3n por correo
#******** Google verification related
GOOGLE_SECRET_BINDING_EXPIRED=La clave secreta de Google vinculada ha expirado, obt\u00E9ngala de nuevo
GOOGLE_CODE_ERROR=C\u00F3digo de verificaci\u00F3n de Google incorrecto
ACCOUNT_IS_ERROR=Fallo en login, la cuenta no existe
GOOGLE_SECRET_IS_NOT_BINDING=Clave secreta de Google no vinculada, vinc\u00FAlela primero
TRADE_CONTRACT_RECORD_NOT_EXISTS=El registro de orden de contrato no existe
TRADE_CONTRACT_CONFIG_ERROR=Error en configuraci\u00F3n de contrato
ARG_ORDER_STATUS_IS_EMPTY=El estado de orden no puede estar vac\u00EDo
ARG_LEVERAGE_IS_EMPTY=El par\u00E1metro de apalancamiento no puede estar vac\u00EDo
ARG_ORDER_TYPE_IS_EMPTY=El par\u00E1metro de tipo de orden no puede estar vac\u00EDo
ARG_VALUE_ERROR=Error en valor de par\u00E1metro
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=Orden de contrato ya cancelada
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=Orden de contrato ya ejecutada
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=Contrato ya cerrado
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Error en tasa de cambio de moneda de orden
USER_FORBIDDEN_FUNC=Esta funcionalidad ha sido deshabilitada
IMAGE_URL_ERROR=Error en direcci\u00F3n de enlace de imagen
USER_WITHDRAW_HAS_SUCCESS=Prohibido modificar orden ya exitosa 