COUNTRY_TH=Thailand
COUNTRY_JP=Japan
COUNTRY_KR=S\u00FCdkorea
COUNTRY_PH=Philippinen
COUNTRY_VN=Vietnam
COUNTRY_CN=China
COUNTRY_GB=Vereinigtes K\u00F6nigreich
COUNTRY_FR=Frankreich
COUNTRY_US=Vereinigte Staaten
COUNTRY_ES=Spanien
COUNTRY_DE=Deutschland
COUNTRY_RU=Russland
COUNTRY_CA=Kanada
COUNTRY_TR=T\u00FCrkei
COUNTRY_PT=Portugal
COUNTRY_MA=Marokko
COUNTRY_DZ=Algerien
COUNTRY_IT=Italien
COUNTRY_CO=Kolumbien
COUNTRY_MX=Mexiko
COUNTRY_CH=Schweiz
COUNTRY_BE=Belgien
COUNTRY_AR=Argentinien
COUNTRY_NO=Norwegen
COUNTRY_HK=Hongkong
BANK=Bankkarte
CRYPTO=Kryptow\u00E4hrung
USER_CERTIFIED_STATUS_NOT=Nicht zertifiziert
USER_CERTIFIED_STATUS_HANDLING=In Zertifizierung
USER_CERTIFIED_STATUS_SUCCESS=Zertifiziert
USER_CERTIFIED_STATUS_FAIL=Zertifizierung fehlgeschlagen
ACCOUNT_NOT_EMPTY=Konto darf nicht leer sein
PASSWORD_NOT_EMPTY=Passwort darf nicht leer sein
PASSWORD_NOT_LENGTH_6_16=Passwortl\u00E4nge betr\u00E4gt 6-16 Zeichen
PASSWORD_FORMATTER_ERROR=Passwortformat sind Zahlen und Buchstaben
ACCOUNT_TYPE_ERROR=Kontotyp-Fehler
MAIL_FORMATTER_ERROR=E-Mail-Format-Fehler
MOBILE_FORMATTER_ERROR=Mobilnummer-Format-Fehler
AREA_NOT_EMPTY=Bereich darf nicht leer sein
MAIL_SCENE_ERROR=SMS-Sendeszenario-Fehler
SMS_SCENE_ERROR=SMS-Sendeszenario-Fehler
REAL_NAME_NOT_EMPTY=Echter Name darf nicht leer sein
CERTIFICATION_TYPE_ERROR=Identit\u00E4tszertifizierungstyp-Fehler
CERTIFICATION_CODE_NOT_EMPTY=Ausweisnummer darf nicht leer sein
CERTIFICATION_FRONT_NOT_EMPTY=Ausweis-Vorderseite darf nicht leer sein
CERTIFICATION_BACK_NOT_EMPTY=Ausweis-R\u00FCckseite darf nicht leer sein
TRADE_PAIR_NOT_EMPTY=Bitte w\u00E4hlen Sie das Handelspaar
FUNDS_RECORD_OP_TYPE=Geldtyp-Fehler
CURRENCY_NOT_EMPTY=Bitte w\u00E4hlen Sie die W\u00E4hrung
PAY_METHOD_ERROR=Bitte w\u00E4hlen Sie die Zahlungsmethode
RECHARGE_AMOUNT_ERROR=Bitte geben Sie den Aufladebetrag ein
WITHDRAW_AMOUNT_ERROR=Bitte geben Sie vollst\u00E4ndig den Abhebungsbetrag ein
FUNDS_PASSWORD_ERROR=Geld-Passwort-Fehler
WALLET_NOT_EMPTY=Bitte w\u00E4hlen Sie die Brieftasche
AUTH_CODE_NOT_EMPTY=Bitte geben Sie den Verifizierungscode ein
AVATAR_FORMATTER_ERROR=Bitte w\u00E4hlen Sie den Avatar
WALLET_TYPE_ERROR=Brieftaschen-Typ-Fehler
WALLET_NAME_NOT_EMPTY=Bitte geben Sie den Brieftaschennamen ein
WALLET_ACCOUNT_NOT_EMPTY=Bitte geben Sie das Brieftaschenkonto ein
WALLET_TYPE_NAME_NOT_EMPTY=Bitte geben Sie den Banknamen ein
ASSET_Type_ERROR=Asset-Typ-Fehler
KEY_NOT_EMPTY=Schl\u00FCsselwort darf nicht leer sein
AMOUNT_NOT_EMPTY=Fehlende Betragsmenge
TRADE_DIRECT_ERROR=Handelsrichtung-Fehler
TRADE_DURATION_ERROR=Handelsdauer-Fehler
TRADE_SEND_TIME_ERROR=Handel-Sendezeit-Fehler
TRADE_PRICE_TIME_ERROR=Handel-Preiszeit-Fehler
TRADE_PAGE_TYPE_ERROR=Klassifizierungsfehler
SUCCESS=Erfolg
WAITHANDLE=In Bearbeitung
FAILURE=Fehlgeschlagen
PENDING=In Bearbeitung
BAD_REQUEST=Fehlerhafte Anfrage
UNKNOW_AUTHORIZED=Unbekannter Autorisierungstyp [{}]
TOKEN_NOT_SUPPORT_MODE=Token-Schnittstelle unterst\u00FCtzt den Autorisierungsmodus [{}] nicht
CLIENT_ERROR=client_id oder client_secret falsch \u00FCbertragen
TOKEN_REFRESH_INVALID=Ung\u00FCltiger Aktualisierungstoken
TOKEN_REFRESH_CLIENT_ERROR=Client-Nummer des Aktualisierungstokens falsch
TOKEN_REFRESH_EXPIRE=Aktualisierungstoken abgelaufen
TOKEN_NOT_EXISTS=Zugriffstoken existiert nicht
TOKEN_EXPIRE=Zugriffstoken abgelaufen
GRANT_RESPONSE_TYPE_ERROR=response_type-Parameter erlaubt nur code und token
UNAUTHORIZED=Konto nicht angemeldet
FORBIDDEN=Keine Berechtigung f\u00FCr diese Operation
NOT_FOUND=Angeforderte Ressource existiert nicht
METHOD_NOT_ALLOWED=Anfragemethode nicht erlaubt
LOCKED=Gesperrt
TOO_MANY_REQUESTS=Zu viele Anfragen, bitte versuchen Sie es sp\u00E4ter
INTERNAL_SERVER_ERROR=Systemausnahme
NOT_IMPLEMENTED=Funktion nicht implementiert/nicht aktiviert
REPEATED_REQUESTS=Wiederholte Anfrage, bitte versuchen Sie es sp\u00E4ter
DEMO_DENY=Demo-Modus, Schreibvorg\u00E4nge verboten
VALUE_ERROR=Wertfehler
GOOGLE_AUTH_NOT_BIND=Google Authenticator nicht gebunden
GOOGLE_AUTH_CODE_ERROR=Google Authenticator Code-Fehler
SCHEDULER_JOB_STOP=[Geplante Aufgabe - Deaktiviert][Siehe https://doc.iocoder.cn/job/ zum Aktivieren]
CANDLE_TABLE_NAME_NOT_AVAILABLE=K-Linien-Tabellenname ung\u00FCltig
CANDLE_BAR_VALUE_ERROR=Bar-Parameter ung\u00FCltig
CANDLE_PRICE_ERROR=Preisabruf-Fehler
TRADE_PAIR_NOT_EXISTS=Handelspaar existiert nicht
TRADE_PAIR_EXISTS=Handelspaar existiert bereits
TRADE_PAIR_TENANT_NOT_EXISTS=Mandant hat dieses Handelspaar nicht
TRADE_PAIR_TENANT_EXISTS=Mandanten-Handelspaar existiert bereits
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=Mandanten-Handelspaar Asset-Typ-Konfiguration existiert nicht
TRADE_TENANT_NEED_DEFAULT=Es muss ein Standard-Handelspaar f\u00FCr den Mandanten geben
TRADE_TENANT_DUPLICATE_DEFAULT=Standard-Handelspaar des Mandanten existiert bereits
CONFIG_NOT_EXISTS=Parameterkonfiguration existiert nicht
CONFIG_KEY_DUPLICATE=Parameterkonfigurationsschl\u00FCssel doppelt
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=Kann Parameterkonfiguration vom systemintegrierten Typ nicht l\u00F6schen
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Fehler beim Abrufen der Parameterkonfiguration, Grund: nicht erlaubt, unsichtbare Konfiguration abzurufen
JOB_NOT_EXISTS=Geplante Aufgabe existiert nicht
JOB_HANDLER_EXISTS=Handler f\u00FCr geplante Aufgabe existiert bereits
JOB_CHANGE_STATUS_INVALID=Nur \u00C4nderung zu aktiviert oder deaktiviert erlaubt
JOB_CHANGE_STATUS_EQUALS=Geplante Aufgabe ist bereits in diesem Status, keine \u00C4nderung erforderlich
JOB_UPDATE_ONLY_NORMAL_STATUS=Nur Aufgaben im aktivierten Status k\u00F6nnen ge\u00E4ndert werden
JOB_CRON_EXPRESSION_VALID=CRON-Ausdruck falsch
JOB_HANDLER_BEAN_NOT_EXISTS=Handler-Bean f\u00FCr geplante Aufgabe existiert nicht
JOB_HANDLER_BEAN_TYPE_ERROR=Handler-Bean-Typ f\u00FCr geplante Aufgabe falsch, implementiert JobHandler-Interface nicht
API_ERROR_LOG_NOT_FOUND=API-Fehlerprotokoll nicht gefunden
API_ERROR_LOG_PROCESSED=API-Fehlerprotokoll verarbeitet
FILE_PATH_EXISTS=Dateipfad existiert bereits
FILE_NOT_EXISTS=Datei existiert nicht
FILE_IS_EMPTY=Datei ist leer
CODEGEN_TABLE_EXISTS=Tabellendefinition existiert bereits
CODEGEN_IMPORT_TABLE_NULL=Importierte Tabelle existiert nicht
CODEGEN_IMPORT_COLUMNS_NULL=Importierte Spalten existieren nicht
CODEGEN_TABLE_NOT_EXISTS=Tabellendefinition existiert nicht
CODEGEN_COLUMN_NOT_EXISTS=Spaltendefinition existiert nicht
CODEGEN_SYNC_COLUMNS_NULL=Synchronisierte Spalten existieren nicht
CODEGEN_SYNC_NONE_CHANGE=Synchronisation fehlgeschlagen, keine \u00C4nderungen
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Datenbank-Tabellenkommentar nicht ausgef\u00FCllt
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Datenbank-Tabellenspaltenkommentar ({}) nicht ausgef\u00FCllt
CODEGEN_MASTER_TABLE_NOT_EXISTS=Master-Tabellendefinition (id={}) existiert nicht, bitte \u00FCberpr\u00FCfen
CODEGEN_SUB_COLUMN_NOT_EXISTS=Untertabellenspalte (id={}) existiert nicht, bitte \u00FCberpr\u00FCfen
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=Master-Tabellen-Code-Generierung fehlgeschlagen, Grund: hat keine Untertabelle
FILE_CONFIG_NOT_EXISTS=Dateikonfiguration existiert nicht
FILE_CONFIG_DELETE_FAIL_MASTER=Diese Dateikonfiguration kann nicht gel\u00F6scht werden, Grund: ist Master-Konfiguration, L\u00F6schen f\u00FChrt zu Unf\u00E4higkeit, Dateien hochzuladen
DATA_SOURCE_CONFIG_NOT_EXISTS=Datenquellenkonfiguration existiert nicht
DATA_SOURCE_CONFIG_NOT_OK=Datenquellenkonfiguration falsch, kann nicht verbinden
DEMO01_CONTACT_NOT_EXISTS=Demo-Kontakt existiert nicht
DEMO02_CATEGORY_NOT_EXISTS=Demo-Kategorie existiert nicht
DEMO02_CATEGORY_EXITS_CHILDREN=Demo-Unterkategorien existieren, kann nicht gel\u00F6scht werden
DEMO02_CATEGORY_PARENT_NOT_EXITS=Demo-Elternkategorie existiert nicht
DEMO02_CATEGORY_PARENT_ERROR=Kann sich nicht selbst als Demo-Elternkategorie setzen
DEMO02_CATEGORY_NAME_DUPLICATE=Demo-Kategorie mit diesem Namen existiert bereits
DEMO02_CATEGORY_PARENT_IS_CHILD=Kann Demo-Unterkategorie nicht als Elternkategorie setzen
DEMO03_STUDENT_NOT_EXISTS=Student existiert nicht
DEMO03_GRADE_NOT_EXISTS=Studentenklasse existiert nicht
DEMO03_GRADE_EXISTS=Studentenklasse existiert bereits
AREA_NOT_EXISTS=Dieses Gebiet existiert nicht
USER_NOT_EXISTS=Benutzer existiert nicht
USER_MOBILE_NOT_EXISTS=Mobilnummer f\u00FCr Benutzer nicht registriert
USER_MOBILE_USED=Diese Mobilnummer wird bereits verwendet
USER_ACCOUNT_USED=Konto wird bereits verwendet
USER_EMAIL_USED=E-Mail wird bereits verwendet
USER_POINT_NOT_ENOUGH=Benutzerpunkte-Guthaben nicht ausreichend
USER_OLD_PASSWORD_NOT_MATCH=Altes Passwort falsch
USER_BALANCE_ERROR=Benutzer-Guthaben-Fehler
USER_CONFIG_NOT_SUPPORTED=Benutzerkonfigurationselement tempor\u00E4r nicht unterst\u00FCtzt
AUTH_LOGIN_BAD_CREDENTIALS=Anmeldung fehlgeschlagen, Konto und Passwort falsch
AUTH_LOGIN_USER_DISABLED=Anmeldung fehlgeschlagen, Konto deaktiviert
AUTH_ACCOUNT_FORMAT_ERROR=Authentifizierungskonto-Format-Fehler
TAG_NOT_EXISTS=Tag existiert nicht
TAG_NAME_EXISTS=Tag existiert bereits
TAG_HAS_USER=Benutzer unter Benutzer-Tag existieren, kann nicht gel\u00F6scht werden
POINT_RECORD_BIZ_NOT_SUPPORT=Benutzerpunkte-Aufzeichnungs-Gesch\u00E4ftstyp nicht unterst\u00FCtzt
SIGN_IN_CONFIG_NOT_EXISTS=Check-In-Regel existiert nicht
SIGN_IN_CONFIG_EXISTS=Check-In-Tage-Regel existiert bereits
SIGN_IN_RECORD_TODAY_EXISTS=Heute bereits eingecheckt, bitte nicht wiederholen
LEVEL_NOT_EXISTS=Benutzerlevel existiert nicht
LEVEL_NAME_EXISTS=Benutzerlevel-Name wird bereits verwendet
LEVEL_VALUE_EXISTS=Benutzerlevel-Wert wird bereits verwendet
LEVEL_EXPERIENCE_MIN=Upgrade-Erfahrung muss gr\u00F6\u00DFer sein als die des vorherigen Levels
LEVEL_EXPERIENCE_MAX=Upgrade-Erfahrung muss kleiner sein als die des n\u00E4chsten Levels
LEVEL_HAS_USER=Benutzer unter Benutzerlevel existieren, kann nicht gel\u00F6scht werden
EXPERIENCE_BIZ_NOT_SUPPORT=Benutzererfahrungs-Gesch\u00E4ftstyp nicht unterst\u00FCtzt
GROUP_NOT_EXISTS=Benutzergruppe existiert nicht
GROUP_HAS_USER=Benutzergruppe hat Benutzer, kann nicht gel\u00F6scht werden
USER_WITHDRAW_NOT_EXISTS=Mitglieder-Abhebung existiert nicht
USER_BALANCE_NOT_ENOUGH=Guthaben nicht ausreichend
USER_WITHDRAW_HAS_HANDLE=Abhebung bereits verarbeitet
USER_WITHDRAW_LESS_MIN_AMOUNT=Abhebung muss gr\u00F6\u00DFer als [{}] sein
USER_WITHDRAW_LESS_MAX_AMOUNT=Abhebung muss kleiner als [{}] sein
USER_WITHDRAW_LESS_MAX_PROCESS=Sie haben eine Bestellung in Abhebung
USER_FROZEN_BALANCE_NOT_ENOUGH=Guthaben nicht ausreichend
USER_ASSETS_SPOT_NOT_EXISTS=Benutzer-Assets existieren nicht
USER_FAVORITE_TRADE_PAIR_EXISTS=Favoriten-Handelspaar existiert bereits
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=Favoriten-Handelspaar existiert nicht
USER_RECHARGE_NOT_EXISTS=Auflade-Aufzeichnung existiert nicht
USER_RECHARGE_HAS_HANDLE=Auflade-Aufzeichnung bereits verarbeitet
USER_WALLET_NOT_EXISTS=Mitglieder-Brieftasche existiert nicht
USER_SPOT_ORDER_NOT_EXISTS=Mitglieder-Spot-Bestellung existiert nicht
USER_TRANSACTIONS_NOT_EXISTS=Mitglieder-Konto existiert nicht
USER_MARGIN_ORDER_NOT_EXISTS=Mitglieder-Kontraktbestellung existiert nicht
USER_MARGIN_CONFIG_NOT_EXISTS=Mitglieder-Kontraktkonfiguration existiert nicht
USER_CERTIFICATION_NOT_EXISTS=Mitglieder-Zertifizierungsinformationen existieren nicht
USER_CERTIFICATION_BEEN_HANDLE=Mitglieder-Zertifizierungsinformationen bereits verarbeitet
USER_CERTIFICATION_STATUS_SUCCESS=Mitglieder-Zertifizierungsinformationen bereits zertifiziert
USER_CERTIFICATION_STATUS_HANDLING=Mitglieder-Zertifizierungsinformationen in \u00DCberpr\u00FCfung
USER_CERTIFICATION_NOT_VERIFY=Identit\u00E4t [{}], Operation nicht erlaubt
USER_CERTIFICATION_VERIFYING=Identit\u00E4tszertifizierung in Bearbeitung, Operation nicht erlaubt
USER_CERTIFICATION_VERIFY_FAILURE=Identit\u00E4tszertifizierung fehlgeschlagen, Operation nicht erlaubt
LEVEL_CONFIG_NOT_EXISTS=Mitglieder-Level-Konfiguration existiert nicht
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=Standard-Level-Konfiguration f\u00FCr Mitglieder kann nicht gel\u00F6scht werden
LEVEL_CONFIG_NAME_EXISTS=Level-Name existiert bereits
USER_FUND_PASSWORD_NOT_EXISTS=Sie haben kein Geld-Passwort gesetzt, kann nicht ge\u00E4ndert werden
USER_FUND_PASSWORD_ERROR=Geld-Passwort-Fehler
FUNDS_RECORD_NOT_EXISTS=Geld-Aufzeichnung existiert nicht
USER_RECHARGE_LESS_MAX_PROCESS=Sie haben [{}] Bestellungen in Abhebung
AUTH_LOGIN_CAPTCHA_CODE_ERROR=Verifizierungscode falsch, Grund: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token ist abgelaufen
AUTH_MOBILE_NOT_EXISTS=Mobilnummer existiert nicht
MENU_NAME_DUPLICATE=Men\u00FC mit diesem Namen existiert bereits
MENU_PARENT_NOT_EXISTS=Elternmen\u00FC existiert nicht
MENU_PARENT_ERROR=Kann sich nicht selbst als Elternmen\u00FC setzen
MENU_NOT_EXISTS=Men\u00FC existiert nicht
MENU_EXISTS_CHILDREN=Untermen\u00FCs existieren, kann nicht gel\u00F6scht werden
MENU_PARENT_NOT_DIR_OR_MENU=Elternmen\u00FC-Typ muss Verzeichnis oder Men\u00FC sein
ROLE_NOT_EXISTS=Rolle existiert nicht
ROLE_NAME_DUPLICATE=Rolle mit Name [{}] existiert bereits
ROLE_CODE_DUPLICATE=Rolle mit Code [{}] existiert bereits
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=Kann Rolle vom systemintegrierten Typ nicht bearbeiten
ROLE_IS_DISABLE=Rolle mit Name [{}] ist deaktiviert
ROLE_ADMIN_CODE_ERROR=Code [{}] kann nicht verwendet werden
USER_USERNAME_EXISTS=Konto existiert bereits
USER_MOBILE_EXISTS=Mobilnummer existiert bereits
USER_EMAIL_EXISTS=E-Mail existiert bereits
USER_IMPORT_LIST_IS_EMPTY=Benutzer-Importdaten k\u00F6nnen nicht leer sein
USER_PASSWORD_FAILED=Benutzer-Passwort-Validierung fehlgeschlagen
USER_IS_DISABLE=Benutzer mit Name [{}] ist deaktiviert
USER_COUNT_MAX=Benutzererstellung fehlgeschlagen, Grund: Mandanten-Maximalquote [{}] \u00FCberschritten
DEPT_NAME_DUPLICATE=Abteilung mit diesem Namen existiert bereits
DEPT_PARENT_NOT_EXITS=Elternabteilung existiert nicht
DEPT_NOT_FOUND=Aktuelle Abteilung existiert nicht
DEPT_EXITS_CHILDREN=Unterabteilungen existieren, kann nicht gel\u00F6scht werden
DEPT_PARENT_ERROR=Kann sich nicht selbst als Elternabteilung setzen
DEPT_EXISTS_USER=Mitarbeiter in Abteilung existieren, kann nicht gel\u00F6scht werden
DEPT_NOT_ENABLE=Abteilung [{}] ist nicht im aktivierten Status, Auswahl nicht erlaubt
DEPT_PARENT_IS_CHILD=Kann Unterabteilung nicht als Elternabteilung setzen
POST_NOT_FOUND=Aktuelle Position existiert nicht
POST_NOT_ENABLE=Position [{}] ist nicht im aktivierten Status, Auswahl nicht erlaubt
POST_NAME_DUPLICATE=Position mit diesem Namen existiert bereits
POST_CODE_DUPLICATE=Position mit dieser Kennung existiert bereits
DICT_TYPE_NOT_EXISTS=Aktueller W\u00F6rterbuchtyp existiert nicht
DICT_TYPE_NOT_ENABLE=W\u00F6rterbuchtyp ist nicht im aktivierten Status, Auswahl nicht erlaubt
DICT_TYPE_NAME_DUPLICATE=W\u00F6rterbuchtyp mit diesem Namen existiert bereits
DICT_TYPE_TYPE_DUPLICATE=W\u00F6rterbuchtyp dieses Typs existiert bereits
DICT_TYPE_HAS_CHILDREN=Kann nicht gel\u00F6scht werden, W\u00F6rterbuchtyp hat W\u00F6rterbuchdaten
DICT_DATA_NOT_EXISTS=Aktuelle W\u00F6rterbuchdaten existieren nicht
DICT_DATA_NOT_ENABLE=W\u00F6rterbuchdaten [{}] sind nicht im aktivierten Status, Auswahl nicht erlaubt
DICT_DATA_VALUE_DUPLICATE=W\u00F6rterbuchdaten mit diesem Wert existieren bereits
NOTICE_NOT_FOUND=Aktuelle Benachrichtigung existiert nicht
SMS_CHANNEL_NOT_EXISTS=SMS-Kanal existiert nicht
SMS_CHANNEL_DISABLE=SMS-Kanal ist nicht im aktivierten Status, Auswahl nicht erlaubt
SMS_CHANNEL_HAS_CHILDREN=Kann nicht gel\u00F6scht werden, SMS-Kanal hat SMS-Vorlagen
SMS_TEMPLATE_NOT_EXISTS=SMS-Vorlage existiert nicht
SMS_TEMPLATE_CODE_DUPLICATE=SMS-Vorlage mit Code [{}] existiert bereits
SMS_TEMPLATE_API_ERROR=SMS-API-Vorlagenaufruf fehlgeschlagen, Grund: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=SMS-API-Vorlage kann nicht verwendet werden, Grund: in \u00DCberpr\u00FCfung
SMS_TEMPLATE_API_AUDIT_FAIL=SMS-API-Vorlage kann nicht verwendet werden, Grund: \u00DCberpr\u00FCfung nicht bestanden, {}
SMS_TEMPLATE_API_NOT_FOUND=SMS-API-Vorlage kann nicht verwendet werden, Grund: Vorlage existiert nicht
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Vorlagenparameter [{}] fehlt
SMS_CODE_NOT_FOUND=Verifizierungscode nicht gefunden
SMS_CODE_EXPIRED=Verifizierungscode abgelaufen
SMS_CODE_USED=Verifizierungscode bereits verwendet
SMS_CODE_NOT_CORRECT=Verifizierungscode falsch
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Maximale t\u00E4gliche SMS-Anzahl \u00FCberschritten
SMS_CODE_SEND_TOO_FAST=SMS-Versand zu h\u00E4ufig
TENANT_NOT_EXISTS=Mandant existiert nicht
TENANT_DISABLE=Mandant mit Name [{}] ist deaktiviert
TENANT_EXPIRE=Mandant mit Name [{}] ist abgelaufen
TENANT_CAN_NOT_UPDATE_SYSTEM=System-Mandant kann nicht ge\u00E4ndert oder gel\u00F6scht werden!
TENANT_CODE_DUPLICATE=Mandant mit Code [{}] existiert bereits
TENANT_WEBSITE_DUPLICATE=Mandant mit Domain [{}] existiert bereits
TENANT_PACKAGE_NOT_EXISTS=Mandanten-Paket existiert nicht
TENANT_PACKAGE_USED=Mandant verwendet dieses Paket, bitte weisen Sie dem Mandanten ein neues Paket zu, bevor Sie es l\u00F6schen
TENANT_PACKAGE_DISABLE=Mandanten-Paket mit Name [{}] ist deaktiviert
OAUTH2_CLIENT_NOT_EXISTS=OAuth2-Client existiert nicht
OAUTH2_CLIENT_EXISTS=OAuth2-Client-Nummer existiert bereits
OAUTH2_CLIENT_DISABLE=OAuth2-Client deaktiviert
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=Dieser Autorisierungstyp wird nicht unterst\u00FCtzt
OAUTH2_CLIENT_SCOPE_OVER=Autorisierungsbereich zu gro\u00DF
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=Ung\u00FCltige redirect_uri: [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=Ung\u00FCltiger client_secret: {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id stimmt nicht \u00FCberein
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=redirect_uri stimmt nicht \u00FCberein
OAUTH2_GRANT_STATE_MISMATCH=state stimmt nicht \u00FCberein
OAUTH2_GRANT_CODE_NOT_EXISTS=code existiert nicht
OAUTH2_CODE_NOT_EXISTS=code existiert nicht
OAUTH2_CODE_EXPIRE=Verifizierungscode abgelaufen
MAIL_ACCOUNT_NOT_EXISTS=E-Mail-Konto existiert nicht
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=Kann nicht gel\u00F6scht werden, E-Mail-Konto hat E-Mail-Vorlagen
MAIL_TEMPLATE_NOT_EXISTS=E-Mail-Vorlage existiert nicht
MAIL_TEMPLATE_CODE_EXISTS=E-Mail-Vorlagencode [{}] existiert bereits
MAIL_SEND_TEMPLATE_PARAM_MISS=Vorlagenparameter [{}] fehlt
MAIL_SEND_MAIL_NOT_EXISTS=E-Mail existiert nicht
MAIL_CODE_SEND_TOO_FAST=E-Mail-Versand zu h\u00E4ufig
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Maximale t\u00E4gliche E-Mail-Anzahl \u00FCberschritten
MAIL_CODE_NOT_FOUND=Verifizierungscode nicht gefunden
MAIL_CODE_EXPIRED=Verifizierungscode abgelaufen
MAIL_CODE_USED=Verifizierungscode bereits verwendet
MAIL_CODE_NOT_CORRECT=Verifizierungscode falsch
MAIL_IS_EXISTS=E-Mail-Adresse wird bereits verwendet
NOTIFY_TEMPLATE_NOT_EXISTS=Interne Nachrichtenvorlage existiert nicht
NOTIFY_TEMPLATE_CODE_DUPLICATE=Interne Nachrichtenvorlage mit Code [{}] existiert bereits
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Vorlagenparameter [{}] fehlt
AGENT_NOT_EXISTS=Agent existiert nicht
AGENT_INVITE_CODE_EXISTS=Einladungscode existiert bereits
AGENT_HAS_DESCENDANT=Agent hat Unter-Agenten, kann nicht gel\u00F6scht werden
AGENT_ANCESTOR_NOT_AVAILABLE=Eltern-Agent nicht verf\u00FCgbar
AUTH_NOT_EXISTS=Mitglieder-Zertifizierung existiert nicht
CURRENCY_NOT_EXISTS=W\u00E4hrung existiert nicht
CURRENCYNOTRATE=Diese W\u00E4hrung hat tempor\u00E4r keinen Wechselkurs
BANNER_NOT_EXISTS=Banner existiert nicht
TENANT_SERVER_NAME_NOT_EXISTS=System-Mandanten-Domain existiert nicht
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Mandanten-W\u00F6rterbuchdaten existieren nicht
TIME_CONTRACT_AMOUNT_LESS=Kaufbetrag kleiner als Mindestbetrag
TIME_CONTRACT_RECORD_NOT_EXISTS=Zeitbegrenzter Kontrakthandel-Aufzeichnung existiert nicht
TRADE_DURATION_NOT_EXISTS=Auftragsdauer-Konfiguration existiert nicht
TRADE_MULTIPLE_NOT_EXISTS=Auftragsmultiplikator-Konfiguration existiert nicht
USER_ACCOUNT_NOT_EMPTY=Konto darf nicht leer sein
AGENT_HAS_NOT_ANCESTOR=Verschobener Agent hat keinen Eltern-Agent
USER_EMAIL_NOT_EXISTS=E-Mail existiert nicht
Mail_CODE_SEND_FAIL=E-Mail-Verifizierungscode-Versand fehlgeschlagen
GOOGLE_SECRET_BINDING_EXPIRED=Gebundener Google-Schl\u00FCssel ist abgelaufen, bitte erneut abrufen
GOOGLE_CODE_ERROR=Google Authenticator-Code falsch
ACCOUNT_IS_ERROR=Anmeldung fehlgeschlagen, Konto existiert nicht
GOOGLE_SECRET_IS_NOT_BINDING=Google-Schl\u00FCssel nicht gebunden, bitte zuerst binden
TRADE_CONTRACT_RECORD_NOT_EXISTS=Kontraktauftrags-Aufzeichnung existiert nicht
TRADE_CONTRACT_CONFIG_ERROR=Kontraktkonfigurationsfehler
ARG_ORDER_STATUS_IS_EMPTY=Auftragsstatus darf nicht leer sein
ARG_LEVERAGE_IS_EMPTY=Hebel-Parameter darf nicht leer sein
ARG_ORDER_TYPE_IS_EMPTY=Auftragstyp-Parameter darf nicht leer sein
ARG_VALUE_ERROR=Parameterwert-Fehler
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=Kontraktauftrag bereits storniert
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=Kontraktauftrag bereits ausgef\u00FChrt
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=Kontrakt bereits geschlossen
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Auftragsw\u00E4hrungs-Wechselkurs-Fehler
USER_FORBIDDEN_FUNC=Diese Funktion ist deaktiviert
IMAGE_URL_ERROR=Bild-Link-Adresse-Fehler
USER_WITHDRAW_HAS_SUCCESS=\u00C4nderung bereits erfolgreicher Bestellung verboten 