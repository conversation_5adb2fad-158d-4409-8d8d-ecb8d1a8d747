COUNTRY_TH=<PERSON>yland
COUNTRY_JP=Japonya
COUNTRY_KR=Kore
COUNTRY_PH=Filipinler
COUNTRY_VN=Vietnam
COUNTRY_CN=\u00C7in
COUNTRY_GB=Birle\u015Fik Krall\u0131k
COUNTRY_FR=Fransa
COUNTRY_ES=\u0130spanya
COUNTRY_US=Amerika
COUNTRY_DE=Almanya
COUNTRY_RU=Rusya
COUNTRY_CA=Kanada
COUNTRY_TR=T\u00FCrkiye
COUNTRY_PT=Portekiz
COUNTRY_MA=Fas
COUNTRY_DZ=Cezayir
COUNTRY_IT=\u0130talya
COUNTRY_CO=Kolombiya
COUNTRY_MX=Meksika
COUNTRY_CH=\u0130svi\u00E7re
COUNTRY_BE=Bel\u00E7ika
COUNTRY_AR=Arjantin
COUNTRY_NO=Norve\u00E7
COUNTRY_HK=Hong Kong
BANK=Banka
ABD=Birle\u015Fik Devletler
BANKA=banka kart\u0131
CRYPTO=Kripto Para Birimi
USER_CERTIFIED_STATUS_NOT=Unauthenticated
USER_CERTIFIED_STATUS_HANDLING=Yetkilendirme
USER_CERTIFIED_STATUS_SUCCESS=Yetkilendirildi
USER_CERTIFIED_STATUS_FAIL=Kimlik do\u011Frulama ba\u015Far\u0131s\u0131z oldu
ACCOUNT_NOT_EMPTY=Hesap bo\u015F olamaz
PASSWORD_NOT_EMPTY=Parola bo\u015F olamaz
PASSWORD_NOT_LENGTH_6_16=Parola uzunlu\u011Fu 6-16 karakterdir
PASSWORD_FORMATTER_ERROR=\u015Eifre bi\u00E7imi say\u0131lar ve harflerdir
ACCOUNT_TYPE_ERROR=Hesap t\u00FCr\u00FC hatas\u0131
MAIL_FORMATTER_ERROR=E-posta bi\u00E7imi hatas\u0131
MOBILE_FORMATTER_ERROR=Mobil numara format hatas\u0131
AREA_NOT_EMPTY=B\u00F6lge bo\u015F olamaz
MAIL_SCENE_ERROR=SMS g\u00F6nderme senaryosu hatas\u0131
SMS_SCENE_ERROR=SMS g\u00F6nderme senaryosu hatas\u0131
REAL_NAME_NOT_EMPTY=Ger\u00E7ek ad bo\u015F olamaz
CERTIFICATION_TYPE_ERROR=Kimlik do\u011Frulama t\u00FCr\u00FC hatas\u0131
CERTIFICATION_CODE_NOT_EMPTY=ID numaras\u0131 bo\u015F olamaz
CERTIFICATION_FRONT_NOT_EMPTY=ID \u00F6n foto\u011Fraf bo\u015F olamaz
CERTIFICATION_BACK_NOT_EMPTY=ID arka foto\u011Fraf bo\u015F olamaz
TRADE_PAIR_NOT_EMPTY=L\u00FCtfen i\u015Flem \u00E7ifti se\u00E7in
FUNDS_RECORD_OP_TYPE=Fon t\u00FCr\u00FC hatas\u0131
CURRENCY_NOT_EMPTY=L\u00FCtfen para birimi se\u00E7in
PAY_METHOD_ERROR=L\u00FCtfen \u00F6deme y\u00F6ntemini se\u00E7in
RECHARGE_AMOUNT_ERROR=L\u00FCtfen y\u00FCkleme miktar\u0131n\u0131 girin
WITHDRAW_AMOUNT_ERROR=\u00C7ekme tutar\u0131n\u0131 tam olarak girin
FUNDS_PASSWORD_ERROR=Fon \u015Fifresi hatas\u0131
WALLET_NOT_EMPTY=L\u00FCtfen c\u00FCzdan se\u00E7in
AUTH_CODE_NOT_EMPTY=L\u00FCtfen do\u011Frulama kodunu girin
AVATAR_FORMATTER_ERROR=L\u00FCtfen avatar se\u00E7in
WALLET_TYPE_ERROR=C\u00FCzdan t\u00FCr\u00FC hatas\u0131
WALLET_NAME_NOT_EMPTY=L\u00FCtfen c\u00FCzdan ad\u0131n\u0131 girin
WALLET_ACCOUNT_NOT_EMPTY=L\u00FCtfen c\u00FCzdan hesap numaras\u0131n\u0131 girin
WALLET_TYPE_NAME_NOT_EMPTY=L\u00FCtfen banka ad\u0131n\u0131 girin
ASSET_Type_ERROR=Varl\u0131k t\u00FCr\u00FC hatas\u0131
KEY_NOT_EMPTY=Anahtar s\u00F6zc\u00FCk bo\u015F olamaz
AMOUNT_NOT_EMPTY=Miktar hatas\u0131
TRADE_DIRECT_ERROR=\u0130\u015Flem y\u00F6n\u00FC hatas\u0131
TRADE_DURATION_ERROR=\u0130\u015Flem s\u00FCresi hatas\u0131
TRADE_SEND_TIME_ERROR=\u0130\u015Flem g\u00F6nderme s\u00FCresi hatas\u0131
TRADE_PRICE_TIME_ERROR=\u0130\u015Flem fiyat\u0131 zaman hatas\u0131
TRADE_PAGE_TYPE_ERROR=S\u0131n\u0131fland\u0131rma hatas\u0131
SUCCESS=Ba\u015Far\u0131
WAITHANDLE=\u0130\u015Fleme
FAILURE=ba\u015Far\u0131s\u0131z
PENDING=Beklemede
BAD_REQUEST=Yanl\u0131\u015F istek
UNKNOW_AUTHORIZED=Bilinmeyen yetkilendirme t\u00FCr\u00FC [{}]
TOKEN_NOT_SUPPORT_MODE=Token aray\u00FCz\u00FC [{}] yetkilendirme modunu desteklemiyor
CLIENT_ERROR=Client_id veya client_secret do\u011Fru \u015Fekilde aktar\u0131lmad\u0131
TOKEN_REFRESH_INVALID=Ge\u00E7ersiz yenileme belirteci
TOKEN_REFRESH_CLIENT_ERROR=Yenileme belirtecinin yanl\u0131\u015F istemci numaras\u0131
TOKEN_REFRESH_EXPIRE=Yenileme belirtecinin s\u00FCresi doldu
TOKEN_NOT_EXISTS=Eri\u015Fim belirteci mevcut de\u011Fil
TOKEN_EXPIRE=Eri\u015Fim belirtecinin s\u00FCresi doldu
GRANT_RESPONSE_TYPE_ERROR=Yan\u0131t_t\u00FCr\u00FC parametre de\u011Feri yaln\u0131zca kod ve belirtece izin verir
UNAUTHORIZED=Hesapta oturum a\u00E7\u0131lmam\u0131\u015F
FORBIDDEN=Bu i\u015Flem i\u00E7in izin yok
NOT_FOUND=\u0130stenen kaynak mevcut de\u011Fil
METHOD_NOT_ALLOWED=\u0130stek y\u00F6ntemine izin verilmiyor
K\u0130L\u0130TL\u0130=Locked
TOO_MANY_REQUESTS=\u0130stekler \u00E7ok s\u0131k, l\u00FCtfen daha sonra tekrar deneyin
INTERNAL_SERVER_ERROR=Sistem anormalli\u011Fi
NOT_IMPLEMENTED=Fonksiyon uygulanmad\u0131/etkin de\u011Fil
REPEATED_REQUESTS=Tekrarlanan istekler, l\u00FCtfen daha sonra tekrar deneyin
DEMO_DENY=Demo modu, yazma i\u015Flemi yasak
VALUE_ERROR=De\u011Fer hatas\u0131
GOOGLE_AUTH_NOT_BIND=Google Authenticator ba\u011Fl\u0131 de\u011Fil
GOOGLE_AUTH_CODE_ERROR=Google do\u011Frulama kodu hatas\u0131
SCHEDULER_JOB_STOP=[Zamanlanm\u0131\u015F g\u00F6rev - devre d\u0131\u015F\u0131][Referans https://doc.iocoder.cn/job/ Etkinle\u015Ftir]
CANDLE_TABLE_NAME_NOT_AVAILABLE=K sat\u0131r\u0131 tablo ad\u0131 ge\u00E7ersiz
CANDLE_BAR_VALUE_ERROR=Bar parametresi ge\u00E7ersiz
CANDLE_PRICE_ERROR=Fiyat hatas\u0131 al
TRADE_PAIR_NOT_EXISTS=Ticaret \u00E7ifti mevcut de\u011Fil
TRADE_PAIR_EXISTS=Ticaret \u00E7ifti zaten mevcut
TRADE_PAIR_TENANT_NOT_EXISTS=Kirac\u0131 bu i\u015Flem \u00E7iftine sahip de\u011Fil
TRADE_PAIR_TENANT_EXISTS=Kirac\u0131 i\u015Flem \u00E7ifti zaten mevcut
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=Kirac\u0131 i\u015Flem \u00E7ifti varl\u0131k t\u00FCr\u00FC yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
TRADE_TENANT_NEED_DEFAULT=Varsay\u0131lan bir kirac\u0131 i\u015Flem \u00E7ifti olmal\u0131d\u0131r
TRADE_TENANT_DUPLICATE_DEFAULT=Kirac\u0131n\u0131n varsay\u0131lan i\u015Flem \u00E7ifti zaten mevcut
CONFIG_NOT_EXISTS=Parametre yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
CONFIG_KEY_DUPLICATE=Parametre yap\u0131land\u0131rma anahtar\u0131 Yineleniyor
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=Sistem yerle\u015Fik tipinin parametre yap\u0131land\u0131rmas\u0131 silinemez
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Parametre yap\u0131land\u0131rmas\u0131 elde edilemedi, neden: G\u00F6r\u00FCnmez yap\u0131land\u0131rma elde edilmesine izin verilmiyor
JOB_NOT_EXISTS=Zamanlanm\u0131\u015F g\u00F6rev mevcut de\u011Fil
JOB_HANDLER_EXISTS=Zamanlanm\u0131\u015F g\u00F6revin i\u015Flemcisi zaten mevcut
JOB_CHANGE_STATUS_INVALID=Sadece a\u00E7\u0131k veya kapal\u0131 durum de\u011Fi\u015Fikli\u011Fine izin verilir
JOB_CHANGE_STATUS_EQUALS=Zamanlanm\u0131\u015F g\u00F6rev zaten bu durumda, herhangi bir de\u011Fi\u015Fiklik gerekmiyor
JOB_UPDATE_ONLY_NORMAL_STATUS=Sadece a\u00E7\u0131k durumdaki g\u00F6revler de\u011Fi\u015Ftirilebilir
JOB_CRON_EXPRESSION_VALID=CRON ifadesi yanl\u0131\u015F
JOB_HANDLER_BEAN_NOT_EXISTS=Zamanlanm\u0131\u015F g\u00F6revin i\u015Flemci \u00E7ekirde\u011Fi mevcut de\u011Fil
JOB_HANDLER_BEAN_TYPE_ERROR=Zamanlanan g\u00F6revin i\u015Flemci fasulye t\u00FCr\u00FC yanl\u0131\u015F ve JobHandler aray\u00FCz\u00FC uygulanm\u0131yor
API_ERROR_LOG_NOT_FOUND=API hata g\u00FCnl\u00FC\u011F\u00FC mevcut de\u011Fil
API_ERROR_LOG_PROCESSED=API hata g\u00FCnl\u00FC\u011F\u00FC i\u015Flendi
FILE_PATH_EXISTS=Dosya yolu zaten var
FILE_NOT_EXISTS=Dosya mevcut de\u011Fil
FILE_IS_EMPTY=Dosya bo\u015F
CODEGEN_TABLE_EXISTS=Tablo tan\u0131m\u0131 zaten mevcut
CODEGEN_IMPORT_TABLE_NULL=\u0130thal edilen tablo mevcut de\u011Fil
CODEGEN_IMPORT_COLUMNS_NULL=\u0130thal edilen alan mevcut de\u011Fil
CODEGEN_TABLE_NOT_EXISTS=Tablo tan\u0131m\u0131 mevcut de\u011Fil
CODEGEN_COLUMN_NOT_EXISTS=Alan tan\u0131m\u0131 mevcut de\u011Fil
CODEGEN_SYNC_COLUMNS_NULL=Senkronize edilmi\u015F alan mevcut de\u011Fil
CODEGEN_SYNC_NONE_CHANGE=Senkronizasyon ba\u015Far\u0131s\u0131z, de\u011Fi\u015Fiklik yok
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Veritaban\u0131 tablo a\u00E7\u0131klamas\u0131 doldurulmam\u0131\u015F
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Veritaban\u0131 tablo alan\u0131 ({}) yorumu doldurulmam\u0131\u015F
CODEGEN_MASTER_TABLE_NOT_EXISTS=Ana tablo (id={}) tan\u0131m\u0131 mevcut de\u011Fil, l\u00FCtfen kontrol edin
CODEGEN_SUB_COLUMN_NOT_EXISTS=Alt tablo alan\u0131 (id={}) mevcut de\u011Fil, l\u00FCtfen kontrol edin
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=Ana tablo kodu olu\u015Fturma ba\u015Far\u0131s\u0131z oldu, nedeni: alt tablosu yok
FILE_CONFIG_NOT_EXISTS=Dosya yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
FILE_CONFIG_DELETE_FAIL_MASTER=Bu dosya yap\u0131land\u0131rmas\u0131 silinemez, nedeni: ana yap\u0131land\u0131rmad\u0131r, silinmesi dosyalar\u0131n y\u00FCklenememesine neden olur
DATA_SOURCE_CONFIG_NOT_EXISTS=Veri kayna\u011F\u0131 Yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
DATA_SOURCE_CONFIG_NOT_OK=Veri kayna\u011F\u0131 yap\u0131land\u0131rmas\u0131 yanl\u0131\u015F, ba\u011Flan\u0131lam\u0131yor
DEMO01_CONTACT_NOT_EXISTS=\u00D6rnek ki\u015Fi mevcut de\u011Fil
DEMO02_CATEGORY_NOT_EXISTS=\u00D6rnek kategori mevcut de\u011Fil
DEMO02_CATEGORY_EXITS_CHILDREN=Alt \u00F6rnek kategorisi var, silinemez
DEMO02_CATEGORY_PARENT_NOT_EXITS=Ebeveyn \u00F6rnek kategorisi mevcut de\u011Fil
DEMO02_CATEGORY_PARENT_ERROR=Kendisini ana \u00F6rnek kategorisi olarak ayarlayam\u0131yor
DEMO02_CATEGORY_NAME_DUPLICATE=Ayn\u0131 ada sahip \u00F6rnek kategori zaten mevcut
DEMO02_CATEGORY_PARENT_IS_CHILD=Kendi alt \u00F6rnek kategorisini ana \u00F6rnek kategorisi olarak ayarlayamaz
DEMO03_STUDENT_NOT_EXISTS=\u00D6\u011Frenci mevcut de\u011Fil
DEMO03_GRADE_NOT_EXISTS=\u00D6\u011Frenci s\u0131n\u0131f\u0131 mevcut de\u011Fil
DEMO03_GRADE_EXISTS=\u00D6\u011Frenci s\u0131n\u0131f\u0131 zaten var
AREA_NOT_EXISTS=B\u00F6lge mevcut de\u011Fil
USER_NOT_EXISTS=Kullan\u0131c\u0131 mevcut de\u011Fil
USER_MOBILE_NOT_EXISTS=Mobil numara kay\u0131tl\u0131 de\u011Fil
USER_MOBILE_USED=Mobil numara kullan\u0131ld\u0131
USER_ACCOUNT_USED=Hesap kullan\u0131ld\u0131
USER_EMAIL_USED=E-posta kullan\u0131ld\u0131
USER_POINT_NOT_ENOUGH=Kullan\u0131c\u0131 puan bakiyesi yetersiz
USER_OLD_PASSWORD_NOT_MATCH=Eski parola hatas\u0131
USER_BALANCE_ERROR=Kullan\u0131c\u0131 bakiyesi hatas\u0131
USER_CONFIG_NOT_SUPPORTED=Kullan\u0131c\u0131 yap\u0131land\u0131rma \u00F6\u011Feleri desteklenmiyor
AUTH_LOGIN_BAD_CREDENTIALS=Giri\u015F ba\u015Far\u0131s\u0131z, hesap parolas\u0131 yanl\u0131\u015F
AUTH_LOGIN_USER_DISABLED=Giri\u015F ba\u015Far\u0131s\u0131z, hesap devre d\u0131\u015F\u0131
AUTH_ACCOUNT_FORMAT_ERROR=Kimlik do\u011Frulama hesab\u0131 bi\u00E7im hatas\u0131
TAG_NOT_EXISTS=Etiket mevcut de\u011Fil
TAG_NAME_EXISTS=Tag zaten mevcut
TAG_HAS_USER=Kullan\u0131c\u0131, kullan\u0131c\u0131 etiketi alt\u0131nda var, silinemez
POINT_RECORD_BIZ_NOT_SUPPORT=Kullan\u0131c\u0131 puan kayd\u0131 i\u015F t\u00FCr\u00FC desteklenmiyor
SIGN_IN_CONFIG_NOT_EXISTS=Sign-in kural\u0131 mevcut de\u011Fil
SIGN_IN_CONFIG_EXISTS=Sign-in days kural\u0131 zaten mevcut
SIGN_IN_RECORD_TODAY_EXISTS=Bug\u00FCn giri\u015F yap\u0131ld\u0131, l\u00FCtfen tekrar giri\u015F yapmay\u0131n
LEVEL_NOT_EXISTS=Kullan\u0131c\u0131 seviyesi mevcut de\u011Fil
LEVEL_NAME_EXISTS=Kullan\u0131c\u0131 seviyesi ad\u0131 kullan\u0131lm\u0131\u015F
LEVEL_VALUE_EXISTS=Kullan\u0131c\u0131 seviyesi de\u011Feri kullan\u0131ld\u0131
LEVEL_EXPERIENCE_MIN=Y\u00FCkseltme deneyimi bir \u00F6nceki seviye i\u00E7in belirlenen y\u00FCkseltme deneyiminden b\u00FCy\u00FCk olmal\u0131d\u0131r
LEVEL_EXPERIENCE_MAX=Y\u00FCkseltme deneyimi bir sonraki seviye i\u00E7in belirlenen y\u00FCkseltme deneyiminden daha az olmal\u0131d\u0131r
LEVEL_HAS_USER=Kullan\u0131c\u0131lar kullan\u0131c\u0131 seviyesi alt\u0131nda mevcuttur, silinemez
EXPERIENCE_BIZ_NOT_SUPPORT=Kullan\u0131c\u0131 deneyimi i\u015F t\u00FCr\u00FC desteklenmiyor
GROUP_NOT_EXISTS=Kullan\u0131c\u0131 grubu mevcut de\u011Fil
GROUP_HAS_USER=Kullan\u0131c\u0131lar kullan\u0131c\u0131 grubunda var, silinemez
USER_WITHDRAW_NOT_EXISTS=\u00DCye Para \u00C7ekme mevcut de\u011Fil
USER_BALANCE_NOT_ENOUGH=Yetersiz bakiye
USER_WITHDRAW_HAS_HANDLE=\u00C7ekme i\u015Flemi ger\u00E7ekle\u015Ftirildi
USER_WITHDRAW_LESS_MIN_AMOUNT=\u00C7ekilen miktar [{}]'den b\u00FCy\u00FCk olmal\u0131d\u0131r
USER_WITHDRAW_LESS_MAX_AMOUNT=\u00C7ekilen miktar [{}]'dan az olmal\u0131d\u0131r
USER_WITHDRAW_LESS_MAX_PROCESS=\u00C7ekilme s\u00FCrecinde olan bir sipari\u015Finiz var
USER_FROZEN_BALANCE_NOT_ENOUGH=Yetersiz bakiye
USER_ASSETS_SPOT_NOT_EXISTS=Kullan\u0131c\u0131 varl\u0131klar\u0131 mevcut de\u011Fil
USER_FAVORITE_TRADE_PAIR_EXISTS=Favori i\u015Flem \u00E7ifti mevcut
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=Favori i\u015Flem \u00E7ifti mevcut de\u011Fil
USER_RECHARGE_NOT_EXISTS=Recharge kayd\u0131 mevcut de\u011Fil
USER_RECHARGE_HAS_HANDLE=Recharge kayd\u0131 i\u015Flendi
USER_WALLET_NOT_EXISTS=\u00DCye c\u00FCzdan\u0131 mevcut de\u011Fil
USER_SPOT_ORDER_NOT_EXISTS=\u00DCye spot emri mevcut de\u011Fil
USER_TRANSACTIONS_NOT_EXISTS=\u00DCye faturas\u0131 mevcut de\u011Fil
USER_MARGIN_ORDER_NOT_EXISTS=\u00DCye s\u00F6zle\u015Fme emri mevcut de\u011Fil
USER_MARGIN_CONFIG_NOT_EXISTS=\u00DCye s\u00F6zle\u015Fme yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
USER_CERTIFICATION_NOT_EXISTS=\u00DCye kimlik do\u011Frulama bilgileri mevcut de\u011Fil
USER_CERTIFICATION_BEEN_HANDLE=\u00DCye kimlik do\u011Frulama bilgileri i\u015Flendi
USER_CERTIFICATION_STATUS_SUCCESS=\u00DCye kimlik do\u011Frulama bilgileri do\u011Fruland\u0131
USER_CERTIFICATION_STATUS_HANDLING=\u00DCye kimlik do\u011Frulama bilgileri inceleme alt\u0131nda
USER_CERTIFICATION_NOT_VERIFY=Identity [{}], i\u015Fleme izin verilmiyor
USER_CERTIFICATION_VERIFYING=Kimlik do\u011Frulamas\u0131 s\u0131ras\u0131nda i\u015Fleme izin verilmiyor
USER_CERTIFICATION_VERIFY_FAILURE=Kimlik do\u011Frulama ba\u015Far\u0131s\u0131z oldu, i\u015Fleme izin verilmiyor
LEVEL_CONFIG_NOT_EXISTS=\u00DCye seviyesi yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=\u00DCye varsay\u0131lan seviye yap\u0131land\u0131rmas\u0131 silinemez
LEVEL_CONFIG_NAME_EXISTS=Seviye ad\u0131 Zaten var oldu\u011Funu s\u00F6yl\u00FCyor
USER_FUND_PASSWORD_NOT_EXISTS=Bir fon \u015Fifresi belirlemediniz ve de\u011Fi\u015Ftiremezsiniz
USER_FUND_PASSWORD_ERROR=Yanl\u0131\u015F fon \u015Fifresi
FUNDS_RECORD_NOT_EXISTS=Fon kayd\u0131 mevcut de\u011Fil
USER_RECHARGE_LESS_MAX_PROCESS=Geri \u00E7ekilmekte olan [{}] sipari\u015Finiz var
AUTH_LOGIN_CAPTCHA_CODE_ERROR=Do\u011Frulama kodu yanl\u0131\u015F, neden: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token'\u0131n s\u00FCresi doldu
AUTH_MOBILE_NOT_EXISTS=Mobil numaras\u0131 mevcut de\u011Fil
MENU_NAME_DUPLICATE=Bu isimde bir men\u00FC zaten mevcut
MENU_PARENT_NOT_EXISTS=\u00DCst men\u00FC mevcut de\u011Fil
MENU_PARENT_ERROR=Kendisini \u00FCst men\u00FC olarak ayarlayam\u0131yor
MENU_NOT_EXISTS=Men\u00FC mevcut de\u011Fil
MENU_EXISTS_CHILDREN=Alt men\u00FC var ve silinemez
MENU_PARENT_NOT_DIR_OR_MENU=\u00DCst men\u00FC bir dizin veya men\u00FC olmal\u0131d\u0131r
ROLE_NOT_EXISTS=Rol mevcut de\u011Fil
ROLE_NAME_DUPLICATE=[{}] ad\u0131nda bir rol zaten var
ROLE_CODE_DUPLICATE=[{}] kodlu bir rol zaten mevcut
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=Sistem Rol\u00FC taraf\u0131ndan yerle\u015Fik olan bir t\u00FCr\u00FC \u00E7al\u0131\u015Ft\u0131ramaz
ROLE_IS_DISABLE=[{}] adl\u0131 rol devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131
ROLE_ADMIN_CODE_ERROR=[{}] kodu kullan\u0131lamaz
USER_USERNAME_EXISTS=Hesap kullan\u0131lam\u0131yor
USER_MOBILE_EXISTS=Telefon numaras\u0131 zaten mevcut
USER_EMAIL_EXISTS=E-posta adresi zaten mevcut
USER_IMPORT_LIST_IS_EMPTY=\u0130\u00E7e aktar\u0131lan kullan\u0131c\u0131 verileri bo\u015F olamaz
USER_PASSWORD_FAILED=Kullan\u0131c\u0131 parolas\u0131 do\u011Frulamas\u0131 ba\u015Far\u0131s\u0131z oldu
USER_IS_DISABLE=[{}] adl\u0131 kullan\u0131c\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131
USER_COUNT_MAX=Kullan\u0131c\u0131 olu\u015Fturulamad\u0131, nedeni: kirac\u0131n\u0131n maksimum kirac\u0131 kotas\u0131n\u0131n a\u015F\u0131lmas\u0131 [{}]
DEPT_NAME_DUPLICATE=Bu isimde bir departman zaten mevcut
DEPT_PARENT_NOT_EXITS=Ana departman mevcut de\u011Fil
DEPT_NOT_FOUND=Ge\u00E7erli departman mevcut de\u011Fil
DEPT_EXITS_CHILDREN=Alt departmanlar mevcut ve silinemez
DEPT_PARENT_ERROR=Kendisini ana departman olarak ayarlayam\u0131yor
DEPT_EXISTS_USER=B\u00F6l\u00FCmde \u00E7al\u0131\u015Fanlar var ve silinemez
DEPT_NOT_ENABLE=Departman [{}] a\u00E7\u0131k durumda de\u011Fil ve se\u00E7ime izin verilmiyor
DEPT_PARENT_IS_CHILD=Kendi alt departman\u0131n\u0131z\u0131 ana departman olarak ayarlayamazs\u0131n\u0131z
POST_NOT_FOUND=Ge\u00E7erli konum mevcut de\u011Fil
POST_NOT_ENABLE=Konum [{}] a\u00E7\u0131k durumda de\u011Fil, se\u00E7ime izin verilmiyor
POST_NAME_DUPLICATE=Bu isimde bir pozisyon zaten mevcut
POST_CODE_DUPLICATE=Bu tan\u0131mlay\u0131c\u0131ya sahip bir pozisyon zaten mevcut
DICT_TYPE_NOT_EXISTS=Ge\u00E7erli s\u00F6zl\u00FCk t\u00FCr\u00FC mevcut de\u011Fil
DICT_TYPE_NOT_ENABLE=S\u00F6zl\u00FCk t\u00FCr\u00FC a\u00E7\u0131k durumda de\u011Fil, se\u00E7ime izin verilmiyor
DICT_TYPE_NAME_DUPLICATE=Bu ada sahip bir s\u00F6zl\u00FCk t\u00FCr\u00FC zaten var
DICT_TYPE_TYPE_DUPLICATE=Bu t\u00FCre sahip bir s\u00F6zl\u00FCk t\u00FCr\u00FC zaten var
DICT_TYPE_HAS_CHILDREN=Silinemez, bu s\u00F6zl\u00FCk t\u00FCr\u00FCnde hala s\u00F6zl\u00FCk verileri vard\u0131r
DICT_DATA_NOT_EXISTS=Ge\u00E7erli s\u00F6zl\u00FCk verisi mevcut de\u011Fil
DICT_DATA_NOT_ENABLE=S\u00F6zl\u00FCk verileri [{}] a\u00E7\u0131k durumda de\u011Fil, se\u00E7ime izin verilmiyor
DICT_DATA_VALUE_DUPLICATE=Bu de\u011Fere sahip s\u00F6zl\u00FCk verisi zaten mevcut
NOTICE_NOT_FOUND=Mevcut bildirim duyurusu mevcut de\u011Fil
SMS_CHANNEL_NOT_EXISTS=SMS kanal\u0131 mevcut de\u011Fil
SMS_CHANNEL_DISABLE=SMS kanal\u0131 a\u00E7\u0131k durumda de\u011Fil, se\u00E7ime izin verilmiyor
SMS_CHANNEL_HAS_CHILDREN=Silinemez, bu SMS kanal\u0131nda hala SMS \u015Fablonlar\u0131 var
SMS_TEMPLATE_NOT_EXISTS=SMS \u015Fablonu mevcut de\u011Fil Var
SMS_TEMPLATE_CODE_DUPLICATE=[{}] olarak kodlanan SMS \u015Fablonu zaten mevcut
SMS_TEMPLATE_API_ERROR=SMS API \u015Fablon \u00E7a\u011Fr\u0131s\u0131 ba\u015Far\u0131s\u0131z oldu, nedeni: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=SMS API \u015Fablonu kullan\u0131lam\u0131yor, nedeni: inceleme alt\u0131nda
SMS_TEMPLATE_API_AUDIT_FAIL=SMS API \u015Fablonu kullan\u0131lam\u0131yor, nedeni: onay ba\u015Far\u0131s\u0131z oldu, {}
SMS_TEMPLATE_API_NOT_FOUND=SMS API \u015Fablonu kullan\u0131lam\u0131yor, nedeni: \u015Fablon mevcut de\u011Fil
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=\u015Eablon parametresi [{}] eksik
SMS_CODE_NOT_FOUND=Do\u011Frulama kodu mevcut de\u011Fil
SMS_CODE_EXPIRED=Do\u011Frulama kodunun s\u00FCresi doldu
SMS_CODE_USED=Do\u011Frulama kodu kullan\u0131ld\u0131
SMS_CODE_NOT_CORRECT=Do\u011Frulama kodu yanl\u0131\u015F
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=G\u00FCn ba\u015F\u0131na g\u00F6nderilen SMS mesaj\u0131 say\u0131s\u0131 a\u015F\u0131ld\u0131
SMS_CODE_SEND_TOO_FAST=SMS mesajlar\u0131 \u00E7ok s\u0131k g\u00F6nderiliyor
TENANT_NOT_EXISTS=Kirac\u0131 mevcut de\u011Fil
TENANT_DISABLE=[{}] adl\u0131 kirac\u0131 devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131
TENANT_EXPIRE=[{}] adl\u0131 kirac\u0131n\u0131n s\u00FCresi doldu
TENANT_CAN_NOT_UPDATE_SYSTEM=Sistem kirac\u0131s\u0131 de\u011Fi\u015Ftirilemez, silinemez, vb.
TENANT_CODE_DUPLICATE=Kirac\u0131 kodu [{}] olan kirac\u0131 zaten mevcut
TENANT_WEBSITE_DUPLICATE=Alan ad\u0131 [{}] olan kirac\u0131 zaten mevcut
TENANT_PACKAGE_NOT_EXISTS=Kirac\u0131 paketi mevcut de\u011Fil
TENANT_PACKAGE_USED=Kirac\u0131 bu paketi kullan\u0131yor, l\u00FCtfen silmeyi denemeden \u00F6nce kirac\u0131 i\u00E7in paketi s\u0131f\u0131rlay\u0131n
TENANT_PACKAGE_DISABLE=[{}] adl\u0131 kirac\u0131 paketi devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131
OAUTH2_CLIENT_NOT_EXISTS=OAuth2 istemcisi mevcut de\u011Fil
OAUTH2_CLIENT_EXISTS=OAuth2 istemci kimli\u011Fi zaten mevcut
OAUTH2_CLIENT_DISABLE=OAuth2 istemcisi devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=Yetkilendirme t\u00FCr\u00FC desteklenmiyor
OAUTH2_CLIENT_SCOPE_OVER=Yetkilendirme kapsam\u0131 \u00E7ok b\u00FCy\u00FCk
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=Ge\u00E7ersiz redirect_uri: [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=Ge\u00E7ersiz client_secret: {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=\u0130stemci_kimli\u011Fi e\u015Fle\u015Fmiyor
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=Y\u00F6nlendirme_urisi e\u015Fle\u015Fmiyor
OAUTH2_GRANT_STATE_MISMATCH=Durum e\u015Fle\u015Fmiyor
OAUTH2_GRANT_CODE_NOT_EXISTS=Kod mevcut de\u011Fil
OAUTH2_CODE_NOT_EXISTS=Kod mevcut de\u011Fil
OAUTH2_CODE_EXPIRE=Do\u011Frulama kodunun s\u00FCresi doldu
MAIL_ACCOUNT_NOT_EXISTS=E-posta hesab\u0131 mevcut de\u011Fil
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=Silinemez, e-posta hesab\u0131nda hala e-posta \u015Fablonlar\u0131 var Board
MAIL_TEMPLATE_NOT_EXISTS=Posta \u015Fablonu mevcut de\u011Fil
MAIL_TEMPLATE_CODE_EXISTS=Mail \u015Fablon kodu[{}] zaten var
MAIL_SEND_TEMPLATE_PARAM_MISS=\u015Eablon parametresi[{}] eksik
MAIL_SEND_MAIL_NOT_EXISTS=E-posta mevcut de\u011Fil
MAIL_CODE_SEND_TOO_FAST=E-posta \u00E7ok s\u0131k g\u00F6nderiliyor
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=G\u00F6nderilen g\u00FCnl\u00FCk e-posta say\u0131s\u0131n\u0131 a\u015Ft\u0131
MAIL_CODE_NOT_FOUND=Do\u011Frulama kodu mevcut de\u011Fil
MAIL_CODE_EXPIRED=Do\u011Frulama kodunun s\u00FCresi doldu
MAIL_CODE_USED=Do\u011Frulama kodu kullan\u0131ld\u0131
MAIL_CODE_NOT_CORRECT=Do\u011Frulama kodu yanl\u0131\u015F
MAIL_IS_EXISTS=E-posta kullan\u0131ld\u0131
NOTIFY_TEMPLATE_NOT_EXISTS=Dahili mesaj \u015Fablonu mevcut de\u011Fil
NOTIFY_TEMPLATE_CODE_DUPLICATE=[{}] olarak kodlanm\u0131\u015F dahili mesaj \u015Fablonu zaten mevcut
NOTIFY_SEND_TEMPLATE_PARAM_MISS=\u015Eablon parametresi[{}] eksik
AGENT_NOT_EXISTS=Ajans mevcut de\u011Fil
AGENT_INVITE_CODE_EXISTS=Davet kodu zaten mevcut
AGENT_HAS_DESCENDANT=Temsilcinin alt\u0131nda alt temsilciler vard\u0131r ve silinemez
AGENT_ANCESTOR_NOT_AVAILABLE=Ebeveyn temsilci kullan\u0131lam\u0131yor
AUTH_NOT_EXISTS=\u00DCyelik sertifikas\u0131 mevcut de\u011Fil
CURRENCY_NOT_EXISTS=Para birimi mevcut de\u011Fil
CURRENCYNOTRATE=Bu para birimi i\u00E7in d\u00F6viz kuru yok
BANNER_NOT_EXISTS=BANNER mevcut de\u011Fil
TENANT_SERVER_NAME_NOT_EXISTS=Sistem kirac\u0131s\u0131 etki alan\u0131 ad\u0131 mevcut de\u011Fil
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Kirac\u0131 s\u00F6zl\u00FCk verileri mevcut de\u011Fil
TIME_CONTRACT_AMOUNT_LESS=Sat\u0131n alma tutar\u0131 minimum tutardan az
TIME_CONTRACT_RECORD_NOT_EXISTS=Zaman s\u0131n\u0131rl\u0131 s\u00F6zle\u015Fme i\u015Flem kayd\u0131 mevcut de\u011Fil
TRADE_DURATION_NOT_EXISTS=Sipari\u015F s\u00FCresi yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
TRADE_MULTIPLE_NOT_EXISTS=\u00C7oklu sipari\u015F yap\u0131land\u0131rmas\u0131 mevcut de\u011Fil
USER_ACCOUNT_NOT_EMPTY=Hesap bo\u015F olamaz
AGENT_HAS_NOT_ANCESTOR=Ta\u015F\u0131nan acentenin herhangi bir ana acentesi yok
USER_EMAIL_NOT_EXISTS=E-posta mevcut de\u011Fil
Mail_CODE_SEND_FAIL=E-posta do\u011Frulama kodu g\u00F6nderilemedi
GOOGLE_SECRET_BINDING_EXPIRED=Ba\u011Fl\u0131 Google anahtar\u0131n\u0131n s\u00FCresi doldu, l\u00FCtfen tekrar al\u0131n
GOOGLE_CODE_ERROR=Google do\u011Frulama kodu hatal\u0131
ACCOUNT_IS_ERROR=Login ba\u015Far\u0131s\u0131z, hesap mevcut de\u011Fil
GOOGLE_SECRET_IS_NOT_BINDING=Google anahtar\u0131 ba\u011Fl\u0131 de\u011Fil, l\u00FCtfen \u00F6nce ba\u011Flay\u0131n
TRADE_CONTRACT_RECORD_NOT_EXISTS=S\u00F6zle\u015Fme emri mevcut de\u011Fil
TRADE_CONTRACT_CONFIG_ERROR=S\u00F6zle\u015Fme yap\u0131land\u0131rma hatas\u0131
ARG_ORDER_STATUS_IS_EMPTY=Parametre sipari\u015F durumu bo\u015F olamaz
ARG_LEVERAGE_IS_EMPTY=Parametre kald\u0131rac\u0131 bo\u015F olamaz
ARG_ORDER_TYPE_IS_EMPTY=Parametre sipari\u015F tipi bo\u015F olamaz
ARG_VALUE_ERROR=Parametre de\u011Feri hatas\u0131
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=S\u00F6zle\u015Fme emri iptal edildi
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=S\u00F6zle\u015Fme emri tamamland\u0131
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=S\u00F6zle\u015Fme kapat\u0131ld\u0131
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Sipari\u015F d\u00F6viz kuru hatas\u0131
USER_FORBIDDEN_FUNC=Bu \u00F6zellik devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131
IMAGE_URL_ERROR=G\u00F6r\u00FCnt\u00FC ba\u011Flant\u0131 hatas\u0131

