COUNTRY_TH=Tailandia
COUNTRY_JP=Jap\u00F3n
COUNTRY_KR=Corea del Sur
COUNTRY_PH=Filipinas
COUNTRY_VN=Vietnam
COUNTRY_CN=China
COUNTRY_GB=Reino Unido
COUNTRY_FR=Francia
COUNTRY_US=Estados Unidos
COUNTRY_ES=Espa\u00F1a
COUNTRY_DE=Alemania
COUNTRY_RU=Rusia
COUNTRY_CA=Canad\u00E1
COUNTRY_TR=Turqu\u00EDa
COUNTRY_PT=Portugal
COUNTRY_MA=Marruecos
COUNTRY_DZ=Argelia
COUNTRY_IT=Italia
COUNTRY_CO=Colombia
COUNTRY_MX=M\u00E9xico
COUNTRY_CH=Suiza
COUNTRY_BE=B\u00E9lgica
COUNTRY_AR=Argentina
COUNTRY_NO=Noruega
COUNTRY_HK=Hong Kong
BANK=Tarjeta bancaria
CRYPTO=Criptomoneda
USER_CERTIFIED_STATUS_NOT=No certificado
USER_CERTIFIED_STATUS_HANDLING=En certificaci\u00F3n
USER_CERTIFIED_STATUS_SUCCESS=Certificado
USER_CERTIFIED_STATUS_FAIL=Certificaci\u00F3n fallida
ACCOUNT_NOT_EMPTY=La cuenta no puede estar vac\u00EDa
PASSWORD_NOT_EMPTY=La contrase\u00F1a no puede estar vac\u00EDa
PASSWORD_NOT_LENGTH_6_16=La longitud de la contrase\u00F1a es de 6-16 caracteres
PASSWORD_FORMATTER_ERROR=El formato de la contrase\u00F1a son n\u00FAmeros y letras
ACCOUNT_TYPE_ERROR=Error de tipo de cuenta
MAIL_FORMATTER_ERROR=Error de formato de correo electr\u00F3nico
MOBILE_FORMATTER_ERROR=Error de formato de n\u00FAmero de m\u00F3vil
AREA_NOT_EMPTY=El \u00E1rea no puede estar vac\u00EDa
MAIL_SCENE_ERROR=Error de escenario de env\u00EDo de SMS
SMS_SCENE_ERROR=Error de escenario de env\u00EDo de SMS
REAL_NAME_NOT_EMPTY=El nombre real no puede estar vac\u00EDo
CERTIFICATION_TYPE_ERROR=Error de tipo de certificaci\u00F3n de identidad
CERTIFICATION_CODE_NOT_EMPTY=El n\u00FAmero de identificaci\u00F3n no puede estar vac\u00EDo
CERTIFICATION_FRONT_NOT_EMPTY=La foto del frente de la identificaci\u00F3n no puede estar vac\u00EDa
CERTIFICATION_BACK_NOT_EMPTY=La foto del reverso de la identificaci\u00F3n no puede estar vac\u00EDa
TRADE_PAIR_NOT_EMPTY=Por favor seleccione el par de trading
FUNDS_RECORD_OP_TYPE=Error de tipo de fondos
CURRENCY_NOT_EMPTY=Por favor seleccione la moneda
PAY_METHOD_ERROR=Por favor seleccione el m\u00E9todo de pago
RECHARGE_AMOUNT_ERROR=Por favor ingrese el monto de recarga
WITHDRAW_AMOUNT_ERROR=Por favor ingrese completamente el monto de retiro
FUNDS_PASSWORD_ERROR=Error de contrase\u00F1a de fondos
WALLET_NOT_EMPTY=Por favor seleccione la billetera
AUTH_CODE_NOT_EMPTY=Por favor ingrese el c\u00F3digo de verificaci\u00F3n
AVATAR_FORMATTER_ERROR=Por favor seleccione el avatar
WALLET_TYPE_ERROR=Error de tipo de billetera
WALLET_NAME_NOT_EMPTY=Por favor ingrese el nombre de la billetera
WALLET_ACCOUNT_NOT_EMPTY=Por favor ingrese la cuenta de la billetera
WALLET_TYPE_NAME_NOT_EMPTY=Por favor ingrese el nombre del banco
ASSET_Type_ERROR=Error de tipo de activo
KEY_NOT_EMPTY=La palabra clave no puede estar vac\u00EDa
AMOUNT_NOT_EMPTY=Error en la cantidad del monto
TRADE_DIRECT_ERROR=Error de direcci\u00F3n de trading
TRADE_DURATION_ERROR=Error de duraci\u00F3n de trading
TRADE_SEND_TIME_ERROR=Error de tiempo de env\u00EDo de trading
TRADE_PRICE_TIME_ERROR=Error de tiempo de precio de trading
TRADE_PAGE_TYPE_ERROR=Error de clasificaci\u00F3n
SUCCESS=\u00C9xito
WAITHANDLE=Procesando
FAILURE=Fallo
PENDING=Procesando
BAD_REQUEST=Solicitud err\u00F3nea
UNKNOW_AUTHORIZED=Tipo de autorizaci\u00F3n desconocido [{}]
TOKEN_NOT_SUPPORT_MODE=La interfaz Token no soporta el modo de autorizaci\u00F3n [{}]
CLIENT_ERROR=client_id o client_secret pasado incorrectamente
TOKEN_REFRESH_INVALID=Token de actualizaci\u00F3n inv\u00E1lido
TOKEN_REFRESH_CLIENT_ERROR=N\u00FAmero de cliente del token de actualizaci\u00F3n incorrecto
TOKEN_REFRESH_EXPIRE=Token de actualizaci\u00F3n expirado
TOKEN_NOT_EXISTS=Token de acceso no existe
TOKEN_EXPIRE=Token de acceso expirado
GRANT_RESPONSE_TYPE_ERROR=El par\u00E1metro response_type solo permite code y token
UNAUTHORIZED=Cuenta no logueada
FORBIDDEN=No tiene permisos para esta operaci\u00F3n
NOT_FOUND=El recurso solicitado no existe
METHOD_NOT_ALLOWED=M\u00E9todo de solicitud no permitido
LOCKED=Bloqueado
TOO_MANY_REQUESTS=Demasiadas solicitudes, por favor intente m\u00E1s tarde
INTERNAL_SERVER_ERROR=Excepci\u00F3n del sistema
NOT_IMPLEMENTED=Funci\u00F3n no implementada/no habilitada
REPEATED_REQUESTS=Solicitud repetida, por favor intente m\u00E1s tarde
DEMO_DENY=Modo demostraci\u00F3n, operaciones de escritura prohibidas
VALUE_ERROR=Error de valor
GOOGLE_AUTH_NOT_BIND=Google Authenticator no vinculado
GOOGLE_AUTH_CODE_ERROR=Error de c\u00F3digo de Google Authenticator
SCHEDULER_JOB_STOP=[Tarea programada - Deshabilitada][Consulte https://doc.iocoder.cn/job/ para habilitar]
CANDLE_TABLE_NAME_NOT_AVAILABLE=Nombre de tabla K-line inv\u00E1lido
CANDLE_BAR_VALUE_ERROR=Par\u00E1metro bar inv\u00E1lido
CANDLE_PRICE_ERROR=Error al obtener precio
TRADE_PAIR_NOT_EXISTS=Par de trading no existe
TRADE_PAIR_EXISTS=Par de trading ya existe
TRADE_PAIR_TENANT_NOT_EXISTS=El inquilino no tiene este par comercial
TRADE_PAIR_TENANT_EXISTS=Par de trading del inquilino ya existe
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=Configuraci\u00F3n de tipo de activo del par de trading del inquilino no existe
TRADE_TENANT_NEED_DEFAULT=Debe haber un par de trading predeterminado para el inquilino
TRADE_TENANT_DUPLICATE_DEFAULT=Par de trading predeterminado del inquilino ya existe
CONFIG_NOT_EXISTS=Configuraci\u00F3n de par\u00E1metros no existe
CONFIG_KEY_DUPLICATE=Clave de configuraci\u00F3n de par\u00E1metros duplicada
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=No se puede eliminar configuraci\u00F3n de par\u00E1metros de tipo integrado del sistema
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Error al obtener configuraci\u00F3n de par\u00E1metros, raz\u00F3n: no se permite obtener configuraci\u00F3n no visible
JOB_NOT_EXISTS=Tarea programada no existe
JOB_HANDLER_EXISTS=Manejador de tarea programada ya existe
JOB_CHANGE_STATUS_INVALID=Solo se permite cambiar a estado de habilitado o deshabilitado
JOB_CHANGE_STATUS_EQUALS=La tarea programada ya est\u00E1 en este estado, no necesita cambiar
JOB_UPDATE_ONLY_NORMAL_STATUS=Solo las tareas en estado habilitado pueden ser modificadas
JOB_CRON_EXPRESSION_VALID=Expresi\u00F3n CRON incorrecta
JOB_HANDLER_BEAN_NOT_EXISTS=Bean del manejador de tarea programada no existe
JOB_HANDLER_BEAN_TYPE_ERROR=Tipo de Bean del manejador de tarea programada incorrecto, no implementa la interfaz JobHandler
API_ERROR_LOG_NOT_FOUND=Log de error de API no encontrado
API_ERROR_LOG_PROCESSED=Log de error de API procesado
FILE_PATH_EXISTS=Ruta de archivo ya existe
FILE_NOT_EXISTS=Archivo no existe
FILE_IS_EMPTY=Archivo vac\u00EDo
CODEGEN_TABLE_EXISTS=Definici\u00F3n de tabla ya existe
CODEGEN_IMPORT_TABLE_NULL=Tabla importada no existe
CODEGEN_IMPORT_COLUMNS_NULL=Columnas importadas no existen
CODEGEN_TABLE_NOT_EXISTS=Definici\u00F3n de tabla no existe
CODEGEN_COLUMN_NOT_EXISTS=Definici\u00F3n de columna no existe
CODEGEN_SYNC_COLUMNS_NULL=Columnas sincronizadas no existen
CODEGEN_SYNC_NONE_CHANGE=Sincronizaci\u00F3n fallida, no hay cambios
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Comentario de tabla de base de datos no completado
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Comentario de columna de tabla de base de datos ({}) no completado
CODEGEN_MASTER_TABLE_NOT_EXISTS=Definici\u00F3n de tabla maestra (id={}) no existe, por favor verifique
CODEGEN_SUB_COLUMN_NOT_EXISTS=Columna de tabla subsidiaria (id={}) no existe, por favor verifique
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=Generaci\u00F3n de c\u00F3digo de tabla maestra fallida, raz\u00F3n: no tiene tabla subsidiaria
FILE_CONFIG_NOT_EXISTS=Configuraci\u00F3n de archivo no existe
FILE_CONFIG_DELETE_FAIL_MASTER=Esta configuraci\u00F3n de archivo no se permite eliminar, raz\u00F3n: es la configuraci\u00F3n maestra, eliminar causar\u00E1 incapacidad de subir archivos
DATA_SOURCE_CONFIG_NOT_EXISTS=Configuraci\u00F3n de fuente de datos no existe
DATA_SOURCE_CONFIG_NOT_OK=Configuraci\u00F3n de fuente de datos incorrecta, no se puede conectar
DEMO01_CONTACT_NOT_EXISTS=Contacto de ejemplo no existe
DEMO02_CATEGORY_NOT_EXISTS=Categor\u00EDa de ejemplo no existe
DEMO02_CATEGORY_EXITS_CHILDREN=Existen categor\u00EDas de ejemplo subsidiarias, no se puede eliminar
DEMO02_CATEGORY_PARENT_NOT_EXITS=Categor\u00EDa de ejemplo padre no existe
DEMO02_CATEGORY_PARENT_ERROR=No se puede establecer a s\u00ED mismo como categor\u00EDa padre de ejemplo
DEMO02_CATEGORY_NAME_DUPLICATE=Ya existe una categor\u00EDa de ejemplo con ese nombre
DEMO02_CATEGORY_PARENT_IS_CHILD=No se puede establecer la categor\u00EDa de ejemplo subsidiaria como categor\u00EDa padre
DEMO03_STUDENT_NOT_EXISTS=Estudiante no existe
DEMO03_GRADE_NOT_EXISTS=Grado de estudiante no existe
DEMO03_GRADE_EXISTS=Grado de estudiante ya existe
AREA_NOT_EXISTS=Esta \u00E1rea no existe
USER_NOT_EXISTS=Usuario no existe
USER_MOBILE_NOT_EXISTS=N\u00FAmero de m\u00F3vil no registrado para usuario
USER_MOBILE_USED=Este n\u00FAmero de m\u00F3vil ya est\u00E1 siendo usado
USER_ACCOUNT_USED=Cuenta ya est\u00E1 siendo usada
USER_EMAIL_USED=Correo electr\u00F3nico ya est\u00E1 siendo usado
USER_POINT_NOT_ENOUGH=Saldo de puntos de usuario insuficiente
USER_OLD_PASSWORD_NOT_MATCH=Contrase\u00F1a antigua incorrecta
USER_BALANCE_ERROR=Error de saldo de usuario
USER_CONFIG_NOT_SUPPORTED=Elemento de configuraci\u00F3n de usuario temporalmente no soportado
AUTH_LOGIN_BAD_CREDENTIALS=Fallo de login, cuenta y contrase\u00F1a incorrectas
AUTH_LOGIN_USER_DISABLED=Fallo de login, cuenta deshabilitada
AUTH_ACCOUNT_FORMAT_ERROR=Error de formato de cuenta de autenticaci\u00F3n
TAG_NOT_EXISTS=Etiqueta no existe
TAG_NAME_EXISTS=Etiqueta ya existe
TAG_HAS_USER=Existen usuarios bajo la etiqueta de usuario, no se puede eliminar
POINT_RECORD_BIZ_NOT_SUPPORT=Tipo de negocio de registro de puntos de usuario no soportado
SIGN_IN_CONFIG_NOT_EXISTS=Regla de check-in no existe
SIGN_IN_CONFIG_EXISTS=Regla de d\u00EDas de check-in ya existe
SIGN_IN_RECORD_TODAY_EXISTS=Ya se hizo check-in hoy, por favor no repita
LEVEL_NOT_EXISTS=Nivel de usuario no existe
LEVEL_NAME_EXISTS=Nombre de nivel de usuario ya est\u00E1 siendo usado
LEVEL_VALUE_EXISTS=Valor de nivel de usuario ya est\u00E1 siendo usado
LEVEL_EXPERIENCE_MIN=Experiencia de upgrade debe ser mayor que la experiencia de upgrade establecida del nivel anterior
LEVEL_EXPERIENCE_MAX=Experiencia de upgrade debe ser menor que la experiencia de upgrade establecida del siguiente nivel
LEVEL_HAS_USER=Existen usuarios bajo el nivel de usuario, no se puede eliminar
EXPERIENCE_BIZ_NOT_SUPPORT=Tipo de negocio de experiencia de usuario no soportado
GROUP_NOT_EXISTS=Grupo de usuario no existe
GROUP_HAS_USER=Grupo de usuario tiene usuarios, no se puede eliminar
USER_WITHDRAW_NOT_EXISTS=Retiro de miembro no existe
USER_BALANCE_NOT_ENOUGH=Saldo insuficiente
USER_WITHDRAW_HAS_HANDLE=Retiro ya procesado
USER_WITHDRAW_LESS_MIN_AMOUNT=Retiro debe ser mayor que [{}]
USER_WITHDRAW_LESS_MAX_AMOUNT=Retiro debe ser menor que [{}]
USER_WITHDRAW_LESS_MAX_PROCESS=Tiene una orden en proceso de retiro
USER_FROZEN_BALANCE_NOT_ENOUGH=Saldo insuficiente
USER_ASSETS_SPOT_NOT_EXISTS=Activos de usuario no existen
USER_FAVORITE_TRADE_PAIR_EXISTS=Par de trading favorito ya existe
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=Par de trading favorito no existe
USER_RECHARGE_NOT_EXISTS=Registro de recarga no existe
USER_RECHARGE_HAS_HANDLE=Registro de recarga ya procesado
USER_WALLET_NOT_EXISTS=Billetera de miembro no existe
USER_SPOT_ORDER_NOT_EXISTS=Orden spot de miembro no existe
USER_TRANSACTIONS_NOT_EXISTS=Cuenta de miembro no existe
USER_MARGIN_ORDER_NOT_EXISTS=Orden de contrato de miembro no existe
USER_MARGIN_CONFIG_NOT_EXISTS=Configuraci\u00F3n de contrato de miembro no existe
USER_CERTIFICATION_NOT_EXISTS=Informaci\u00F3n de certificaci\u00F3n de miembro no existe
USER_CERTIFICATION_BEEN_HANDLE=Informaci\u00F3n de certificaci\u00F3n de miembro ya procesada
USER_CERTIFICATION_STATUS_SUCCESS=Informaci\u00F3n de certificaci\u00F3n de miembro ya certificada
USER_CERTIFICATION_STATUS_HANDLING=Informaci\u00F3n de certificaci\u00F3n de miembro en revisi\u00F3n
USER_CERTIFICATION_NOT_VERIFY=Identidad [{}], operaci\u00F3n no permitida
USER_CERTIFICATION_VERIFYING=Certificaci\u00F3n de identidad en proceso, operaci\u00F3n no permitida
USER_CERTIFICATION_VERIFY_FAILURE=Certificaci\u00F3n de identidad fallida, operaci\u00F3n no permitida
LEVEL_CONFIG_NOT_EXISTS=Configuraci\u00F3n de nivel de miembro no existe
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=Configuraci\u00F3n de nivel predeterminado de miembro no se puede eliminar
LEVEL_CONFIG_NAME_EXISTS=Nombre de nivel ya existe
USER_FUND_PASSWORD_NOT_EXISTS=No ha establecido contrase\u00F1a de fondos, no se puede modificar
USER_FUND_PASSWORD_ERROR=Error de contrase\u00F1a de fondos
FUNDS_RECORD_NOT_EXISTS=Registro de fondos no existe
USER_RECHARGE_LESS_MAX_PROCESS=Tiene [{}] \u00F3rdenes en proceso de retiro
AUTH_LOGIN_CAPTCHA_CODE_ERROR=C\u00F3digo de verificaci\u00F3n incorrecto, raz\u00F3n: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token ha expirado
AUTH_MOBILE_NOT_EXISTS=N\u00FAmero de m\u00F3vil no existe
MENU_NAME_DUPLICATE=Ya existe un men\u00FA con ese nombre
MENU_PARENT_NOT_EXISTS=Men\u00FA padre no existe
MENU_PARENT_ERROR=No se puede establecer a s\u00ED mismo como men\u00FA padre
MENU_NOT_EXISTS=Men\u00FA no existe
MENU_EXISTS_CHILDREN=Existen submen\u00FAs, no se puede eliminar
MENU_PARENT_NOT_DIR_OR_MENU=El tipo de men\u00FA padre debe ser directorio o men\u00FA
ROLE_NOT_EXISTS=Rol no existe
ROLE_NAME_DUPLICATE=Ya existe un rol con nombre [{}]
ROLE_CODE_DUPLICATE=Ya existe un rol con c\u00F3digo [{}]
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=No se puede operar rol de tipo integrado del sistema
ROLE_IS_DISABLE=Rol con nombre [{}] est\u00E1 deshabilitado
ROLE_ADMIN_CODE_ERROR=C\u00F3digo [{}] no se puede usar
USER_USERNAME_EXISTS=Cuenta ya existe
USER_MOBILE_EXISTS=N\u00FAmero de m\u00F3vil ya existe
USER_EMAIL_EXISTS=Correo electr\u00F3nico ya existe
USER_IMPORT_LIST_IS_EMPTY=Datos de importaci\u00F3n de usuario no pueden estar vac\u00EDos
USER_PASSWORD_FAILED=Validaci\u00F3n de contrase\u00F1a de usuario fallida
USER_IS_DISABLE=Usuario con nombre [{}] est\u00E1 deshabilitado
USER_COUNT_MAX=Creaci\u00F3n de usuario fallida, raz\u00F3n: excede la cuota m\u00E1xima de inquilino [{}]
DEPT_NAME_DUPLICATE=Ya existe un departamento con ese nombre
DEPT_PARENT_NOT_EXITS=Departamento padre no existe
DEPT_NOT_FOUND=Departamento actual no existe
DEPT_EXITS_CHILDREN=Existen departamentos subsidiarios, no se puede eliminar
DEPT_PARENT_ERROR=No se puede establecer a s\u00ED mismo como departamento padre
DEPT_EXISTS_USER=Existen empleados en el departamento, no se puede eliminar
DEPT_NOT_ENABLE=Departamento [{}] no est\u00E1 en estado habilitado, no se permite seleccionar
DEPT_PARENT_IS_CHILD=No se puede establecer el departamento subsidiario como departamento padre
POST_NOT_FOUND=Puesto actual no existe
POST_NOT_ENABLE=Puesto [{}] no est\u00E1 en estado habilitado, no se permite seleccionar
POST_NAME_DUPLICATE=Ya existe un puesto con ese nombre
POST_CODE_DUPLICATE=Ya existe un puesto con ese identificador
DICT_TYPE_NOT_EXISTS=Tipo de diccionario actual no existe
DICT_TYPE_NOT_ENABLE=Tipo de diccionario no est\u00E1 en estado habilitado, no se permite seleccionar
DICT_TYPE_NAME_DUPLICATE=Ya existe un tipo de diccionario con ese nombre
DICT_TYPE_TYPE_DUPLICATE=Ya existe un tipo de diccionario de ese tipo
DICT_TYPE_HAS_CHILDREN=No se puede eliminar, el tipo de diccionario tiene datos de diccionario
DICT_DATA_NOT_EXISTS=Datos de diccionario actuales no existen
DICT_DATA_NOT_ENABLE=Datos de diccionario [{}] no est\u00E1n en estado habilitado, no se permite seleccionar
DICT_DATA_VALUE_DUPLICATE=Ya existen datos de diccionario con ese valor
NOTICE_NOT_FOUND=Notificaci\u00F3n actual no existe
SMS_CHANNEL_NOT_EXISTS=Canal SMS no existe
SMS_CHANNEL_DISABLE=Canal SMS no est\u00E1 en estado habilitado, no se permite seleccionar
SMS_CHANNEL_HAS_CHILDREN=No se puede eliminar, el canal SMS tiene plantillas SMS
SMS_TEMPLATE_NOT_EXISTS=Plantilla SMS no existe
SMS_TEMPLATE_CODE_DUPLICATE=Ya existe una plantilla SMS con c\u00F3digo [{}]
SMS_TEMPLATE_API_ERROR=Llamada a API de plantilla SMS fallida, raz\u00F3n: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=Plantilla de API SMS no se puede usar, raz\u00F3n: en auditor\u00EDa
SMS_TEMPLATE_API_AUDIT_FAIL=Plantilla de API SMS no se puede usar, raz\u00F3n: auditor\u00EDa no aprobada, {}
SMS_TEMPLATE_API_NOT_FOUND=Plantilla de API SMS no se puede usar, raz\u00F3n: plantilla no existe
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Par\u00E1metro de plantilla [{}] faltante
SMS_CODE_NOT_FOUND=C\u00F3digo de verificaci\u00F3n no encontrado
SMS_CODE_EXPIRED=C\u00F3digo de verificaci\u00F3n expirado
SMS_CODE_USED=C\u00F3digo de verificaci\u00F3n ya usado
SMS_CODE_NOT_CORRECT=C\u00F3digo de verificaci\u00F3n incorrecto
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Excede la cantidad m\u00E1xima de SMS enviados por d\u00EDa
SMS_CODE_SEND_TOO_FAST=Env\u00EDo de SMS demasiado frecuente
TENANT_NOT_EXISTS=Inquilino no existe
TENANT_DISABLE=Inquilino con nombre [{}] est\u00E1 deshabilitado
TENANT_EXPIRE=Inquilino con nombre [{}] ha expirado
TENANT_CAN_NOT_UPDATE_SYSTEM=\u00A1Inquilino del sistema no puede ser modificado o eliminado!
TENANT_CODE_DUPLICATE=Ya existe un inquilino con c\u00F3digo [{}]
TENANT_WEBSITE_DUPLICATE=Ya existe un inquilino con dominio [{}]
TENANT_PACKAGE_NOT_EXISTS=Paquete de inquilino no existe
TENANT_PACKAGE_USED=El inquilino est\u00E1 usando este paquete, por favor reasigne un paquete al inquilino antes de intentar eliminar
TENANT_PACKAGE_DISABLE=Paquete de inquilino con nombre [{}] est\u00E1 deshabilitado
OAUTH2_CLIENT_NOT_EXISTS=Cliente OAuth2 no existe
OAUTH2_CLIENT_EXISTS=N\u00FAmero de cliente OAuth2 ya existe
OAUTH2_CLIENT_DISABLE=Cliente OAuth2 deshabilitado
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=Este tipo de autorizaci\u00F3n no es soportado
OAUTH2_CLIENT_SCOPE_OVER=Alcance de autorizaci\u00F3n demasiado amplio
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=redirect_uri inv\u00E1lido: [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=client_secret inv\u00E1lido: {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id no coincide
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=redirect_uri no coincide
OAUTH2_GRANT_STATE_MISMATCH=state no coincide
OAUTH2_GRANT_CODE_NOT_EXISTS=code no existe
OAUTH2_CODE_NOT_EXISTS=code no existe
OAUTH2_CODE_EXPIRE=C\u00F3digo de verificaci\u00F3n expirado
MAIL_ACCOUNT_NOT_EXISTS=Cuenta de correo no existe
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=No se puede eliminar, la cuenta de correo tiene plantillas de correo
MAIL_TEMPLATE_NOT_EXISTS=Plantilla de correo no existe
MAIL_TEMPLATE_CODE_EXISTS=C\u00F3digo de plantilla de correo [{}] ya existe
MAIL_SEND_TEMPLATE_PARAM_MISS=Par\u00E1metro de plantilla [{}] faltante
MAIL_SEND_MAIL_NOT_EXISTS=Correo electr\u00F3nico no existe
MAIL_CODE_SEND_TOO_FAST=Env\u00EDo de correo demasiado frecuente
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Excede la cantidad m\u00E1xima de correos enviados por d\u00EDa
MAIL_CODE_NOT_FOUND=C\u00F3digo de verificaci\u00F3n no encontrado
MAIL_CODE_EXPIRED=C\u00F3digo de verificaci\u00F3n expirado
MAIL_CODE_USED=C\u00F3digo de verificaci\u00F3n ya usado
MAIL_CODE_NOT_CORRECT=C\u00F3digo de verificaci\u00F3n incorrecto
MAIL_IS_EXISTS=Direcci\u00F3n de correo electr\u00F3nico ya est\u00E1 siendo usada
NOTIFY_TEMPLATE_NOT_EXISTS=Plantilla de mensaje interno no existe
NOTIFY_TEMPLATE_CODE_DUPLICATE=Ya existe una plantilla de mensaje interno con c\u00F3digo [{}]
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Par\u00E1metro de plantilla [{}] faltante
AGENT_NOT_EXISTS=Agente no existe
AGENT_INVITE_CODE_EXISTS=C\u00F3digo de invitaci\u00F3n ya existe
AGENT_HAS_DESCENDANT=El agente tiene agentes subordinados, no se puede eliminar
AGENT_ANCESTOR_NOT_AVAILABLE=Agente padre no disponible
AUTH_NOT_EXISTS=Certificaci\u00F3n de miembro no existe
CURRENCY_NOT_EXISTS=Moneda no existe
CURRENCYNOTRATE=Esta moneda temporalmente no tiene tipo de cambio
BANNER_NOT_EXISTS=Banner no existe
TENANT_SERVER_NAME_NOT_EXISTS=Dominio de inquilino del sistema no existe
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Datos de diccionario de inquilino no existen
TIME_CONTRACT_AMOUNT_LESS=Monto de compra menor que el monto m\u00EDnimo
TIME_CONTRACT_RECORD_NOT_EXISTS=Registro de trading de contrato limitado por tiempo no existe
TRADE_DURATION_NOT_EXISTS=Configuraci\u00F3n de duraci\u00F3n de orden no existe
TRADE_MULTIPLE_NOT_EXISTS=Configuraci\u00F3n de m\u00FAltiplo de orden no existe
USER_ACCOUNT_NOT_EMPTY=La cuenta no puede estar vac\u00EDa
AGENT_HAS_NOT_ANCESTOR=El agente movido no tiene ning\u00FAn agente padre
USER_EMAIL_NOT_EXISTS=Correo electr\u00F3nico no existe
Mail_CODE_SEND_FAIL=Env\u00EDo de c\u00F3digo de verificaci\u00F3n por correo fallido
GOOGLE_SECRET_BINDING_EXPIRED=Clave de Google vinculada ha expirado, por favor obt\u00E9ngala nuevamente
GOOGLE_CODE_ERROR=C\u00F3digo de Google Authenticator incorrecto
ACCOUNT_IS_ERROR=Fallo de login, cuenta no existe
GOOGLE_SECRET_IS_NOT_BINDING=Clave de Google no vinculada, por favor vincule primero
TRADE_CONTRACT_RECORD_NOT_EXISTS=Registro de orden de contrato no existe
TRADE_CONTRACT_CONFIG_ERROR=Error de configuraci\u00F3n de contrato
ARG_ORDER_STATUS_IS_EMPTY=Estado de orden no puede estar vac\u00EDo
ARG_LEVERAGE_IS_EMPTY=Par\u00E1metro de apalancamiento no puede estar vac\u00EDo
ARG_ORDER_TYPE_IS_EMPTY=Par\u00E1metro de tipo de orden no puede estar vac\u00EDo
ARG_VALUE_ERROR=Error de valor de par\u00E1metro
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=Orden de contrato ya cancelada
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=Orden de contrato ya ejecutada
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=Contrato ya cerrado
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Error de tipo de cambio de moneda de orden
USER_FORBIDDEN_FUNC=Esta funci\u00F3n est\u00E1 deshabilitada
IMAGE_URL_ERROR=Error de direcci\u00F3n de enlace de imagen
USER_WITHDRAW_HAS_SUCCESS=Prohibido modificar orden ya exitosa 