COUNTRY_TH=\uD0DC\uAD6D
COUNTRY_JP=\uC77C\uBCF8
COUNTRY_KR=\uB300\uD55C\uBBFC\uAD6D
COUNTRY_PH=\uD544\uB9AC\uD540
COUNTRY_VN=\uBCA0\uD2B8\uB0A8
COUNTRY_CN=\uC911\uAD6D
COUNTRY_GB=\uC601\uAD6D
COUNTRY_FR=\uD504\uB791\uC2A4
COUNTRY_US=\uBBF8\uAD6D
COUNTRY_ES=\uC2A4\uD398\uC778
COUNTRY_DE=\uB3C5\uC77C
COUNTRY_RU=\uB7EC\uC2DC\uC544 \uC81C\uAD6D
COUNTRY_CA=\uCE90\uB098\uB2E4
COUNTRY_TR=\uD130\uD0A4\uC5B4
COUNTRY_PT=\uD3EC\uB974\uD22C\uAC08
COUNTRY_MA=\uBAA8\uB85C\uCF54
COUNTRY_DZ=\uC54C\uC81C\uB9AC
COUNTRY_IT=\uC774\uD0C8\uB9AC\uC544
COUNTRY_CO=\uCF5C\uB86C\uBE44\uC544
COUNTRY_MX=\uBA55\uC2DC\uCF54
COUNTRY_CH=\uC2A4\uC704\uC2A4
COUNTRY_BE=\uBCA8\uAE30\uC5D0
COUNTRY_AR=\uC544\uB974\uD5E8\uD2F0\uB098
COUNTRY_NO=\uB178\uB974\uC6E8\uC774
COUNTRY_HK=\uD64D\uCF69
BANK=\uC740\uD589 \uCE74\uB4DC
CRYPTO=\uC554\uD638\uD654\uD3D0
USER_CERTIFIED_STATUS_NOT=\uC778\uC99D\uB418\uC9C0 \uC54A\uC74C
USER_CERTIFIED_STATUS_HANDLING=\uC778\uC99D \uC9C4\uD589 \uC911
USER_CERTIFIED_STATUS_SUCCESS=\uAC80\uC99D\uB428
USER_CERTIFIED_STATUS_FAIL=\uC778\uC99D \uC2E4\uD328
ACCOUNT_NOT_EMPTY=\uACC4\uC815\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
PASSWORD_NOT_EMPTY=\uBE44\uBC00\uBC88\uD638\uB294 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
PASSWORD_NOT_LENGTH_6_16=\uBE44\uBC00\uBC88\uD638 \uAE38\uC774\uB294 6~16\uC790\uC785\uB2C8\uB2E4.
PASSWORD_FORMATTER_ERROR=\uBE44\uBC00\uBC88\uD638 \uD615\uC2DD\uC740 \uC22B\uC790\uC640 \uBB38\uC790\uC785\uB2C8\uB2E4.
ACCOUNT_TYPE_ERROR=\uC798\uBABB\uB41C \uACC4\uC815 \uC720\uD615
MAIL_FORMATTER_ERROR=\uC774\uBA54\uC77C \uD615\uC2DD \uC624\uB958
MOBILE_FORMATTER_ERROR=\uC798\uBABB\uB41C \uC804\uD654\uBC88\uD638
AREA_NOT_EMPTY=\uC9C0\uC5ED\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
MAIL_SCENE_ERROR=SMS \uC804\uC1A1 \uC2DC\uB098\uB9AC\uC624 \uC624\uB958
SMS_SCENE_ERROR=SMS \uC804\uC1A1 \uC2DC\uB098\uB9AC\uC624 \uC624\uB958
REAL_NAME_NOT_EMPTY=\uC2E4\uBA85\uC744 \uACF5\uBC31\uC73C\uB85C \uB450\uC9C0 \uB9C8\uC2ED\uC2DC\uC624.
CERTIFICATION_TYPE_ERROR=\uC798\uBABB\uB41C \uC778\uC99D \uC720\uD615
CERTIFICATION_CODE_NOT_EMPTY=ID \uBC88\uD638\uB294 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
CERTIFICATION_FRONT_NOT_EMPTY=\uC2E0\uBD84\uC99D\uC758 \uC55E\uBA74 \uC0AC\uC9C4\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
CERTIFICATION_BACK_NOT_EMPTY=\uC2E0\uBD84\uC99D \uB4B7\uBA74 \uC0AC\uC9C4\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
TRADE_PAIR_NOT_EMPTY=\uAC70\uB798\uC30D\uC744 \uC120\uD0DD\uD574\uC8FC\uC138\uC694
FUNDS_RECORD_OP_TYPE=\uC798\uBABB\uB41C \uD380\uB4DC \uC720\uD615
CURRENCY_NOT_EMPTY=\uD1B5\uD654\uB97C \uC120\uD0DD\uD558\uC138\uC694
PAY_METHOD_ERROR=\uACB0\uC81C\uBC29\uC2DD\uC744 \uC120\uD0DD\uD574\uC8FC\uC138\uC694
RECHARGE_AMOUNT_ERROR=\uCDA9\uC804\uAE08\uC561\uC744 \uC785\uB825\uD574\uC8FC\uC138\uC694
WITHDRAW_AMOUNT_ERROR=\uCD9C\uAE08\uAE08\uC561\uC744 \uBAA8\uB450 \uC785\uB825\uD558\uC138\uC694.
FUNDS_PASSWORD_ERROR=\uC798\uBABB\uB41C \uD380\uB4DC \uBE44\uBC00\uBC88\uD638
WALLET_NOT_EMPTY=\uC9C0\uAC11\uC744 \uC120\uD0DD\uD574\uC8FC\uC138\uC694
AUTH_CODE_NOT_EMPTY=\uC778\uC99D\uBC88\uD638\uB97C \uC785\uB825\uD574\uC8FC\uC138\uC694
AVATAR_FORMATTER_ERROR=\uC544\uBC14\uD0C0\uB97C \uC120\uD0DD\uD558\uC138\uC694
WALLET_TYPE_ERROR=\uC798\uBABB\uB41C \uC9C0\uAC11 \uC720\uD615
WALLET_NAME_NOT_EMPTY=\uC9C0\uAC11 \uC774\uB984\uC744 \uC785\uB825\uD574\uC8FC\uC138\uC694
WALLET_ACCOUNT_NOT_EMPTY=\uC9C0\uAC11 \uACC4\uC88C\uB97C \uC785\uB825\uD574\uC8FC\uC138\uC694
WALLET_TYPE_NAME_NOT_EMPTY=\uC740\uD589\uBA85\uC744 \uC785\uB825\uD574\uC8FC\uC138\uC694
ASSET_Type_ERROR=\uC798\uBABB\uB41C \uC790\uC0B0 \uC720\uD615
KEY_NOT_EMPTY=\uD0A4\uC6CC\uB4DC\uB294 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
AMOUNT_NOT_EMPTY=\uAE08\uC561\uC774 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
TRADE_DIRECT_ERROR=\uC798\uBABB\uB41C \uAC70\uB798 \uBC29\uD5A5
TRADE_DURATION_ERROR=\uAC70\uB798 \uC2DC\uAC04 \uC81C\uD55C \uC624\uB958
TRADE_SEND_TIME_ERROR=\uC798\uBABB\uB41C \uC2DC\uAC04\uC5D0 \uAC70\uB798\uAC00 \uC804\uC1A1\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
TRADE_PRICE_TIME_ERROR=\uAC70\uB798\uAC00\uACA9 \uC2DC\uAC04 \uC624\uB958
TRADE_PAGE_TYPE_ERROR=\uBD84\uB958 \uC624\uB958
SUCCESS=\uC131\uACF5
WAITHANDLE=\uCC98\uB9AC
FAILURE=\uC2E4\uD328\uD558\uB2E4
PENDING=\uCC98\uB9AC
BAD_REQUEST=\uC798\uBABB\uB41C \uC694\uCCAD
UNKNOW_AUTHORIZED=\uC54C \uC218 \uC5C6\uB294 \uC778\uC99D \uC720\uD615[{}]
TOKEN_NOT_SUPPORT_MODE=\uD1A0\uD070 \uC778\uD130\uD398\uC774\uC2A4\uB294 [{}] \uC778\uC99D \uBAA8\uB4DC\uB97C \uC9C0\uC6D0\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
CLIENT_ERROR=client_id \uB610\uB294 client_secret\uC774 \uC62C\uBC14\uB974\uAC8C \uC804\uB2EC\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
TOKEN_REFRESH_INVALID=\uC798\uBABB\uB41C \uC0C8\uB85C \uACE0\uCE68 \uD1A0\uD070
TOKEN_REFRESH_CLIENT_ERROR=\uC0C8\uB85C \uACE0\uCE68 \uD1A0\uD070\uC758 \uD074\uB77C\uC774\uC5B8\uD2B8 \uBC88\uD638\uAC00 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
TOKEN_REFRESH_EXPIRE=\uC0C8\uB85C \uACE0\uCE68 \uD1A0\uD070\uC774 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
TOKEN_NOT_EXISTS=\uC561\uC138\uC2A4 \uD1A0\uD070\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TOKEN_EXPIRE=\uC561\uC138\uC2A4 \uD1A0\uD070\uC774 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
GRANT_RESPONSE_TYPE_ERROR=response_type \uB9E4\uAC1C\uBCC0\uC218 \uAC12\uC740 \uCF54\uB4DC\uC640 \uD1A0\uD070\uB9CC \uD5C8\uC6A9\uD569\uB2C8\uB2E4.
UNAUTHORIZED=\uB85C\uADF8\uC778\uB418\uC9C0 \uC54A\uC740 \uACC4\uC815
FORBIDDEN=\uC774 \uC791\uC5C5\uC5D0 \uB300\uD55C \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.
NOT_FOUND=\uC694\uCCAD\uD55C \uB9AC\uC18C\uC2A4\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
METHOD_NOT_ALLOWED=\uC694\uCCAD \uBC29\uBC95\uC774 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
LOCKED=\uC7A0\uAE40
TOO_MANY_REQUESTS=\uC694\uCCAD\uC774 \uB108\uBB34 \uC790\uC8FC \uBC1C\uC0DD\uD569\uB2C8\uB2E4. \uB098\uC911\uC5D0 \uB2E4\uC2DC \uC2DC\uB3C4\uD574 \uC8FC\uC138\uC694.
INTERNAL_SERVER_ERROR=\uC2DC\uC2A4\uD15C \uC608\uC678
NOT_IMPLEMENTED=\uAE30\uB2A5\uC774 \uAD6C\uD604\uB418\uC9C0 \uC54A\uC74C/\uD65C\uC131\uD654\uB418\uC9C0 \uC54A\uC74C
REPEATED_REQUESTS=\uC694\uCCAD\uC744 \uBC18\uBCF5\uD558\uC138\uC694. \uB098\uC911\uC5D0 \uB2E4\uC2DC \uC2DC\uB3C4\uD574 \uC8FC\uC138\uC694.
DEMO_DENY=\uB370\uBAA8 \uBAA8\uB4DC, \uC4F0\uAE30 \uC791\uC5C5\uC774 \uBE44\uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
VALUE_ERROR=\uC798\uBABB\uB41C \uAC12
GOOGLE_AUTH_NOT_BIND=Google OTP\uAC00 \uBC14\uC778\uB529\uB418\uC9C0 \uC54A\uC74C
GOOGLE_AUTH_CODE_ERROR=Google \uC778\uC99D \uCF54\uB4DC \uC624\uB958
SCHEDULER_JOB_STOP=[\uC608\uC57D \uC791\uC5C5 - \uBE44\uD65C\uC131\uD654\uB428][\uD65C\uC131\uD654\uD558\uB824\uBA74 https://doc.iocoder.cn/job/ \uCC38\uC870]
CANDLE_TABLE_NAME_NOT_AVAILABLE=k-\uB77C\uC778 \uD14C\uC774\uBE14 \uC774\uB984\uC774 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
CANDLE_BAR_VALUE_ERROR=bar \uB9E4\uAC1C\uBCC0\uC218\uAC00 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
CANDLE_PRICE_ERROR=\uAC00\uACA9 \uC624\uB958 \uBC1B\uAE30
TRADE_PAIR_NOT_EXISTS=\uAC70\uB798 \uC30D\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
TRADE_PAIR_EXISTS=\uAC70\uB798 \uC30D\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
TRADE_PAIR_TENANT_NOT_EXISTS=\uD14C\uB10C\uD2B8\uC5D0\uAC8C \uAC70\uB798 \uC30D\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.
TRADE_PAIR_TENANT_EXISTS=\uD14C\uB10C\uD2B8 \uAC70\uB798 \uC30D\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=\uD14C\uB10C\uD2B8 \uAC70\uB798 \uC30D \uC790\uC0B0 \uC720\uD615 \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TRADE_TENANT_NEED_DEFAULT=\uAE30\uBCF8 \uD14C\uB10C\uD2B8 \uAC70\uB798 \uC30D\uC774 \uC788\uC5B4\uC57C \uD569\uB2C8\uB2E4.
TRADE_TENANT_DUPLICATE_DEFAULT=\uD14C\uB10C\uD2B8\uC758 \uAE30\uBCF8 \uAC70\uB798 \uC30D\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
CONFIG_NOT_EXISTS=\uB9E4\uAC1C\uBCC0\uC218 \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
CONFIG_KEY_DUPLICATE=\uB9E4\uAC1C\uBCC0\uC218 \uAD6C\uC131 \uD0A4\uAC00 \uBC18\uBCF5\uB429\uB2C8\uB2E4.
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=\uC720\uD615\uC774 \uC2DC\uC2A4\uD15C \uB0B4\uC7A5\uC778 \uB9E4\uAC1C\uBCC0\uC218 \uAD6C\uC131\uC740 \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=\uB9E4\uAC1C\uBCC0\uC218 \uAD6C\uC131\uC744 \uAC00\uC838\uC624\uC9C0 \uBABB\uD588\uC2B5\uB2C8\uB2E4. \uC774\uC720: \uBCF4\uC774\uC9C0 \uC54A\uB294 \uAD6C\uC131\uC744 \uAC00\uC838\uC624\uB294 \uAC83\uC740 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
JOB_NOT_EXISTS=\uC608\uC57D\uB41C \uC791\uC5C5\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
JOB_HANDLER_EXISTS=\uC608\uC57D\uB41C \uC791\uC5C5\uC758 \uD504\uB85C\uC138\uC11C\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
JOB_CHANGE_STATUS_INVALID=\uCF1C\uC9D0 \uB610\uB294 \uAEBC\uC9D0\uC73C\uB85C\uB9CC \uC218\uC815 \uAC00\uB2A5
JOB_CHANGE_STATUS_EQUALS=\uC608\uC57D\uB41C \uC791\uC5C5\uC740 \uC774\uBBF8 \uC774 \uC0C1\uD0DC\uC774\uBBC0\uB85C \uC218\uC815\uD560 \uD544\uC694\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.
JOB_UPDATE_ONLY_NORMAL_STATUS=\uC5F4\uB824 \uC788\uB294 \uC791\uC5C5\uB9CC \uC218\uC815\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
JOB_CRON_EXPRESSION_VALID=CRON \uD45C\uD604\uC2DD\uC774 \uC62C\uBC14\uB974\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
JOB_HANDLER_BEAN_NOT_EXISTS=\uC608\uC57D\uB41C \uC791\uC5C5\uC758 \uD504\uB85C\uC138\uC11C \uBE48\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
JOB_HANDLER_BEAN_TYPE_ERROR=\uC608\uC57D\uB41C \uC791\uC5C5\uC758 \uD504\uB85C\uC138\uC11C Bean \uC720\uD615\uC774 \uC62C\uBC14\uB974\uC9C0 \uC54A\uC73C\uBA70 JobHandler \uC778\uD130\uD398\uC774\uC2A4\uAC00 \uAD6C\uD604\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
API_ERROR_LOG_NOT_FOUND=API \uC624\uB958 \uB85C\uADF8\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
API_ERROR_LOG_PROCESSED=API \uC624\uB958 \uB85C\uADF8\uAC00 \uCC98\uB9AC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
FILE_PATH_EXISTS=\uD30C\uC77C \uACBD\uB85C\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
FILE_NOT_EXISTS=\uD30C\uC77C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4
FILE_IS_EMPTY=\uD30C\uC77C\uC774 \uBE44\uC5B4 \uC788\uC2B5\uB2C8\uB2E4.
CODEGEN_TABLE_EXISTS=\uD14C\uC774\uBE14 \uC815\uC758\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
CODEGEN_IMPORT_TABLE_NULL=\uAC00\uC838\uC628 \uD14C\uC774\uBE14\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
CODEGEN_IMPORT_COLUMNS_NULL=\uAC00\uC838\uC628 \uD544\uB4DC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
CODEGEN_TABLE_NOT_EXISTS=\uD14C\uC774\uBE14 \uC815\uC758\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
CODEGEN_COLUMN_NOT_EXISTS=\uD544\uB4DC \uC815\uC758\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
CODEGEN_SYNC_COLUMNS_NULL=\uB3D9\uAE30\uD654\uB41C \uD544\uB4DC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
CODEGEN_SYNC_NONE_CHANGE=\uB3D9\uAE30\uD654\uC5D0 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4. \uBCC0\uACBD\uC0AC\uD56D\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=\uB370\uC774\uD130\uBCA0\uC774\uC2A4 \uD14C\uC774\uBE14 \uC124\uBA85\uC774 \uCC44\uC6CC\uC9C0\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=\uB370\uC774\uD130\uBCA0\uC774\uC2A4 \uD14C\uC774\uBE14 \uD544\uB4DC({}) \uC8FC\uC11D\uC774 \uCC44\uC6CC\uC9C0\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
CODEGEN_MASTER_TABLE_NOT_EXISTS=\uAE30\uBCF8 \uD14C\uC774\uBE14(id={}) \uC815\uC758\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. \uD655\uC778\uD558\uC138\uC694.
CODEGEN_SUB_COLUMN_NOT_EXISTS=\uD558\uC704 \uD14C\uC774\uBE14\uC758 \uD544\uB4DC(id={})\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. \uD655\uC778\uD558\uC138\uC694.
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=\uAE30\uBCF8 \uD14C\uC774\uBE14 \uC0DD\uC131 \uCF54\uB4DC\uAC00 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4. \uC774\uC720: \uD558\uC704 \uD14C\uC774\uBE14\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.
FILE_CONFIG_NOT_EXISTS=\uD30C\uC77C \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
FILE_CONFIG_DELETE_FAIL_MASTER=\uC774 \uD30C\uC77C \uAD6C\uC131\uC740 \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774\uC720: \uAE30\uBCF8 \uAD6C\uC131\uC774\uBBC0\uB85C \uC0AD\uC81C\uD558\uBA74 \uD30C\uC77C\uC744 \uC5C5\uB85C\uB4DC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DATA_SOURCE_CONFIG_NOT_EXISTS=\uB370\uC774\uD130 \uC18C\uC2A4 \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DATA_SOURCE_CONFIG_NOT_OK=\uB370\uC774\uD130 \uC18C\uC2A4\uAC00 \uC798\uBABB \uAD6C\uC131\uB418\uC5B4 \uC5F0\uACB0\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEMO01_CONTACT_NOT_EXISTS=\uC608\uC2DC \uC5F0\uB77D\uCC98\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DEMO02_CATEGORY_NOT_EXISTS=\uC608\uC2DC \uCE74\uD14C\uACE0\uB9AC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DEMO02_CATEGORY_EXITS_CHILDREN=\uD558\uC704 \uC608\uC2DC \uCE74\uD14C\uACE0\uB9AC\uAC00 \uC788\uC5B4 \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEMO02_CATEGORY_PARENT_NOT_EXITS=\uC0C1\uC704 \uC608\uC2DC \uCE74\uD14C\uACE0\uB9AC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DEMO02_CATEGORY_PARENT_ERROR=\uC790\uC2E0\uC744 \uC0C1\uC704 \uC608\uC2DC \uCE74\uD14C\uACE0\uB9AC\uB85C \uC124\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEMO02_CATEGORY_NAME_DUPLICATE=\uC774 \uC774\uB984\uC744 \uAC00\uC9C4 \uC608\uC2DC \uCE74\uD14C\uACE0\uB9AC\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
DEMO02_CATEGORY_PARENT_IS_CHILD=\uC790\uC2E0\uC758 \uD558\uC704 \uC608\uC2DC \uCE74\uD14C\uACE0\uB9AC\uB97C \uC0C1\uC704 \uC608\uC2DC \uCE74\uD14C\uACE0\uB9AC\uB85C \uC124\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEMO03_STUDENT_NOT_EXISTS=\uD559\uC0DD\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
DEMO03_GRADE_NOT_EXISTS=\uD559\uC0DD \uC218\uC5C5\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DEMO03_GRADE_EXISTS=\uD559\uC0DD \uC218\uC5C5\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
AREA_NOT_EXISTS=\uC774 \uC9C0\uC5ED\uC740 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_NOT_EXISTS=\uC0AC\uC6A9\uC790\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
USER_MOBILE_NOT_EXISTS=\uD734\uB300\uD3F0 \uBC88\uD638\uAC00 \uB4F1\uB85D\uB418\uC9C0 \uC54A\uC740 \uC0AC\uC6A9\uC790
USER_MOBILE_USED=\uC774 \uD734\uB300\uD3F0 \uBC88\uD638\uB294 \uC774\uBBF8 \uC0AC\uC6A9 \uC911\uC785\uB2C8\uB2E4.
USER_ACCOUNT_USED=\uACC4\uC815\uC774 \uC0AC\uC6A9\uB418\uC5C8\uC2B5\uB2C8\uB2E4
USER_EMAIL_USED=\uC774\uBA54\uC77C\uC774 \uC774\uBBF8 \uC0AC\uC6A9 \uC911\uC785\uB2C8\uB2E4.
USER_POINT_NOT_ENOUGH=\uC0AC\uC6A9\uC790 \uD3EC\uC778\uD2B8 \uC794\uC561\uC774 \uBD80\uC871\uD568
USER_OLD_PASSWORD_NOT_MATCH=\uC798\uBABB\uB41C \uC774\uC804 \uBE44\uBC00\uBC88\uD638
USER_BALANCE_ERROR=\uC0AC\uC6A9\uC790 \uC794\uC561 \uC624\uB958
USER_CONFIG_NOT_SUPPORTED=\uD604\uC7AC \uC9C0\uC6D0\uB418\uC9C0 \uC54A\uB294 \uC0AC\uC6A9\uC790 \uAD6C\uC131 \uD56D\uBAA9
AUTH_LOGIN_BAD_CREDENTIALS=\uB85C\uADF8\uC778 \uC2E4\uD328, \uACC4\uC815 \uBE44\uBC00\uBC88\uD638\uAC00 \uC62C\uBC14\uB974\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
AUTH_LOGIN_USER_DISABLED=\uB85C\uADF8\uC778 \uC2E4\uD328, \uACC4\uC815 \uBE44\uD65C\uC131\uD654
AUTH_ACCOUNT_FORMAT_ERROR=\uC778\uC99D \uACC4\uC815 \uD615\uC2DD \uC624\uB958
TAG_NOT_EXISTS=\uD0DC\uADF8\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
TAG_NAME_EXISTS=\uD0DC\uADF8\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
TAG_HAS_USER=\uC0AC\uC6A9\uC790 \uB77C\uBCA8 \uC544\uB798\uC5D0 \uC0AC\uC6A9\uC790\uAC00 \uC788\uC73C\uBBC0\uB85C \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
POINT_RECORD_BIZ_NOT_SUPPORT=\uC0AC\uC6A9\uC790 \uD3EC\uC778\uD2B8 \uAE30\uB85D \uBE44\uC988\uB2C8\uC2A4 \uC720\uD615\uC740 \uC9C0\uC6D0\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
SIGN_IN_CONFIG_NOT_EXISTS=\uB85C\uADF8\uC778 \uADDC\uCE59\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
SIGN_IN_CONFIG_EXISTS=\uCCB4\uD06C\uC778 \uB0A0\uC9DC \uADDC\uCE59\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
SIGN_IN_RECORD_TODAY_EXISTS=\uC624\uB298 \uB85C\uADF8\uC778\uD558\uC168\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uB85C\uADF8\uC778\uD558\uC9C0 \uB9C8\uC138\uC694.
LEVEL_NOT_EXISTS=\uC0AC\uC6A9\uC790 \uC218\uC900\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
LEVEL_NAME_EXISTS=\uC0AC\uC6A9\uC790 \uC218\uC900 \uC774\uB984\uC774 \uC774\uBBF8 \uC0AC\uC6A9 \uC911\uC785\uB2C8\uB2E4.
LEVEL_VALUE_EXISTS=\uC0AC\uC6A9\uC790 \uB808\uBCA8 \uAC12\uC774 \uC0AC\uC6A9\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
LEVEL_EXPERIENCE_MIN=\uC5C5\uADF8\uB808\uC774\uB4DC \uACBD\uD5D8\uCE58\uB294 \uC774\uC804 \uB808\uBCA8\uC5D0 \uC124\uC815\uB41C \uC5C5\uADF8\uB808\uC774\uB4DC \uACBD\uD5D8\uCE58\uBCF4\uB2E4 \uB192\uC544\uC57C \uD569\uB2C8\uB2E4.
LEVEL_EXPERIENCE_MAX=\uC5C5\uADF8\uB808\uC774\uB4DC \uACBD\uD5D8\uCE58\uB294 \uB2E4\uC74C \uB808\uBCA8\uC5D0 \uC124\uC815\uB41C \uC5C5\uADF8\uB808\uC774\uB4DC \uACBD\uD5D8\uCE58\uBCF4\uB2E4 \uB0AE\uC544\uC57C \uD569\uB2C8\uB2E4.
LEVEL_HAS_USER=\uC0AC\uC6A9\uC790\uB294 \uC0AC\uC6A9\uC790 \uC218\uC900\uC5D0 \uC874\uC7AC\uD558\uBA70 \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
EXPERIENCE_BIZ_NOT_SUPPORT=\uC0AC\uC6A9\uC790 \uACBD\uD5D8 \uBE44\uC988\uB2C8\uC2A4 \uC720\uD615\uC740 \uC9C0\uC6D0\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
GROUP_NOT_EXISTS=\uC0AC\uC6A9\uC790 \uADF8\uB8F9\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
GROUP_HAS_USER=\uC0AC\uC6A9\uC790 \uADF8\uB8F9\uC5D0 \uC0AC\uC6A9\uC790\uAC00 \uC874\uC7AC\uD558\uBBC0\uB85C \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
USER_WITHDRAW_NOT_EXISTS=\uD68C\uC6D0 \uD0C8\uD1F4\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
USER_BALANCE_NOT_ENOUGH=\uC794\uC561 \uBD88\uCDA9\uBD84
USER_WITHDRAW_HAS_HANDLE=\uCD9C\uAE08\uC774 \uCC98\uB9AC\uB418\uC5C8\uC2B5\uB2C8\uB2E4
USER_WITHDRAW_LESS_MIN_AMOUNT=\uC778\uCD9C \uAE08\uC561\uC740 [{}]\uBCF4\uB2E4 \uCEE4\uC57C \uD569\uB2C8\uB2E4.
USER_WITHDRAW_LESS_MAX_AMOUNT=\uC778\uCD9C \uAE08\uC561\uC740 [{}]\uBCF4\uB2E4 \uC791\uC544\uC57C \uD569\uB2C8\uB2E4.
USER_WITHDRAW_LESS_MAX_PROCESS=\uCCA0\uD68C \uC911\uC778 \uC8FC\uBB38\uC774 \uC788\uC2B5\uB2C8\uB2E4.
USER_FROZEN_BALANCE_NOT_ENOUGH=\uC794\uC561 \uBD88\uCDA9\uBD84
USER_ASSETS_SPOT_NOT_EXISTS=\uC0AC\uC6A9\uC790 \uC790\uC0B0\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_FAVORITE_TRADE_PAIR_EXISTS=\uC990\uACA8\uCC3E\uB294 \uAC70\uB798 \uC30D\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=\uC120\uD638\uD558\uB294 \uAC70\uB798 \uC30D\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
USER_RECHARGE_NOT_EXISTS=\uC800\uC7A5 \uAC00\uCE58 \uAE30\uB85D\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
USER_RECHARGE_HAS_HANDLE=\uC800\uC7A5 \uAC00\uCE58 \uAE30\uB85D\uC774 \uCC98\uB9AC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
USER_WALLET_NOT_EXISTS=\uD68C\uC6D0 \uC9C0\uAC11\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_SPOT_ORDER_NOT_EXISTS=\uD68C\uC6D0 \uC989\uC11D \uC8FC\uBB38\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_TRANSACTIONS_NOT_EXISTS=\uD68C\uC6D0 \uCCAD\uAD6C\uC11C\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
USER_MARGIN_ORDER_NOT_EXISTS=\uD68C\uC6D0\uACC4\uC57D\uC8FC\uBB38\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_MARGIN_CONFIG_NOT_EXISTS=\uD68C\uC6D0 \uACC4\uC57D \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_CERTIFICATION_NOT_EXISTS=\uD68C\uC6D0\uC778\uC99D\uC815\uBCF4\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_CERTIFICATION_BEEN_HANDLE=\uD68C\uC6D0\uC778\uC99D\uC815\uBCF4\uAC00 \uCC98\uB9AC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
USER_CERTIFICATION_STATUS_SUCCESS=\uD68C\uC6D0\uC778\uC99D\uC815\uBCF4\uAC00 \uC778\uC99D\uB418\uC5C8\uC2B5\uB2C8\uB2E4
USER_CERTIFICATION_STATUS_HANDLING=\uD68C\uC6D0 \uC778\uC99D \uC815\uBCF4\uB97C \uAC80\uD1A0 \uC911\uC785\uB2C8\uB2E4.
USER_CERTIFICATION_NOT_VERIFY=ID [{}], \uC791\uC5C5\uC774 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_CERTIFICATION_VERIFYING=\uBCF8\uC778\uC778\uC99D \uC911\uC5D0\uB294 \uC870\uC791\uC774 \uBD88\uAC00\uB2A5\uD569\uB2C8\uB2E4.
USER_CERTIFICATION_VERIFY_FAILURE=\uC2E0\uC6D0 \uC778\uC99D \uC2E4\uD328, \uC791\uC5C5\uC774 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC74C
LEVEL_CONFIG_NOT_EXISTS=\uD68C\uC6D0 \uC218\uC900 \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=\uBA64\uBC84\uC2ED \uAE30\uBCF8 \uC218\uC900 \uAD6C\uC131\uC740 \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
LEVEL_CONFIG_NAME_EXISTS=\uB808\uBCA8 \uC774\uB984\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
USER_FUND_PASSWORD_NOT_EXISTS=\uD380\uB4DC \uBE44\uBC00\uBC88\uD638\uB97C \uC124\uC815\uD558\uC9C0 \uC54A\uC558\uC73C\uBA70 \uC218\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
USER_FUND_PASSWORD_ERROR=\uC798\uBABB\uB41C \uD380\uB4DC \uBE44\uBC00\uBC88\uD638
FUNDS_RECORD_NOT_EXISTS=\uD380\uB4DC \uAE30\uB85D\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
USER_RECHARGE_LESS_MAX_PROCESS=\uCCA0\uD68C\uB418\uB294 [{}]\uAC1C\uC758 \uC8FC\uBB38\uC774 \uC788\uC2B5\uB2C8\uB2E4.
AUTH_LOGIN_CAPTCHA_CODE_ERROR=\uC778\uC99D \uCF54\uB4DC\uAC00 \uC62C\uBC14\uB974\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. \uC774\uC720: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=\uD1A0\uD070\uC774 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4
AUTH_MOBILE_NOT_EXISTS=\uD734\uB300\uD3F0\uBC88\uD638\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
MENU_NAME_DUPLICATE=\uAC19\uC740 \uC774\uB984\uC758 \uBA54\uB274\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
MENU_PARENT_NOT_EXISTS=\uC0C1\uC704 \uBA54\uB274\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
MENU_PARENT_ERROR=\uC790\uC2E0\uC744 \uC0C1\uC704 \uBA54\uB274\uB85C \uC124\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
MENU_NOT_EXISTS=\uBA54\uB274\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
MENU_EXISTS_CHILDREN=\uD558\uC704 \uBA54\uB274\uAC00 \uC874\uC7AC\uD558\uBBC0\uB85C \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
MENU_PARENT_NOT_DIR_OR_MENU=\uC0C1\uC704 \uBA54\uB274 \uC720\uD615\uC740 \uCE74\uD0C8\uB85C\uADF8 \uB610\uB294 \uBA54\uB274\uC5EC\uC57C \uD569\uB2C8\uB2E4.
ROLE_NOT_EXISTS=\uC5ED\uD560\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
ROLE_NAME_DUPLICATE=\uC774\uB984\uC774 [{}]\uC778 \uBB38\uC790\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
ROLE_CODE_DUPLICATE=\uCF54\uB4DC [{}]\uAC00 \uC788\uB294 \uC5ED\uD560\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=\uC2DC\uC2A4\uD15C\uC5D0 \uB0B4\uC7A5\uB41C \uC5ED\uD560\uC744 \uC6B4\uC601\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
ROLE_IS_DISABLE=[{}]\uB77C\uB294 \uBB38\uC790\uAC00 \uBE44\uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
ROLE_ADMIN_CODE_ERROR=[{}] \uC778\uCF54\uB529\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
USER_USERNAME_EXISTS=\uACC4\uC815\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4
USER_MOBILE_EXISTS=\uD734\uB300\uD3F0 \uBC88\uD638\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
USER_EMAIL_EXISTS=\uC774\uBA54\uC77C\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4
USER_IMPORT_LIST_IS_EMPTY=\uAC00\uC838\uC628 \uC0AC\uC6A9\uC790 \uC815\uBCF4\uB294 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
USER_PASSWORD_FAILED=\uC0AC\uC6A9\uC790 \uBE44\uBC00\uBC88\uD638 \uD655\uC778\uC5D0 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
USER_IS_DISABLE=[{}]\uC774\uB77C\uB294 \uC0AC\uC6A9\uC790\uAC00 \uBE44\uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
USER_COUNT_MAX=\uC0AC\uC6A9\uC790\uB97C \uC0DD\uC131\uD558\uC9C0 \uBABB\uD588\uC2B5\uB2C8\uB2E4. \uC774\uC720: \uD14C\uB10C\uD2B8 [{}]\uC758 \uCD5C\uB300 \uD14C\uB10C\uD2B8 \uD560\uB2F9\uB7C9\uC744 \uCD08\uACFC\uD588\uC2B5\uB2C8\uB2E4.
DEPT_NAME_DUPLICATE=\uAC19\uC740 \uC774\uB984\uC758 \uBD80\uC11C\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
DEPT_PARENT_NOT_EXITS=\uC0C1\uC704 \uBD80\uC11C\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DEPT_NOT_FOUND=\uD604\uC7AC \uD574\uB2F9 \uBD80\uC11C\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DEPT_EXITS_CHILDREN=\uD558\uC704 \uBD80\uC11C\uAC00 \uC874\uC7AC\uD558\uBBC0\uB85C \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEPT_PARENT_ERROR=\uC790\uC2E0\uC744 \uC0C1\uC704 \uBD80\uC11C\uB85C \uC124\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEPT_EXISTS_USER=\uBD80\uC11C\uC5D0 \uC9C1\uC6D0\uC774 \uC788\uC73C\uBBC0\uB85C \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEPT_NOT_ENABLE=\uBD80\uC11C [{}]\uC774(\uAC00) \uC5F4\uB824 \uC788\uC9C0 \uC54A\uC73C\uBA70 \uC120\uD0DD\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
DEPT_PARENT_IS_CHILD=\uC790\uC2E0\uC758 \uD558\uC704 \uBD80\uC11C\uB97C \uC0C1\uC704 \uBD80\uC11C\uB85C \uC124\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
POST_NOT_FOUND=\uD604\uC7AC \uC704\uCE58\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
POST_NOT_ENABLE=\uC704\uCE58 [{}]\uC774(\uAC00) \uC5F4\uB824 \uC788\uC9C0 \uC54A\uC73C\uBA70 \uC120\uD0DD\uC774 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
POST_NAME_DUPLICATE=\uAC19\uC740 \uC774\uB984\uC758 \uC9C1\uC704\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
POST_CODE_DUPLICATE=\uC774 \uB85C\uACE0\uAC00 \uC788\uB294 \uC704\uCE58\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
DICT_TYPE_NOT_EXISTS=\uD604\uC7AC \uC0AC\uC804 \uC720\uD615\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DICT_TYPE_NOT_ENABLE=\uC0AC\uC804 \uC720\uD615\uC774 \uC5F4\uB824 \uC788\uC9C0 \uC54A\uC73C\uBA70 \uC120\uD0DD\uC774 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DICT_TYPE_NAME_DUPLICATE=\uC774 \uC774\uB984\uC744 \uAC00\uC9C4 \uC0AC\uC804 \uC720\uD615\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
DICT_TYPE_TYPE_DUPLICATE=\uC774 \uC720\uD615\uC758 \uC0AC\uC804 \uC720\uD615\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
DICT_TYPE_HAS_CHILDREN=\uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774 \uC0AC\uC804 \uC720\uD615\uC5D0\uB294 \uC0AC\uC804 \uB370\uC774\uD130\uB3C4 \uC788\uC2B5\uB2C8\uB2E4.
DICT_DATA_NOT_EXISTS=\uD604\uC7AC \uC0AC\uC804 \uB370\uC774\uD130\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DICT_DATA_NOT_ENABLE=\uC0AC\uC804 \uB370\uC774\uD130 [{}]\uAC00 \uC5F4\uB824 \uC788\uC9C0 \uC54A\uC73C\uBA70 \uC120\uD0DD\uC774 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
DICT_DATA_VALUE_DUPLICATE=\uC774 \uAC12\uC5D0 \uB300\uD55C \uC0AC\uC804 \uB370\uC774\uD130\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
NOTICE_NOT_FOUND=\uC54C\uB9BC \uACF5\uC9C0\uAC00 \uD604\uC7AC \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
SMS_CHANNEL_NOT_EXISTS=SMS \uCC44\uB110\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
SMS_CHANNEL_DISABLE=SMS \uCC44\uB110\uC774 \uC5F4\uB824 \uC788\uC9C0 \uC54A\uC544 \uC120\uD0DD\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
SMS_CHANNEL_HAS_CHILDREN=\uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774 SMS \uCC44\uB110\uC5D0\uB3C4 SMS \uD15C\uD50C\uB9BF\uC774 \uC788\uC2B5\uB2C8\uB2E4.
SMS_TEMPLATE_NOT_EXISTS=SMS \uD15C\uD50C\uB9BF\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
SMS_TEMPLATE_CODE_DUPLICATE=\uCF54\uB4DC [{}]\uAC00 \uD3EC\uD568\uB41C \uBB38\uC790 \uBA54\uC2DC\uC9C0 \uD15C\uD50C\uB9BF\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
SMS_TEMPLATE_API_ERROR=\uB2E4\uC74C \uC6D0\uC778\uC73C\uB85C \uC778\uD574 SMS API \uD15C\uD50C\uB9BF \uD638\uCD9C\uC774 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=SMS API \uD15C\uD50C\uB9BF\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774\uC720: \uAC80\uD1A0 \uC911
SMS_TEMPLATE_API_AUDIT_FAIL=SMS API \uD15C\uD50C\uB9BF\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774\uC720: \uC2B9\uC778 \uC2E4\uD328, {}
SMS_TEMPLATE_API_NOT_FOUND=SMS API \uD15C\uD50C\uB9BF\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774\uC720: \uD15C\uD50C\uB9BF\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=\uD15C\uD50C\uB9BF \uB9E4\uAC1C\uBCC0\uC218 [{}]\uAC00 \uB204\uB77D\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
SMS_CODE_NOT_FOUND=\uC778\uC99D\uCF54\uB4DC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
SMS_CODE_EXPIRED=\uC778\uC99D \uCF54\uB4DC\uAC00 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4
SMS_CODE_USED=\uC778\uC99D\uCF54\uB4DC\uAC00 \uC0AC\uC6A9\uB418\uC5C8\uC2B5\uB2C8\uB2E4
SMS_CODE_NOT_CORRECT=\uC798\uBABB\uB41C \uC778\uC99D \uCF54\uB4DC
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=\uC77C\uC77C \uBB38\uC790 \uBA54\uC2DC\uC9C0 \uBC1C\uC1A1 \uAC74\uC218 \uCD08\uACFC
SMS_CODE_SEND_TOO_FAST=\uBB38\uC790 \uBA54\uC2DC\uC9C0\uB97C \uB108\uBB34 \uC790\uC8FC \uBCF4\uB0B8\uB2E4
TENANT_NOT_EXISTS=\uD14C\uB10C\uD2B8\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TENANT_DISABLE=[{}]\uC774\uB77C\uB294 \uD14C\uB10C\uD2B8\uAC00 \uBE44\uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
TENANT_EXPIRE=[{}]\uC774\uB77C\uB294 \uD14C\uB10C\uD2B8\uAC00 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
TENANT_CAN_NOT_UPDATE_SYSTEM=\uC2DC\uC2A4\uD15C \uD14C\uB10C\uD2B8\uB294 \uC218\uC815, \uC0AD\uC81C \uB4F1\uC758 \uC791\uC5C5\uC744 \uC218\uD589\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4!
TENANT_CODE_DUPLICATE=\uD14C\uB10C\uD2B8 \uCF54\uB4DC\uAC00 [{}]\uC778 \uD14C\uB10C\uD2B8\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
TENANT_WEBSITE_DUPLICATE=\uB3C4\uBA54\uC778 \uC774\uB984\uC774 [{}]\uC778 \uD14C\uB10C\uD2B8\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
TENANT_PACKAGE_NOT_EXISTS=\uD14C\uB10C\uD2B8 \uD328\uD0A4\uC9C0\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TENANT_PACKAGE_USED=\uD14C\uB10C\uD2B8\uAC00 \uC774 \uD328\uD0A4\uC9C0\uB97C \uC0AC\uC6A9\uD558\uACE0 \uC788\uC2B5\uB2C8\uB2E4. \uD14C\uB10C\uD2B8\uC6A9 \uD328\uD0A4\uC9C0\uB97C \uC7AC\uC124\uC815\uD55C \uD6C4 \uC0AD\uC81C\uD574 \uBCF4\uC138\uC694.
TENANT_PACKAGE_DISABLE=[{}]\uC774\uB77C\uB294 \uD14C\uB10C\uD2B8 \uD328\uD0A4\uC9C0\uAC00 \uBE44\uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
OAUTH2_CLIENT_NOT_EXISTS=OAuth2 \uD074\uB77C\uC774\uC5B8\uD2B8\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
OAUTH2_CLIENT_EXISTS=OAuth2 \uD074\uB77C\uC774\uC5B8\uD2B8 \uBC88\uD638\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
OAUTH2_CLIENT_DISABLE=OAuth2 \uD074\uB77C\uC774\uC5B8\uD2B8\uAC00 \uBE44\uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=\uC774 \uC2B9\uC778 \uC720\uD615\uC740 \uC9C0\uC6D0\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
OAUTH2_CLIENT_SCOPE_OVER=\uC2B9\uC778 \uBC94\uC704\uAC00 \uB108\uBB34 \uD07D\uB2C8\uB2E4.
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=\uC798\uBABB\uB41C \uB9AC\uB514\uB809\uC158_uri:[{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=\uC798\uBABB\uB41C client_secret:{}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id\uAC00 \uC77C\uCE58\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=\uB9AC\uB514\uB809\uC158_uri\uAC00 \uC77C\uCE58\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
OAUTH2_GRANT_STATE_MISMATCH=\uC0C1\uD0DC\uAC00 \uC77C\uCE58\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
OAUTH2_GRANT_CODE_NOT_EXISTS=\uCF54\uB4DC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
OAUTH2_CODE_NOT_EXISTS=\uCF54\uB4DC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
OAUTH2_CODE_EXPIRE=\uC778\uC99D \uCF54\uB4DC\uAC00 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4
MAIL_ACCOUNT_NOT_EXISTS=\uC774\uBA54\uC77C \uACC4\uC815\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=\uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774 \uC774\uBA54\uC77C \uACC4\uC815\uC5D0\uB294 \uC774\uBA54\uC77C \uD15C\uD50C\uB9BF\uB3C4 \uC788\uC2B5\uB2C8\uB2E4.
MAIL_TEMPLATE_NOT_EXISTS=\uC774\uBA54\uC77C \uD15C\uD50C\uB9BF\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
MAIL_TEMPLATE_CODE_EXISTS=\uC774\uBA54\uC77C \uD15C\uD50C\uB9BF \uCF54\uB4DC[{}]\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
MAIL_SEND_TEMPLATE_PARAM_MISS=\uD15C\uD50C\uB9BF \uB9E4\uAC1C\uBCC0\uC218 [{}]\uAC00 \uB204\uB77D\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
MAIL_SEND_MAIL_NOT_EXISTS=\uC774\uBA54\uC77C\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
MAIL_CODE_SEND_TOO_FAST=\uC0AC\uC11C\uD568\uC774 \uB108\uBB34 \uC790\uC8FC \uC804\uC1A1\uB429\uB2C8\uB2E4.
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=\uD558\uB8E8\uC5D0 \uBCF4\uB0B8 \uC774\uBA54\uC77C \uC218\uB97C \uCD08\uACFC\uD588\uC2B5\uB2C8\uB2E4.
MAIL_CODE_NOT_FOUND=\uC778\uC99D\uCF54\uB4DC\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
MAIL_CODE_EXPIRED=\uC778\uC99D \uCF54\uB4DC\uAC00 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4
MAIL_CODE_USED=\uC778\uC99D\uCF54\uB4DC\uAC00 \uC0AC\uC6A9\uB418\uC5C8\uC2B5\uB2C8\uB2E4
MAIL_CODE_NOT_CORRECT=\uC798\uBABB\uB41C \uC778\uC99D \uCF54\uB4DC
MAIL_IS_EXISTS=\uC774\uBA54\uC77C \uBC88\uD638\uAC00 \uC774\uBBF8 \uC0AC\uC6A9 \uC911\uC785\uB2C8\uB2E4.
NOTIFY_TEMPLATE_NOT_EXISTS=\uC0AC\uC774\uD2B8 \uD3B8\uC9C0 \uD15C\uD50C\uB9BF\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
NOTIFY_TEMPLATE_CODE_DUPLICATE=[{}]\uB85C \uCF54\uB529\uB41C \uC0AC\uC774\uD2B8 \uBA54\uC2DC\uC9C0 \uD15C\uD50C\uB9BF\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
NOTIFY_SEND_TEMPLATE_PARAM_MISS=\uD15C\uD50C\uB9BF \uB9E4\uAC1C\uBCC0\uC218 [{}]\uAC00 \uB204\uB77D\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
AGENT_NOT_EXISTS=\uC5D0\uC774\uC804\uD2B8\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
AGENT_INVITE_CODE_EXISTS=\uCD08\uB300 \uCF54\uB4DC\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
AGENT_HAS_DESCENDANT=\uC5D0\uC774\uC804\uD2B8 \uC544\uB798\uC5D0 \uD558\uC704 \uC5D0\uC774\uC804\uD2B8\uAC00 \uC788\uC5B4 \uC0AD\uC81C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4
AGENT_ANCESTOR_NOT_AVAILABLE=\uC0C1\uC704 \uC5D0\uC774\uC804\uD2B8\uB97C \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
AUTH_NOT_EXISTS=\uD68C\uC6D0\uC778\uC99D\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
CURRENCY_NOT_EXISTS=\uD1B5\uD654\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
CURRENCYNOTRATE=\uD604\uC7AC \uC774 \uD1B5\uD654\uC5D0 \uB300\uD55C \uD658\uC728\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.
BANNER_NOT_EXISTS=\uBC30\uB108\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
TENANT_SERVER_NAME_NOT_EXISTS=\uC2DC\uC2A4\uD15C \uD14C\uB10C\uD2B8 \uB3C4\uBA54\uC778 \uC774\uB984\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=\uD14C\uB10C\uD2B8 \uC0AC\uC804 \uC815\uBCF4\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TIME_CONTRACT_AMOUNT_LESS=\uAD6C\uB9E4 \uAE08\uC561\uC774 \uCD5C\uC18C \uAE08\uC561\uBCF4\uB2E4 \uC801\uC2B5\uB2C8\uB2E4.
TIME_CONTRACT_RECORD_NOT_EXISTS=\uAE30\uAC04\uD55C\uC815 \uACC4\uC57D \uAC70\uB798 \uAE30\uB85D\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TRADE_DURATION_NOT_EXISTS=\uC8FC\uBB38 \uAE30\uAC04 \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TRADE_MULTIPLE_NOT_EXISTS=\uC8FC\uBB38 \uBC30\uC728 \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
USER_ACCOUNT_NOT_EMPTY=\uACC4\uC815\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
AGENT_HAS_NOT_ANCESTOR=\uC774\uB3D9\uB41C \uC5D0\uC774\uC804\uD2B8\uC5D0\uB294 \uC0C1\uC704 \uC5D0\uC774\uC804\uD2B8\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.
USER_EMAIL_NOT_EXISTS=\uC774\uBA54\uC77C\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
Mail_CODE_SEND_FAIL=\uC774\uBA54\uC77C \uC778\uC99D \uCF54\uB4DC\uB97C \uBCF4\uB0B4\uC9C0 \uBABB\uD588\uC2B5\uB2C8\uB2E4.
GOOGLE_SECRET_BINDING_EXPIRED=\uBC14\uC778\uB529\uB41C Google \uD0A4\uAC00 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uC5BB\uC73C\uC2ED\uC2DC\uC624.
GOOGLE_CODE_ERROR=Google \uC778\uC99D \uCF54\uB4DC\uAC00 \uC62C\uBC14\uB974\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
ACCOUNT_IS_ERROR=\uB85C\uADF8\uC778 \uC2E4\uD328, \uACC4\uC815\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
GOOGLE_SECRET_IS_NOT_BINDING=Google \uD0A4\uAC00 \uBC14\uC778\uB529\uB418\uC5B4 \uC788\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. \uBA3C\uC800 \uD0A4\uB97C \uBC14\uC778\uB529\uD574 \uC8FC\uC138\uC694.
TRADE_CONTRACT_RECORD_NOT_EXISTS=\uACC4\uC57D \uC8FC\uBB38\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
TRADE_CONTRACT_CONFIG_ERROR=\uACC4\uC57D \uAD6C\uC131 \uC624\uB958
ARG_ORDER_STATUS_IS_EMPTY=\uC8FC\uBB38 \uC0C1\uD0DC\uB294 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
ARG_LEVERAGE_IS_EMPTY=\uB9E4\uAC1C\uBCC0\uC218 \uBC30\uC728\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4
ARG_ORDER_TYPE_IS_EMPTY=\uB9E4\uAC1C\uBCC0\uC218 \uC21C\uC11C \uC720\uD615\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
ARG_VALUE_ERROR=\uB9E4\uAC1C\uBCC0\uC218 \uAC12 \uC624\uB958
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=\uACC4\uC57D \uC8FC\uBB38\uC774 \uCDE8\uC18C\uB418\uC5C8\uC2B5\uB2C8\uB2E4
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=\uACC4\uC57D\uC8FC\uBB38\uC774 \uC644\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=\uACC4\uC57D\uC774 \uC885\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=\uC8FC\uBB38 \uD658\uC728 \uC624\uB958
USER_FORBIDDEN_FUNC=\uC774 \uAE30\uB2A5\uC740 \uBE44\uD65C\uC131\uD654\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4
IMAGE_URL_ERROR=\uC774\uBBF8\uC9C0 \uB9C1\uD06C \uC624\uB958