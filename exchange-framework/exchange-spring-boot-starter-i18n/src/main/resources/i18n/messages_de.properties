COUNTRY_TH=Thailand
COUNTRY_JP=Japan
COUNTRY_KR=Korea
COUNTRY_PH=Philippinen
COUNTRY_VN=Vietnam
CN = China
COUNTRY_GB=Vereinigtes K\u00F6nigreich
COUNTRY_FR=Frankreich
COUNTRY_US=Vereinigte Staaten
COUNTRY_ES=Spain
COUNTRY_DE=Germany
COUNTRY_RU=Russia
COUNTRY_CA=Canada
COUNTRY_TR=T\u00FCrkiye
COUNTRY_PT=Portugal
COUNTRY_MA=Marokko
COUNTRY_DZ=Algerien
COUNTRY_IT=Italien
COUNTRY_CO=Kolumbien
COUNTRY_MX=Mexiko
COUNTRY_CH=Schweiz
COUNTRY_BE=Belgien
COUNTRY_AR=Argentinien
COUNTRY_NO=Norwegen
COUNTRY_HK=Hongkong
BANK=Bankkarte
CRYPTO=Kryptow\u00E4hrung
USER_CERTIFIED_STATUS_NOT=Nicht authentifiziert
USER_CERTIFIED_STATUS_HANDLING=Autorisierung
USER_CERTIFIED_STATUS_SUCCESS=Autorisiert
USER_CERTIFIED_STATUS_FAIL=Authentifizierung fehlgeschlagen
ACCOUNT_NOT_EMPTY=Konto darf nicht leer sein
PASSWORD_NOT_EMPTY=Das Passwort darf nicht leer sein
PASSWORD_NOT_LENGTH_6_16=Die Passwortl\u00E4nge betr\u00E4gt 6-16 Zeichen
PASSWORD_FORMATTER_ERROR=Das Passwortformat besteht aus Zahlen und Buchstaben
ACCOUNT_TYPE_ERROR=Kontotypfehler
MAIL_FORMATTER_ERROR=E-Mail-Formatfehler
MOBILE_FORMATTER_ERROR=Mobilnummerformatfehler
AREA_NOT_EMPTY=Region darf nicht leer sein
MAIL_SCENE_ERROR=Fehler beim Senden der SMS
SMS_SCENE_ERROR=Fehler beim Senden von SMS
REAL_NAME_NOT_EMPTY=Der tats\u00E4chliche Name darf nicht leer sein
CERTIFICATION_TYPE_ERROR=Fehler beim Identit\u00E4tsauthentifizierungstyp
CERTIFICATION_CODE_NOT_EMPTY=ID-Nummer darf nicht leer sein
CERTIFICATION_FRONT_NOT_EMPTY=Das Foto auf der Vorderseite des Ausweises darf nicht leer sein
CERTIFICATION_BACK_NOT_EMPTY=Das Foto auf der R\u00FCckseite des Ausweises darf nicht leer sein
TRADE_PAIR_NOT_EMPTY=Bitte w\u00E4hlen Sie ein Handelspaar
FUNDS_RECORD_OP_TYPE=Fehler beim Fondstyp
CURRENCY_NOT_EMPTY=Bitte w\u00E4hlen Sie eine W\u00E4hrung
PAY_METHOD_ERROR=Bitte w\u00E4hlen Sie eine Zahlungsmethode
RECHARGE_AMOUNT_ERROR=Bitte geben Sie den Aufladebetrag ein
WITHDRAW_AMOUNT_ERROR=Geben Sie den Auszahlungsbetrag vollst\u00E4ndig ein
FUNDS_PASSWORD_ERROR=Fehler beim Guthaben-Passwort
WALLET_NOT_EMPTY=Bitte w\u00E4hlen Sie eine Brieftasche
AUTH_CODE_NOT_EMPTY=Bitte geben Sie den Best\u00E4tigungscode ein.
AVATAR_FORMATTER_ERROR=Bitte w\u00E4hlen Sie einen Avatar
WALLET_TYPE_ERROR=Wallet-Typ-Fehler
WALLET_NAME_NOT_EMPTY=Bitte geben Sie den Wallet-Namen ein
WALLET_ACCOUNT_NOT_EMPTY=Bitte geben Sie die Wallet-Kontonummer ein
WALLET_TYPE_NAME_NOT_EMPTY=Bitte geben Sie den Namen der Bank ein
ASSET_Type_ERROR=Asset-Typ-Fehler
KEY_NOT_EMPTY=Schl\u00FCsselwort darf nicht leer sein
AMOUNT_NOT_EMPTY=Betragsfehler
TRADE_DIRECT_ERROR=Fehler in der Transaktionsrichtung
TRADE_DURATION_ERROR=Transaktionszeitfehler
TRADE_SEND_TIME_ERROR=Fehler beim Senden der Transaktionszeit
TRADE_PRICE_TIME_ERROR=Fehler bei der Transaktionspreiszeit
TRADE_PAGE_TYPE_ERROR=Klassifizierungsfehler
SUCCESS=Erfolg
WAITHANDLE=Wird verarbeitet
FAILURE=fehlgeschlagen
PENDING=Ausstehend
BAD_REQUEST=Falsche Anfrage
UNKNOW_AUTHORIZED=Unbekannter Autorisierungstyp [{}]
TOKEN_NOT_SUPPORT_MODE=Token-Schnittstelle unterst\u00FCtzt den Autorisierungsmodus [{}] nicht
CLIENT_ERROR=Client_id oder client_secret wurden nicht korrekt \u00FCbergeben
TOKEN_REFRESH_INVALID=Ung\u00FCltiges Aktualisierungstoken
TOKEN_REFRESH_CLIENT_ERROR=Falsche Clientnummer des Aktualisierungstokens
TOKEN_REFRESH_EXPIRE=Aktualisierungstoken abgelaufen
TOKEN_NOT_EXISTS=Zugriffstoken existiert nicht
TOKEN_EXPIRE=Zugriffstoken abgelaufen
GRANT_RESPONSE_TYPE_ERROR=Der response_type-Parameterwert erlaubt nur Code und Token
UNAUTHORIZED=Konto nicht angemeldet
FORBIDDEN=Keine Berechtigung f\u00FCr diesen Vorgang
NOT_FOUND=Die angeforderte Ressource existiert nicht
METHOD_NOT_ALLOWED=Anforderungsmethode nicht zul\u00E4ssig
LOCKED=Gesperrt
TOO_MANY_REQUESTS=Anfragen sind zu h\u00E4ufig, bitte versuchen Sie es sp\u00E4ter noch einmal
INTERNAL_SERVER_ERROR=Systemst\u00F6rung
NOT_IMPLEMENTED=Funktion nicht implementiert/nicht aktiviert
REPEATED_REQUESTS=Wiederholte Anfragen, bitte versuchen Sie es sp\u00E4ter erneut
DEMO_DENY=Demo-Modus, Schreibvorgang verboten
VALUE_ERROR=Wertfehler
GOOGLE_AUTH_NOT_BIND=Google Authenticator ist nicht gebunden
GOOGLE_AUTH_CODE_ERROR=Fehler beim Google-Best\u00E4tigungscode
SCHEDULER_JOB_STOP=[Geplante Aufgabe \u2013 deaktiviert][Referenz https://doc.iocoder.cn/job/ Aktivieren]
CANDLE_TABLE_NAME_NOT_AVAILABLE=K-Line-Tabellenname ist ung\u00FCltig
CANDLE_BAR_VALUE_ERROR=Bar-Parameter ist ung\u00FCltig
CANDLE_PRICE_ERROR=Preisfehler erhalten
TRADE_PAIR_NOT_EXISTS=Handelspaar existiert nicht
TRADE_PAIR_EXISTS=Handelspaar existiert bereits
TRADE_PAIR_TENANT_NOT_EXISTS=Der Mandant hat dieses Handelspaar nicht
TRADE_PAIR_TENANT_EXISTS=Mieter-Handelspaar existiert bereits
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=Konfiguration des Asset-Typs des Mandanten-Handelspaars existiert nicht
TRADE_TENANT_NEED_DEFAULT=Es muss ein Standard-Mandanten-Handelspaar vorhanden sein
TRADE_TENANT_DUPLICATE_DEFAULT=Das Standard-Handelspaar des Mieters existiert bereits
CONFIG_NOT_EXISTS=Parameterkonfiguration existiert nicht
CONFIG_KEY_DUPLICATE=Parameterkonfigurationsschl\u00FCssel Duplikat
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=Parameterkonfiguration des systemintegrierten Typs kann nicht gel\u00F6scht werden
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Parameterkonfiguration konnte nicht abgerufen werden. Grund: Das Abrufen einer unsichtbaren Konfiguration ist nicht zul\u00E4ssig.
JOB_NOT_EXISTS=Geplante Aufgabe existiert nicht
JOB_HANDLER_EXISTS=Der Prozessor der geplanten Aufgabe existiert bereits
JOB_CHANGE_STATUS_INVALID=Nur \u00C4nderungen am Status \u201E\u00D6ffnen\u201C oder \u201ESchlie\u00DFen\u201C sind zul\u00E4ssig.
JOB_CHANGE_STATUS_EQUALS=Die geplante Aufgabe befindet sich bereits in diesem Status, es sind keine \u00C4nderungen erforderlich
JOB_UPDATE_ONLY_NORMAL_STATUS=Nur Aufgaben im offenen Status k\u00F6nnen ge\u00E4ndert werden.
JOB_CRON_EXPRESSION_VALID=CRON-Ausdruck ist falsch
JOB_HANDLER_BEAN_NOT_EXISTS=Die Prozessor-Bean der geplanten Aufgabe existiert nicht
JOB_HANDLER_BEAN_TYPE_ERROR=Der Prozessor-Bean-Typ der geplanten Aufgabe ist falsch und die JobHandler-Schnittstelle ist nicht implementiert
API_ERROR_LOG_NOT_FOUND=API-Fehlerprotokoll existiert nicht
API_ERROR_LOG_PROCESSED=API-Fehlerprotokoll wurde verarbeitet
FILE_PATH_EXISTS=Dateipfad existiert bereits
FILE_NOT_EXISTS=Datei existiert nicht
FILE_IS_EMPTY=Datei ist leer
CODEGEN_TABLE_EXISTS=Tabellendefinition existiert bereits
CODEGEN_IMPORT_TABLE_NULL=Die importierte Tabelle existiert nicht
CODEGEN_IMPORT_COLUMNS_NULL=Importiertes Feld existiert nicht
CODEGEN_TABLE_NOT_EXISTS=Tabellendefinition existiert nicht
CODEGEN_COLUMN_NOT_EXISTS=Felddefinition existiert nicht
CODEGEN_SYNC_COLUMNS_NULL=Synchronisiertes Feld existiert nicht
CODEGEN_SYNC_NONE_CHANGE=Synchronisierung fehlgeschlagen, keine \u00C4nderung
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Datenbanktabellenkommentar ist nicht ausgef\u00FCllt
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Datenbanktabellenfeld ({}) Kommentar ist nicht ausgef\u00FCllt
CODEGEN_MASTER_TABLE_NOT_EXISTS=Die Definition der Haupttabelle (id={}) existiert nicht, bitte \u00FCberpr\u00FCfen Sie
CODEGEN_SUB_COLUMN_NOT_EXISTS=Untertabellenfeld (id={}) existiert nicht, bitte pr\u00FCfen
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=Die Codegenerierung der Haupttabelle ist fehlgeschlagen. Grund: Es ist keine Untertabelle vorhanden.
FILE_CONFIG_NOT_EXISTS=Dateikonfiguration existiert nicht
FILE_CONFIG_DELETE_FAIL_MASTER=Diese Dateikonfiguration kann nicht gel\u00F6scht werden. Grund: Es handelt sich um die Hauptkonfiguration. Beim L\u00F6schen schl\u00E4gt das Hochladen der Dateien fehl.
DATA_SOURCE_CONFIG_NOT_EXISTS=Datenquellenkonfiguration existiert nicht
DATA_SOURCE_CONFIG_NOT_OK=Die Datenquellenkonfiguration ist falsch, keine Verbindung m\u00F6glich
DEMO01_CONTACT_NOT_EXISTS=Beispielkontakt existiert nicht
DEMO02_CATEGORY_NOT_EXISTS=Beispielkategorie existiert nicht
DEMO02_CATEGORY_EXITS_CHILDREN=Unterstichprobenkategorie existiert, kann nicht gel\u00F6scht werden
DEMO02_CATEGORY_PARENT_NOT_EXITS=\u00DCbergeordnete Beispielkategorie existiert nicht
DEMO02_CATEGORY_PARENT_ERROR=Kann sich nicht selbst als \u00FCbergeordnete Beispielkategorie festlegen
DEMO02_CATEGORY_NAME_DUPLICATE=Beispielkategorie mit demselben Namen existiert bereits
DEMO02_CATEGORY_PARENT_IS_CHILD=Eigene Unterstichprobenkategorie kann nicht als \u00FCbergeordnete Stichprobenkategorie festgelegt werden
DEMO03_STUDENT_NOT_EXISTS=Student existiert nicht
DEMO03_GRADE_NOT_EXISTS=Studentenklasse existiert nicht
DEMO03_GRADE_EXISTS=Studentenklasse existiert bereits
AREA_NOT_EXISTS=Region existiert nicht
USER_NOT_EXISTS=Benutzer existiert nicht
USER_MOBILE_NOT_EXISTS=Mobilnummer ist nicht registriert
USER_MOBILE_USED=Mobilnummer wurde verwendet
USER_ACCOUNT_USED=Konto wurde verwendet
USER_EMAIL_USED=E-Mail wurde verwendet
USER_POINT_NOT_ENOUGH=Benutzerpunkteguthaben reicht nicht aus
USER_OLD_PASSWORD_NOT_MATCH=Fehler \u201EAltes Passwort\u201C
USER_BALANCE_ERROR=Benutzerguthabenfehler
USER_CONFIG_NOT_SUPPORTED=Benutzerkonfigurationselemente werden nicht unterst\u00FCtzt
AUTH_LOGIN_BAD_CREDENTIALS=Anmeldung fehlgeschlagen, Kontokennwort ist falsch
AUTH_LOGIN_USER_DISABLED=Anmeldung fehlgeschlagen, Konto ist deaktiviert
AUTH_ACCOUNT_FORMAT_ERROR=Fehler beim Format des Authentifizierungskontos
TAG_NOT_EXISTS=Tag existiert nicht
TAG_NAME_EXISTS=Tag existiert bereits
TAG_HAS_USER=Benutzer existiert unter Benutzertag, kann nicht gel\u00F6scht werden
POINT_RECORD_BIZ_NOT_SUPPORT=Gesch\u00E4ftstyp des Benutzerpunktedatensatzes wird nicht unterst\u00FCtzt
SIGN_IN_CONFIG_NOT_EXISTS=Anmelderegel existiert nicht
SIGN_IN_CONFIG_EXISTS=Regel f\u00FCr Anmeldetage existiert bereits
SIGN_IN_RECORD_TODAY_EXISTS=Heute angemeldet, bitte nicht erneut anmelden
LEVEL_NOT_EXISTS=Benutzerebene existiert nicht
LEVEL_NAME_EXISTS=Benutzerebenenname wurde verwendet
LEVEL_VALUE_EXISTS=Benutzerlevelwert wurde verwendet
LEVEL_EXPERIENCE_MIN=Die Upgrade-Erfahrung muss gr\u00F6\u00DFer sein als die f\u00FCr das vorherige Level festgelegte Upgrade-Erfahrung.
LEVEL_EXPERIENCE_MAX=Die Upgrade-Erfahrung muss geringer sein als die f\u00FCr das n\u00E4chste Level festgelegte Upgrade-Erfahrung.
LEVEL_HAS_USER=Benutzer existieren unter Benutzerebene und k\u00F6nnen nicht gel\u00F6scht werden.
EXPERIENCE_BIZ_NOT_SUPPORT=Gesch\u00E4ftstyp \u201EUser Experience\u201C wird nicht unterst\u00FCtzt
GROUP_NOT_EXISTS=Benutzergruppe existiert nicht
GROUP_HAS_USER=Benutzer existieren in der Benutzergruppe und k\u00F6nnen nicht gel\u00F6scht werden
USER_WITHDRAW_NOT_EXISTS=Mitgliederaustritt existiert nicht
USER_BALANCE_NOT_ENOUGH=Unzureichendes Guthaben
USER_WITHDRAW_HAS_HANDLE=Auszahlung wurde bearbeitet
USER_WITHDRAW_LESS_MIN_AMOUNT=Auszahlung muss gr\u00F6\u00DFer sein als [{}]
USER_WITHDRAW_LESS_MAX_AMOUNT=Die Auszahlung muss kleiner sein als [{}]
USER_WITHDRAW_LESS_MAX_PROCESS=Sie haben eine Bestellung im Widerrufsprozess.
USER_FROZEN_BALANCE_NOT_ENOUGH=Unzureichendes Guthaben
USER_ASSETS_SPOT_NOT_EXISTS=Benutzer-Assets existieren nicht
USER_FAVORITE_TRADE_PAIR_EXISTS=Bevorzugtes Handelspaar existiert
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=Bevorzugtes Handelspaar existiert nicht
USER_RECHARGE_NOT_EXISTS=Aufladedatensatz existiert nicht
USER_RECHARGE_HAS_HANDLE=Aufladedatensatz wurde verarbeitet
USER_WALLET_NOT_EXISTS=Mitglieds-Wallet existiert nicht
USER_SPOT_ORDER_NOT_EXISTS=Mitglieds-Spot-Order existiert nicht
USER_TRANSACTIONS_NOT_EXISTS=Mitgliedsrechnung existiert nicht
USER_MARGIN_ORDER_NOT_EXISTS=Mitgliedsvertragsauftrag existiert nicht
USER_MARGIN_CONFIG_NOT_EXISTS=Mitgliedsvertragskonfiguration existiert nicht
USER_CERTIFICATION_NOT_EXISTS=Mitgliedsauthentifizierungsinformationen sind nicht vorhanden
USER_CERTIFICATION_BEEN_HANDLE=Mitgliedsauthentifizierungsinformationen wurden verarbeitet
USER_CERTIFICATION_STATUS_SUCCESS=Mitgliedsauthentifizierungsinformationen wurden authentifiziert
USER_CERTIFICATION_STATUS_HANDLING=Mitgliedsauthentifizierungsinformationen werden \u00FCberpr\u00FCft
USER_CERTIFICATION_NOT_VERIFY=Identit\u00E4t [{}], Vorgang nicht zul\u00E4ssig
USER_CERTIFICATION_VERIFYING=W\u00E4hrend der Identit\u00E4tsauthentifizierung ist der Vorgang nicht zul\u00E4ssig
USER_CERTIFICATION_VERIFY_FAILURE=Identit\u00E4tsauthentifizierung fehlgeschlagen, Vorgang nicht zul\u00E4ssig
LEVEL_CONFIG_NOT_EXISTS=Konfiguration auf Mitgliedsebene existiert nicht
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=Die Standardkonfiguration der Mitgliedsebene kann nicht gel\u00F6scht werden
LEVEL_CONFIG_NAME_EXISTS=Levelname Es hei\u00DFt, dass es bereits existiert
USER_FUND_PASSWORD_NOT_EXISTS=Sie haben kein Fondskennwort festgelegt und k\u00F6nnen es nicht \u00E4ndern
USER_FUND_PASSWORD_ERROR=Falsches Fondspasswort
FUNDS_RECORD_NOT_EXISTS=Fondsdatensatz existiert nicht
USER_RECHARGE_LESS_MAX_PROCESS=Sie haben [{}] Bestellungen zur\u00FCckgezogen
AUTH_LOGIN_CAPTCHA_CODE_ERROR=Der Best\u00E4tigungscode ist falsch, Grund: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token ist abgelaufen
AUTH_MOBILE_NOT_EXISTS=Mobilnummer existiert nicht
MENU_NAME_DUPLICATE=Ein Men\u00FC mit diesem Namen existiert bereits
MENU_PARENT_NOT_EXISTS=Das \u00FCbergeordnete Men\u00FC existiert nicht
MENU_PARENT_ERROR=Kann sich nicht selbst als \u00FCbergeordnetes Men\u00FC festlegen
MENU_NOT_EXISTS=Men\u00FC existiert nicht
MENU_EXISTS_CHILDREN=Untermen\u00FC existiert und kann nicht gel\u00F6scht werden
MENU_PARENT_NOT_DIR_OR_MENU=Das \u00FCbergeordnete Men\u00FC muss ein Verzeichnis oder Men\u00FC sein
ROLE_NOT_EXISTS=Rolle existiert nicht
ROLE_NAME_DUPLICATE=Eine Rolle mit dem Namen [{}] existiert bereits
ROLE_CODE_DUPLICATE=Eine Rolle mit dem Code [{}] existiert bereits
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=Ein Typ, der in der Systemrolle integriert ist, kann nicht ausgef\u00FChrt werden.
ROLE_IS_DISABLE=Die Rolle mit dem Namen [{}] wurde deaktiviert
ROLE_ADMIN_CODE_ERROR=Der Code [{}] kann nicht verwendet werden
USER_USERNAME_EXISTS=Das Konto ist nicht verf\u00FCgbar
USER_MOBILE_EXISTS=Die Telefonnummer existiert bereits
USER_EMAIL_EXISTS=Die E-Mail-Adresse existiert bereits
USER_IMPORT_LIST_IS_EMPTY=Die importierten Benutzerdaten d\u00FCrfen nicht leer sein
USER_PASSWORD_FAILED=Benutzerkennwort\u00FCberpr\u00FCfung fehlgeschlagen
USER_IS_DISABLE=Der Benutzer mit dem Namen [{}] wurde deaktiviert
USER_COUNT_MAX=Benutzer konnte nicht erstellt werden. Der Grund ist: \u00DCberschreiten des maximalen Mandantenkontingents des Mandanten [{}]
DEPT_NAME_DUPLICATE=Eine Abteilung mit diesem Namen existiert bereits
DEPT_PARENT_NOT_EXITS=Die \u00FCbergeordnete Abteilung existiert nicht
DEPT_NOT_FOUND=Die aktuelle Abteilung existiert nicht
DEPT_EXITS_CHILDREN=Unterabteilungen existieren und k\u00F6nnen nicht gel\u00F6scht werden
DEPT_PARENT_ERROR=Kann sich nicht selbst als \u00FCbergeordnete Abteilung festlegen
DEPT_EXISTS_USER=Es gibt Mitarbeiter in der Abteilung und diese k\u00F6nnen nicht gel\u00F6scht werden
DEPT_NOT_ENABLE=Abteilung [{}] ist nicht im ge\u00F6ffneten Zustand und eine Auswahl ist nicht zul\u00E4ssig
DEPT_PARENT_IS_CHILD=Ihre eigene Unterabteilung kann nicht als \u00FCbergeordnete Abteilung festgelegt werden.
POST_NOT_FOUND=Die aktuelle Position existiert nicht
POST_NOT_ENABLE=Position [{}] ist nicht im ge\u00F6ffneten Zustand, Auswahl ist nicht zul\u00E4ssig
POST_NAME_DUPLICATE=Eine Position mit diesem Namen existiert bereits
POST_CODE_DUPLICATE=Eine Position mit dieser Kennung existiert bereits
DICT_TYPE_NOT_EXISTS=Der aktuelle W\u00F6rterbuchtyp existiert nicht
DICT_TYPE_NOT_ENABLE=Der W\u00F6rterbuchtyp ist nicht im ge\u00F6ffneten Zustand, eine Auswahl ist nicht zul\u00E4ssig.
DICT_TYPE_NAME_DUPLICATE=Ein W\u00F6rterbuchtyp mit diesem Namen existiert bereits
DICT_TYPE_TYPE_DUPLICATE=Ein W\u00F6rterbuchtyp mit diesem Typ existiert bereits
DICT_TYPE_HAS_CHILDREN=Kann nicht gel\u00F6scht werden, dieser W\u00F6rterbuchtyp hat noch W\u00F6rterbuchdaten
DICT_DATA_NOT_EXISTS=Aktuelle W\u00F6rterbuchdaten existieren nicht
DICT_DATA_NOT_ENABLE=W\u00F6rterbuchdaten [{}] sind nicht im ge\u00F6ffneten Zustand, Auswahl ist nicht zul\u00E4ssig
DICT_DATA_VALUE_DUPLICATE=W\u00F6rterbuchdaten mit diesem Wert existieren bereits
NOTICE_NOT_FOUND=Aktuelle Benachrichtigungsank\u00FCndigung existiert nicht
SMS_CHANNEL_NOT_EXISTS=SMS-Kanal existiert nicht
SMS_CHANNEL_DISABLE=SMS-Kanal ist nicht im ge\u00F6ffneten Zustand, Auswahl ist nicht zul\u00E4ssig
SMS_CHANNEL_HAS_CHILDREN=Kann nicht gel\u00F6scht werden, dieser SMS-Kanal hat noch SMS-Vorlagen
SMS_TEMPLATE_NOT_EXISTS=SMS-Vorlage existiert nicht Existiert
SMS_TEMPLATE_CODE_DUPLICATE=Die als [{}] codierte SMS-Vorlage existiert bereits
SMS_TEMPLATE_API_ERROR=Der Aufruf der SMS-API-Vorlage ist fehlgeschlagen. Der Grund ist: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=Die SMS-API-Vorlage kann nicht verwendet werden. Der Grund ist: Wird \u00FCberpr\u00FCft.
SMS_TEMPLATE_API_AUDIT_FAIL=Die SMS-API-Vorlage kann nicht verwendet werden. Der Grund ist: Genehmigung fehlgeschlagen, {}
SMS_TEMPLATE_API_NOT_FOUND=Die SMS-API-Vorlage kann nicht verwendet werden. Der Grund ist: Die Vorlage existiert nicht.
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Der Vorlagenparameter [{}] fehlt
SMS_CODE_NOT_FOUND=Der Best\u00E4tigungscode existiert nicht
SMS_CODE_EXPIRED=Der Best\u00E4tigungscode ist abgelaufen
SMS_CODE_USED=Der Best\u00E4tigungscode wurde verwendet
SMS_CODE_NOT_CORRECT=Der Best\u00E4tigungscode ist falsch
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Die Anzahl der pro Tag gesendeten SMS-Nachrichten wurde \u00FCberschritten
SMS_CODE_SEND_TOO_FAST=SMS-Nachrichten werden zu h\u00E4ufig gesendet
TENANT_NOT_EXISTS=Der Mandant existiert nicht
TENANT_DISABLE=Der Mandant mit dem Namen [{}] wurde deaktiviert
TENANT_EXPIRE=Der Mandant mit dem Namen [{}] ist abgelaufen
TENANT_CAN_NOT_UPDATE_SYSTEM=Der System-Tenant kann nicht ge\u00E4ndert, gel\u00F6scht usw. werden!
TENANT_CODE_DUPLICATE=Der Mandant mit dem Mandantencode [{}] existiert bereits
TENANT_WEBSITE_DUPLICATE=Der Mandant mit dem Dom\u00E4nennamen [{}] existiert bereits
TENANT_PACKAGE_NOT_EXISTS=Das Mandantenpaket existiert nicht
TENANT_PACKAGE_USED=Der Mandant verwendet dieses Paket. Bitte setzen Sie das Paket f\u00FCr den Mandanten zur\u00FCck, bevor Sie versuchen, es zu l\u00F6schen.
TENANT_PACKAGE_DISABLE=Das Mandantenpaket mit dem Namen [{}] wurde deaktiviert
OAUTH2_CLIENT_NOT_EXISTS=Der OAuth2-Client existiert nicht
OAUTH2_CLIENT_EXISTS=Die OAuth2-Client-ID existiert bereits
OAUTH2_CLIENT_DISABLE=Der OAuth2-Client ist deaktiviert
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=Der Autorisierungstyp wird nicht unterst\u00FCtzt
OAUTH2_CLIENT_SCOPE_OVER=Der Autorisierungsumfang ist zu gro\u00DF
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=Ung\u00FCltige Umleitungs-URI: [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=Ung\u00FCltiges Client-Geheimnis: {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=Die Client-ID stimmt nicht \u00FCberein
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=Die Umleitungs-URI stimmt nicht \u00FCberein
OAUTH2_GRANT_STATE_MISMATCH=Der Status stimmt nicht \u00FCberein
OAUTH2_GRANT_CODE_NOT_EXISTS=Code existiert nicht
OAUTH2_CODE_NOT_EXISTS=Code existiert nicht
OAUTH2_CODE_EXPIRE=Verifizierungscode abgelaufen
MAIL_ACCOUNT_NOT_EXISTS=E-Mail-Konto existiert nicht
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=Kann nicht gel\u00F6scht werden, das E-Mail-Konto hat noch E-Mail-Vorlagen.
MAIL_TEMPLATE_NOT_EXISTS=Mail-Vorlage existiert nicht
MAIL_TEMPLATE_CODE_EXISTS=Mail-Vorlagencode[{}] existiert bereits
MAIL_SEND_TEMPLATE_PARAM_MISS=Vorlagenparameter[{}] fehlt
MAIL_SEND_MAIL_NOT_EXISTS=E-Mail existiert nicht
MAIL_CODE_SEND_TOO_FAST=E-Mail wird zu h\u00E4ufig gesendet
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=\u00DCberschreitet die Anzahl der t\u00E4glich gesendeten E-Mails
MAIL_CODE_NOT_FOUND=Best\u00E4tigungscode existiert nicht
MAIL_CODE_EXPIRED=Best\u00E4tigungscode ist abgelaufen
MAIL_CODE_USED=Best\u00E4tigungscode wurde verwendet
MAIL_CODE_NOT_CORRECT=Best\u00E4tigungscode ist falsch
MAIL_IS_EXISTS=E-Mail wurde verwendet
NOTIFY_TEMPLATE_NOT_EXISTS=Interne Nachrichtenvorlage existiert nicht
NOTIFY_TEMPLATE_CODE_DUPLICATE=Interne Nachrichtenvorlage mit dem Code [{}] existiert bereits
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Vorlagenparameter[{}] fehlt
AGENT_NOT_EXISTS=Agentur existiert nicht
AGENT_INVITE_CODE_EXISTS=Einladungscode existiert bereits
AGENT_HAS_DESCENDANT=Dem Agenten sind untergeordnete Agenten zugeordnet, die nicht gel\u00F6scht werden k\u00F6nnen.
AGENT_ANCESTOR_NOT_AVAILABLE=\u00DCbergeordneter Agent ist nicht verf\u00FCgbar
AUTH_NOT_EXISTS=Mitgliedschaftszertifikat existiert nicht
CURRENCY_NOT_EXISTS=W\u00E4hrung existiert nicht
CURRENCYNOTRATE=Es gibt keinen Wechselkurs f\u00FCr diese W\u00E4hrung
BANNER_NOT_EXISTS=BANNER existiert nicht
TENANT_SERVER_NAME_NOT_EXISTS=Der Dom\u00E4nenname des System-Mandanten existiert nicht
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Mieterw\u00F6rterbuchdaten existieren nicht
TIME_CONTRACT_AMOUNT_LESS=Kaufbetrag ist geringer als der Mindestbetrag
TIME_CONTRACT_RECORD_NOT_EXISTS=Zeitlich begrenzter Vertragstransaktionsdatensatz existiert nicht
TRADE_DURATION_NOT_EXISTS=Konfiguration f\u00FCr die Auftragsdauer existiert nicht
TRADE_MULTIPLE_NOT_EXISTS=Konfiguration f\u00FCr Mehrfachbestellungen existiert nicht
USER_ACCOUNT_NOT_EMPTY=Konto darf nicht leer sein
AGENT_HAS_NOT_ANCESTOR=Der verschobene Agent hat keinen \u00FCbergeordneten Agenten.
USER_EMAIL_NOT_EXISTS=E-Mail existiert nicht
Mail_CODE_SEND_FAIL=E-Mail-Best\u00E4tigungscode konnte nicht gesendet werden
GOOGLE_SECRET_BINDING_EXPIRED=Der gebundene Google-Schl\u00FCssel ist abgelaufen, bitte holen Sie ihn erneut ab
GOOGLE_CODE_ERROR=Der Google-Best\u00E4tigungscode ist falsch
ACCOUNT_IS_ERROR=Anmeldung fehlgeschlagen, Konto existiert nicht
GOOGLE_SECRET_IS_NOT_BINDING=Der Google-Schl\u00FCssel ist nicht gebunden. Bitte zuerst binden.
TRADE_CONTRACT_RECORD_NOT_EXISTS=Vertragsauftrag existiert nicht
TRADE_CONTRACT_CONFIG_ERROR=Fehler bei der Vertragskonfiguration
ARG_ORDER_STATUS_IS_EMPTY=Parameter Bestellstatus darf nicht leer sein
ARG_LEVERAGE_IS_EMPTY=Der Parameter Leverage darf nicht leer sein
ARG_ORDER_TYPE_IS_EMPTY=Parameter Auftragstyp darf nicht leer sein
ARG_VALUE_ERROR=Parameterwertfehler
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=Die Vertragsbestellung wurde storniert
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=Der Vertragsauftrag wurde abgeschlossen
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=Der Vertrag wurde geschlossen.
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Fehler beim Wechselkurs der Bestellung
USER_FORBIDDEN_FUNC=Diese Funktion ist deaktiviert
IMAGE_URL_ERROR=Bildlink-Fehler
