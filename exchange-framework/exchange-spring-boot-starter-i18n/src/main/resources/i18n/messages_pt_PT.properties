COUNTRY_TH=Tail\u00E2ndia
COUNTRY_JP=Jap\u00E3o
COUNTRY_KR=Coreia do Sul
COUNTRY_PH=Filipinas
COUNTRY_VN=Vietn\u00E3
COUNTRY_CN=China
COUNTRY_GB=Reino Unido
COUNTRY_FR=Fran\u00E7a
COUNTRY_US=Estados Unidos
COUNTRY_ES=Espanha
COUNTRY_DE=Alemanha
COUNTRY_RU=R\u00FAssia
COUNTRY_CA=Canad\u00E1
COUNTRY_TR=Turquia
COUNTRY_PT=Portugal
COUNTRY_MA=Marrocos
COUNTRY_DZ=Arg\u00E9lia
COUNTRY_IT=It\u00E1lia
COUNTRY_CO=Col\u00F4mbia
COUNTRY_MX=M\u00E9xico
COUNTRY_CH=Su\u00ED\u00E7a
COUNTRY_BE=B\u00E9lgica
COUNTRY_AR=Argentina
COUNTRY_NO=Noruega
COUNTRY_HK=Hong Kong
BANK=Cart\u00E3o Banc\u00E1rio
CRYPTO=Criptomoeda
USER_CERTIFIED_STATUS_NOT=N\u00E3o Certificado
USER_CERTIFIED_STATUS_HANDLING=Certificando
USER_CERTIFIED_STATUS_SUCCESS=Certificado
USER_CERTIFIED_STATUS_FAIL=Falha na Certifica\u00E7\u00E3o
ACCOUNT_NOT_EMPTY=A conta n\u00E3o pode estar vazia
PASSWORD_NOT_EMPTY=A senha n\u00E3o pode estar vazia
PASSWORD_NOT_LENGTH_6_16=O comprimento da senha deve ser de 6-16 caracteres
PASSWORD_FORMATTER_ERROR=O formato da senha deve conter n\u00FAmeros e letras
ACCOUNT_TYPE_ERROR=Tipo de conta incorreto
MAIL_FORMATTER_ERROR=Formato de email incorreto
MOBILE_FORMATTER_ERROR=Formato de n\u00FAmero de telefone incorreto
AREA_NOT_EMPTY=A \u00E1rea n\u00E3o pode estar vazia
MAIL_SCENE_ERROR=Erro no cen\u00E1rio de envio de email
SMS_SCENE_ERROR=Erro no cen\u00E1rio de envio de SMS
REAL_NAME_NOT_EMPTY=O nome real n\u00E3o pode estar vazio
CERTIFICATION_TYPE_ERROR=Tipo de certifica\u00E7\u00E3o de identidade incorreto
CERTIFICATION_CODE_NOT_EMPTY=O c\u00F3digo do documento n\u00E3o pode estar vazio
CERTIFICATION_FRONT_NOT_EMPTY=A foto da frente do documento n\u00E3o pode estar vazia
CERTIFICATION_BACK_NOT_EMPTY=A foto do verso do documento n\u00E3o pode estar vazia
TRADE_PAIR_NOT_EMPTY=Por favor, selecione um par de negocia\u00E7\u00E3o
FUNDS_RECORD_OP_TYPE=Tipo de fundo incorreto
CURRENCY_NOT_EMPTY=Por favor, selecione uma moeda
PAY_METHOD_ERROR=Por favor, selecione um m\u00E9todo de pagamento
RECHARGE_AMOUNT_ERROR=Por favor, insira o valor da recarga
WITHDRAW_AMOUNT_ERROR=Por favor, insira o valor do saque
FUNDS_PASSWORD_ERROR=Senha de fundos incorreta
WALLET_NOT_EMPTY=Por favor, selecione uma carteira
AUTH_CODE_NOT_EMPTY=Por favor, insira o c\u00F3digo de verifica\u00E7\u00E3o
AVATAR_FORMATTER_ERROR=Por favor, selecione um avatar
WALLET_TYPE_ERROR=Tipo de carteira incorreto
WALLET_NAME_NOT_EMPTY=Por favor, insira o nome da carteira
WALLET_ACCOUNT_NOT_EMPTY=Por favor, insira a conta da carteira
WALLET_TYPE_NAME_NOT_EMPTY=Por favor, insira o nome do banco
ASSET_Type_ERROR=Tipo de ativo incorreto
KEY_NOT_EMPTY=A palavra-chave n\u00E3o pode estar vazia
AMOUNT_NOT_EMPTY=Erro na quantidade
TRADE_DIRECT_ERROR=Dire\u00E7\u00E3o de negocia\u00E7\u00E3o incorreta
TRADE_DURATION_ERROR=Limite de tempo de negocia\u00E7\u00E3o incorreto
TRADE_SEND_TIME_ERROR=Hor\u00E1rio de envio da negocia\u00E7\u00E3o incorreto
TRADE_PRICE_TIME_ERROR=Hor\u00E1rio do pre\u00E7o da negocia\u00E7\u00E3o incorreto
TRADE_PAGE_TYPE_ERROR=Categoria incorreta
SUCCESS=Sucesso
WAITHANDLE=Processando
FAILURE=Falha
PENDING=Processando
BAD_REQUEST=Solicita\u00E7\u00E3o incorreta
UNKNOW_AUTHORIZED=Tipo de autoriza\u00E7\u00E3o desconhecida [{}]
TOKEN_NOT_SUPPORT_MODE=Interface Token n\u00E3o suporta modo de autoriza\u00E7\u00E3o [{}]
CLIENT_ERROR=client_id ou client_secret n\u00E3o foram passados corretamente
TOKEN_REFRESH_INVALID=Token de atualiza\u00E7\u00E3o inv\u00E1lido
TOKEN_REFRESH_CLIENT_ERROR=ID do cliente do token de atualiza\u00E7\u00E3o incorreto
TOKEN_REFRESH_EXPIRE=Token de atualiza\u00E7\u00E3o expirou
TOKEN_NOT_EXISTS=Token de acesso n\u00E3o existe
TOKEN_EXPIRE=Token de acesso expirou
GRANT_RESPONSE_TYPE_ERROR=O valor do par\u00E2metro response_type s\u00F3 permite code e token
UNAUTHORIZED=Conta n\u00E3o logada
FORBIDDEN=Sem permiss\u00E3o para esta opera\u00E7\u00E3o
NOT_FOUND=O recurso solicitado n\u00E3o existe
METHOD_NOT_ALLOWED=M\u00E9todo de solicita\u00E7\u00E3o n\u00E3o permitido
LOCKED=Bloqueado
TOO_MANY_REQUESTS=Muitas solicita\u00E7\u00F5es, tente novamente mais tarde
INTERNAL_SERVER_ERROR=Erro do sistema
NOT_IMPLEMENTED=Funcionalidade n\u00E3o implementada/n\u00E3o habilitada
REPEATED_REQUESTS=Solicita\u00E7\u00E3o repetida, tente novamente mais tarde
DEMO_DENY=Modo demo, opera\u00E7\u00F5es de escrita proibidas
VALUE_ERROR=Erro de valor
GOOGLE_AUTH_NOT_BIND=Autenticador Google n\u00E3o vinculado
GOOGLE_AUTH_CODE_ERROR=C\u00F3digo do autenticador Google incorreto
SCHEDULER_JOB_STOP=[Tarefa Agendada - Desabilitada][Consulte https://doc.iocoder.cn/job/ para habilitar]
CANDLE_TABLE_NAME_NOT_AVAILABLE=Nome da tabela K-line inv\u00E1lido
CANDLE_BAR_VALUE_ERROR=Par\u00E2metro bar inv\u00E1lido
CANDLE_PRICE_ERROR=Erro ao obter pre\u00E7o
TRADE_PAIR_NOT_EXISTS=Par de negocia\u00E7\u00E3o n\u00E3o existe
TRADE_PAIR_EXISTS=Par de negocia\u00E7\u00E3o j\u00E1 existe
TRADE_PAIR_TENANT_NOT_EXISTS=Locat\u00E1rio n\u00E3o possui este par de negocia\u00E7\u00E3o
TRADE_PAIR_TENANT_EXISTS=Par de negocia\u00E7\u00E3o do locat\u00E1rio j\u00E1 existe
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=Configura\u00E7\u00E3o do tipo de ativo do par de negocia\u00E7\u00E3o do locat\u00E1rio n\u00E3o existe
TRADE_TENANT_NEED_DEFAULT=Deve haver um par de negocia\u00E7\u00E3o padr\u00E3o do locat\u00E1rio
TRADE_TENANT_DUPLICATE_DEFAULT=Par de negocia\u00E7\u00E3o padr\u00E3o do locat\u00E1rio j\u00E1 existe
CONFIG_NOT_EXISTS=Configura\u00E7\u00E3o de par\u00E2metro n\u00E3o existe
CONFIG_KEY_DUPLICATE=Chave de configura\u00E7\u00E3o de par\u00E2metro duplicada
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=N\u00E3o \u00E9 poss\u00EDvel deletar configura\u00E7\u00E3o de par\u00E2metro do tipo sistema incorporado
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Falha ao obter configura\u00E7\u00E3o de par\u00E2metro, motivo: n\u00E3o \u00E9 permitido obter configura\u00E7\u00E3o invis\u00EDvel
JOB_NOT_EXISTS=Tarefa agendada n\u00E3o existe
JOB_HANDLER_EXISTS=Manipulador da tarefa agendada j\u00E1 existe
JOB_CHANGE_STATUS_INVALID=S\u00F3 \u00E9 permitido modificar para status aberto ou fechado
JOB_CHANGE_STATUS_EQUALS=A tarefa agendada j\u00E1 est\u00E1 neste status, n\u00E3o h\u00E1 necessidade de modificar
JOB_UPDATE_ONLY_NORMAL_STATUS=Apenas tarefas no status aberto podem ser modificadas
JOB_CRON_EXPRESSION_VALID=Express\u00E3o CRON incorreta
JOB_HANDLER_BEAN_NOT_EXISTS=Bean do manipulador da tarefa agendada n\u00E3o existe
JOB_HANDLER_BEAN_TYPE_ERROR=Tipo do Bean do manipulador da tarefa agendada incorreto, n\u00E3o implementa interface JobHandler
API_ERROR_LOG_NOT_FOUND=Log de erro da API n\u00E3o existe
API_ERROR_LOG_PROCESSED=Log de erro da API j\u00E1 processado
FILE_PATH_EXISTS=Caminho do arquivo j\u00E1 existe
FILE_NOT_EXISTS=Arquivo n\u00E3o existe
FILE_IS_EMPTY=Arquivo est\u00E1 vazio
CODEGEN_TABLE_EXISTS=Defini\u00E7\u00E3o da tabela j\u00E1 existe
CODEGEN_IMPORT_TABLE_NULL=A tabela importada n\u00E3o existe
CODEGEN_IMPORT_COLUMNS_NULL=Os campos importados n\u00E3o existem
CODEGEN_TABLE_NOT_EXISTS=Defini\u00E7\u00E3o da tabela n\u00E3o existe
CODEGEN_COLUMN_NOT_EXISTS=Defini\u00E7\u00E3o do campo n\u00E3o existe
CODEGEN_SYNC_COLUMNS_NULL=Os campos sincronizados n\u00E3o existem
CODEGEN_SYNC_NONE_CHANGE=Falha na sincroniza\u00E7\u00E3o, n\u00E3o h\u00E1 mudan\u00E7as
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Coment\u00E1rio da tabela do banco de dados n\u00E3o preenchido
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Coment\u00E1rio do campo da tabela do banco de dados ({}) n\u00E3o preenchido
CODEGEN_MASTER_TABLE_NOT_EXISTS=Defini\u00E7\u00E3o da tabela principal (id={}) n\u00E3o existe, verifique
CODEGEN_SUB_COLUMN_NOT_EXISTS=Campo da subtabela (id={}) n\u00E3o existe, verifique
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=Falha na gera\u00E7\u00E3o de c\u00F3digo da tabela principal, motivo: ela n\u00E3o possui subtabela
FILE_CONFIG_NOT_EXISTS=Configura\u00E7\u00E3o de arquivo n\u00E3o existe
FILE_CONFIG_DELETE_FAIL_MASTER=Esta configura\u00E7\u00E3o de arquivo n\u00E3o pode ser deletada, motivo: \u00E9 a configura\u00E7\u00E3o principal, deletar causar\u00E1 impossibilidade de upload de arquivos
DATA_SOURCE_CONFIG_NOT_EXISTS=Configura\u00E7\u00E3o de fonte de dados n\u00E3o existe
DATA_SOURCE_CONFIG_NOT_OK=Configura\u00E7\u00E3o de fonte de dados incorreta, n\u00E3o \u00E9 poss\u00EDvel conectar
DEMO01_CONTACT_NOT_EXISTS=Contato de exemplo n\u00E3o existe
DEMO02_CATEGORY_NOT_EXISTS=Categoria de exemplo n\u00E3o existe
DEMO02_CATEGORY_EXITS_CHILDREN=Existem subcategorias de exemplo, n\u00E3o \u00E9 poss\u00EDvel deletar
DEMO02_CATEGORY_PARENT_NOT_EXITS=Categoria pai de exemplo n\u00E3o existe
DEMO02_CATEGORY_PARENT_ERROR=N\u00E3o \u00E9 poss\u00EDvel definir a si mesmo como categoria pai de exemplo
DEMO02_CATEGORY_NAME_DUPLICATE=J\u00E1 existe uma categoria de exemplo com este nome
DEMO02_CATEGORY_PARENT_IS_CHILD=N\u00E3o \u00E9 poss\u00EDvel definir sua pr\u00F3pria subcategoria de exemplo como categoria pai de exemplo
DEMO03_STUDENT_NOT_EXISTS=Estudante n\u00E3o existe
DEMO03_GRADE_NOT_EXISTS=S\u00E9rie do estudante n\u00E3o existe
DEMO03_GRADE_EXISTS=S\u00E9rie do estudante j\u00E1 existe
AREA_NOT_EXISTS=Esta \u00E1rea n\u00E3o existe
USER_NOT_EXISTS=Usu\u00E1rio n\u00E3o existe
USER_MOBILE_NOT_EXISTS=N\u00FAmero de telefone n\u00E3o registrado para usu\u00E1rio
USER_MOBILE_USED=Este n\u00FAmero de telefone j\u00E1 est\u00E1 sendo usado
USER_ACCOUNT_USED=Conta j\u00E1 est\u00E1 sendo usada
USER_EMAIL_USED=Email j\u00E1 est\u00E1 sendo usado
USER_POINT_NOT_ENOUGH=Saldo de pontos do usu\u00E1rio insuficiente
USER_OLD_PASSWORD_NOT_MATCH=Senha antiga incorreta
USER_BALANCE_ERROR=Erro no saldo do usu\u00E1rio
USER_CONFIG_NOT_SUPPORTED=Item de configura\u00E7\u00E3o do usu\u00E1rio temporariamente n\u00E3o suportado
AUTH_LOGIN_BAD_CREDENTIALS=Falha no login, conta ou senha incorreta
AUTH_LOGIN_USER_DISABLED=Falha no login, conta desabilitada
AUTH_ACCOUNT_FORMAT_ERROR=Formato da conta de autentica\u00E7\u00E3o incorreto
TAG_NOT_EXISTS=Tag n\u00E3o existe
TAG_NAME_EXISTS=Tag j\u00E1 existe
TAG_HAS_USER=Existem usu\u00E1rios na tag do usu\u00E1rio, n\u00E3o \u00E9 poss\u00EDvel deletar
POINT_RECORD_BIZ_NOT_SUPPORT=Tipo de neg\u00F3cio do registro de pontos do usu\u00E1rio n\u00E3o suportado
SIGN_IN_CONFIG_NOT_EXISTS=Regra de check-in n\u00E3o existe
SIGN_IN_CONFIG_EXISTS=Regra de dias de check-in j\u00E1 existe
SIGN_IN_RECORD_TODAY_EXISTS=J\u00E1 fez check-in hoje, n\u00E3o repita o check-in
LEVEL_NOT_EXISTS=N\u00EDvel do usu\u00E1rio n\u00E3o existe
LEVEL_NAME_EXISTS=Nome do n\u00EDvel do usu\u00E1rio j\u00E1 est\u00E1 sendo usado
LEVEL_VALUE_EXISTS=Valor do n\u00EDvel do usu\u00E1rio j\u00E1 est\u00E1 sendo usado
LEVEL_EXPERIENCE_MIN=Experi\u00EAncia de upgrade deve ser maior que a experi\u00EAncia de upgrade definida no n\u00EDvel anterior
LEVEL_EXPERIENCE_MAX=Experi\u00EAncia de upgrade deve ser menor que a experi\u00EAncia de upgrade definida no pr\u00F3ximo n\u00EDvel
LEVEL_HAS_USER=Existem usu\u00E1rios no n\u00EDvel do usu\u00E1rio, n\u00E3o \u00E9 poss\u00EDvel deletar
EXPERIENCE_BIZ_NOT_SUPPORT=Tipo de neg\u00F3cio de experi\u00EAncia do usu\u00E1rio n\u00E3o suportado
GROUP_NOT_EXISTS=Grupo de usu\u00E1rio n\u00E3o existe
GROUP_HAS_USER=Grupo de usu\u00E1rio cont\u00E9m usu\u00E1rios, n\u00E3o \u00E9 poss\u00EDvel deletar
USER_WITHDRAW_NOT_EXISTS=Saque do membro n\u00E3o existe
USER_BALANCE_NOT_ENOUGH=Saldo insuficiente
USER_WITHDRAW_HAS_HANDLE=Saque j\u00E1 processado
USER_WITHDRAW_LESS_MIN_AMOUNT=Saque deve ser maior que [{}]
USER_WITHDRAW_LESS_MAX_AMOUNT=Saque deve ser menor que [{}]
USER_WITHDRAW_LESS_MAX_PROCESS=Voc\u00EA tem uma ordem sendo processada para saque
USER_FROZEN_BALANCE_NOT_ENOUGH=Saldo insuficiente
USER_ASSETS_SPOT_NOT_EXISTS=Ativo do usu\u00E1rio n\u00E3o existe
USER_FAVORITE_TRADE_PAIR_EXISTS=Par de negocia\u00E7\u00E3o favorito j\u00E1 existe
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=Par de negocia\u00E7\u00E3o favorito n\u00E3o existe
USER_RECHARGE_NOT_EXISTS=Registro de recarga n\u00E3o existe
USER_RECHARGE_HAS_HANDLE=Registro de recarga j\u00E1 processado
USER_WALLET_NOT_EXISTS=Carteira do membro n\u00E3o existe
USER_SPOT_ORDER_NOT_EXISTS=Ordem spot do membro n\u00E3o existe
USER_TRANSACTIONS_NOT_EXISTS=Transa\u00E7\u00E3o do membro n\u00E3o existe
USER_MARGIN_ORDER_NOT_EXISTS=Ordem de contrato do membro n\u00E3o existe
USER_MARGIN_CONFIG_NOT_EXISTS=Configura\u00E7\u00E3o de contrato do membro n\u00E3o existe
USER_CERTIFICATION_NOT_EXISTS=Informa\u00E7\u00E3o de certifica\u00E7\u00E3o do membro n\u00E3o existe
USER_CERTIFICATION_BEEN_HANDLE=Informa\u00E7\u00E3o de certifica\u00E7\u00E3o do membro j\u00E1 processada
USER_CERTIFICATION_STATUS_SUCCESS=Informa\u00E7\u00E3o de certifica\u00E7\u00E3o do membro j\u00E1 certificada
USER_CERTIFICATION_STATUS_HANDLING=Informa\u00E7\u00E3o de certifica\u00E7\u00E3o do membro em auditoria
USER_CERTIFICATION_NOT_VERIFY=Identidade [{}], opera\u00E7\u00E3o n\u00E3o permitida
USER_CERTIFICATION_VERIFYING=Certifica\u00E7\u00E3o de identidade em andamento, opera\u00E7\u00E3o n\u00E3o permitida
USER_CERTIFICATION_VERIFY_FAILURE=Falha na certifica\u00E7\u00E3o de identidade, opera\u00E7\u00E3o n\u00E3o permitida
LEVEL_CONFIG_NOT_EXISTS=Configura\u00E7\u00E3o de n\u00EDvel de membro n\u00E3o existe
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=Configura\u00E7\u00E3o de n\u00EDvel padr\u00E3o de membro n\u00E3o pode ser deletada
LEVEL_CONFIG_NAME_EXISTS=Nome do n\u00EDvel j\u00E1 existe
USER_FUND_PASSWORD_NOT_EXISTS=Voc\u00EA n\u00E3o definiu senha de fundos, n\u00E3o pode modificar
USER_FUND_PASSWORD_ERROR=Senha de fundos incorreta
FUNDS_RECORD_NOT_EXISTS=Registro de fundos n\u00E3o existe
USER_RECHARGE_LESS_MAX_PROCESS=Voc\u00EA tem [{}] ordens sendo processadas para saque
AUTH_LOGIN_CAPTCHA_CODE_ERROR=C\u00F3digo de verifica\u00E7\u00E3o incorreto, motivo: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token expirou
AUTH_MOBILE_NOT_EXISTS=N\u00FAmero de telefone n\u00E3o existe
MENU_NAME_DUPLICATE=J\u00E1 existe um menu com este nome
MENU_PARENT_NOT_EXISTS=Menu pai n\u00E3o existe
MENU_PARENT_ERROR=N\u00E3o \u00E9 poss\u00EDvel definir a si mesmo como menu pai
MENU_NOT_EXISTS=Menu n\u00E3o existe
MENU_EXISTS_CHILDREN=Existem submenus, n\u00E3o \u00E9 poss\u00EDvel deletar
MENU_PARENT_NOT_DIR_OR_MENU=O tipo do menu pai deve ser diret\u00F3rio ou menu
ROLE_NOT_EXISTS=Fun\u00E7\u00E3o n\u00E3o existe
ROLE_NAME_DUPLICATE=J\u00E1 existe uma fun\u00E7\u00E3o com o nome [{}]
ROLE_CODE_DUPLICATE=J\u00E1 existe uma fun\u00E7\u00E3o com o c\u00F3digo [{}]
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=N\u00E3o \u00E9 poss\u00EDvel operar fun\u00E7\u00F5es do tipo sistema incorporado
ROLE_IS_DISABLE=A fun\u00E7\u00E3o com nome [{}] foi desabilitada
ROLE_ADMIN_CODE_ERROR=C\u00F3digo [{}] n\u00E3o pode ser usado
USER_USERNAME_EXISTS=Conta j\u00E1 existe
USER_MOBILE_EXISTS=N\u00FAmero de telefone j\u00E1 existe
USER_EMAIL_EXISTS=Email j\u00E1 existe
USER_IMPORT_LIST_IS_EMPTY=Dados de usu\u00E1rio importados n\u00E3o podem estar vazios
USER_PASSWORD_FAILED=Falha na valida\u00E7\u00E3o da senha do usu\u00E1rio
USER_IS_DISABLE=Usu\u00E1rio com nome [{}] foi desabilitado
USER_COUNT_MAX=Falha ao criar usu\u00E1rio, motivo: excede a cota m\u00E1xima do locat\u00E1rio [{}]
DEPT_NAME_DUPLICATE=J\u00E1 existe um departamento com este nome
DEPT_PARENT_NOT_EXITS=Departamento pai n\u00E3o existe
DEPT_NOT_FOUND=Departamento atual n\u00E3o existe
DEPT_EXITS_CHILDREN=Existem subdepartamentos, n\u00E3o \u00E9 poss\u00EDvel deletar
DEPT_PARENT_ERROR=N\u00E3o \u00E9 poss\u00EDvel definir a si mesmo como departamento pai
DEPT_EXISTS_USER=Existem funcion\u00E1rios no departamento, n\u00E3o \u00E9 poss\u00EDvel deletar
DEPT_NOT_ENABLE=Departamento [{}] n\u00E3o est\u00E1 em status ativo, n\u00E3o \u00E9 permitido selecionar
DEPT_PARENT_IS_CHILD=N\u00E3o \u00E9 poss\u00EDvel definir seu pr\u00F3prio subdepartamento como departamento pai
POST_NOT_FOUND=Cargo atual n\u00E3o existe
POST_NOT_ENABLE=Cargo [{}] n\u00E3o est\u00E1 em status ativo, n\u00E3o \u00E9 permitido selecionar
POST_NAME_DUPLICATE=J\u00E1 existe um cargo com este nome
POST_CODE_DUPLICATE=J\u00E1 existe um cargo com este identificador
DICT_TYPE_NOT_EXISTS=Tipo de dicion\u00E1rio atual n\u00E3o existe
DICT_TYPE_NOT_ENABLE=Tipo de dicion\u00E1rio n\u00E3o est\u00E1 em status ativo, n\u00E3o \u00E9 permitido selecionar
DICT_TYPE_NAME_DUPLICATE=J\u00E1 existe um tipo de dicion\u00E1rio com este nome
DICT_TYPE_TYPE_DUPLICATE=J\u00E1 existe um tipo de dicion\u00E1rio com este tipo
DICT_TYPE_HAS_CHILDREN=N\u00E3o \u00E9 poss\u00EDvel deletar, este tipo de dicion\u00E1rio ainda possui dados de dicion\u00E1rio
DICT_DATA_NOT_EXISTS=Dados de dicion\u00E1rio atual n\u00E3o existem
DICT_DATA_NOT_ENABLE=Dados de dicion\u00E1rio [{}] n\u00E3o est\u00E3o em status ativo, n\u00E3o \u00E9 permitido selecionar
DICT_DATA_VALUE_DUPLICATE=J\u00E1 existem dados de dicion\u00E1rio com este valor
NOTICE_NOT_FOUND=An\u00FAncio atual n\u00E3o existe
SMS_CHANNEL_NOT_EXISTS=Canal de SMS n\u00E3o existe
SMS_CHANNEL_DISABLE=Canal de SMS n\u00E3o est\u00E1 em status ativo, n\u00E3o \u00E9 permitido selecionar
SMS_CHANNEL_HAS_CHILDREN=N\u00E3o \u00E9 poss\u00EDvel deletar, este canal de SMS ainda possui modelos de SMS
SMS_TEMPLATE_NOT_EXISTS=Modelo de SMS n\u00E3o existe
SMS_TEMPLATE_CODE_DUPLICATE=J\u00E1 existe um modelo de SMS com c\u00F3digo [{}]
SMS_TEMPLATE_API_ERROR=Falha na chamada da API do modelo de SMS, motivo: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=Modelo de SMS da API n\u00E3o pode ser usado, motivo: em auditoria
SMS_TEMPLATE_API_AUDIT_FAIL=Modelo de SMS da API n\u00E3o pode ser usado, motivo: auditoria n\u00E3o passou, {}
SMS_TEMPLATE_API_NOT_FOUND=Modelo de SMS da API n\u00E3o pode ser usado, motivo: modelo n\u00E3o existe
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Par\u00E2metro do modelo [{}] ausente
SMS_CODE_NOT_FOUND=C\u00F3digo de verifica\u00E7\u00E3o n\u00E3o existe
SMS_CODE_EXPIRED=C\u00F3digo de verifica\u00E7\u00E3o expirou
SMS_CODE_USED=C\u00F3digo de verifica\u00E7\u00E3o j\u00E1 usado
SMS_CODE_NOT_CORRECT=C\u00F3digo de verifica\u00E7\u00E3o incorreto
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Excedeu a quantidade di\u00E1ria de envio de SMS
SMS_CODE_SEND_TOO_FAST=Envio de SMS muito frequente
TENANT_NOT_EXISTS=Locat\u00E1rio n\u00E3o existe
TENANT_DISABLE=Locat\u00E1rio com nome [{}] foi desabilitado
TENANT_EXPIRE=Locat\u00E1rio com nome [{}] expirou
TENANT_CAN_NOT_UPDATE_SYSTEM=Locat\u00E1rio do sistema n\u00E3o pode ser modificado, deletado ou outras opera\u00E7\u00F5es!
TENANT_CODE_DUPLICATE=Locat\u00E1rio com c\u00F3digo [{}] j\u00E1 existe
TENANT_WEBSITE_DUPLICATE=Locat\u00E1rio com dom\u00EDnio [{}] j\u00E1 existe
TENANT_PACKAGE_NOT_EXISTS=Pacote do locat\u00E1rio n\u00E3o existe
TENANT_PACKAGE_USED=Locat\u00E1rio est\u00E1 usando este pacote, redefina o pacote do locat\u00E1rio antes de tentar deletar
TENANT_PACKAGE_DISABLE=Pacote do locat\u00E1rio com nome [{}] foi desabilitado
OAUTH2_CLIENT_NOT_EXISTS=Cliente OAuth2 n\u00E3o existe
OAUTH2_CLIENT_EXISTS=ID do cliente OAuth2 j\u00E1 existe
OAUTH2_CLIENT_DISABLE=Cliente OAuth2 foi desabilitado
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=N\u00E3o suporta este tipo de autoriza\u00E7\u00E3o
OAUTH2_CLIENT_SCOPE_OVER=Escopo de autoriza\u00E7\u00E3o muito amplo
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=redirect_uri inv\u00E1lido: [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=client_secret inv\u00E1lido: {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id n\u00E3o corresponde
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=redirect_uri n\u00E3o corresponde
OAUTH2_GRANT_STATE_MISMATCH=state n\u00E3o corresponde
OAUTH2_GRANT_CODE_NOT_EXISTS=code n\u00E3o existe
OAUTH2_CODE_NOT_EXISTS=code n\u00E3o existe
OAUTH2_CODE_EXPIRE=C\u00F3digo de verifica\u00E7\u00E3o expirou
MAIL_ACCOUNT_NOT_EXISTS=Conta de email n\u00E3o existe
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=N\u00E3o \u00E9 poss\u00EDvel deletar, esta conta de email ainda possui modelos de email
MAIL_TEMPLATE_NOT_EXISTS=Modelo de email n\u00E3o existe
MAIL_TEMPLATE_CODE_EXISTS=C\u00F3digo do modelo de email [{}] j\u00E1 existe
MAIL_SEND_TEMPLATE_PARAM_MISS=Par\u00E2metro do modelo [{}] ausente
MAIL_SEND_MAIL_NOT_EXISTS=Email n\u00E3o existe
MAIL_CODE_SEND_TOO_FAST=Envio de email muito frequente
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Excedeu a quantidade di\u00E1ria de envio de emails
MAIL_CODE_NOT_FOUND=C\u00F3digo de verifica\u00E7\u00E3o n\u00E3o existe
MAIL_CODE_EXPIRED=C\u00F3digo de verifica\u00E7\u00E3o expirou
MAIL_CODE_USED=C\u00F3digo de verifica\u00E7\u00E3o j\u00E1 usado
MAIL_CODE_NOT_CORRECT=C\u00F3digo de verifica\u00E7\u00E3o incorreto
MAIL_IS_EXISTS=Endere\u00E7o de email j\u00E1 est\u00E1 sendo usado
NOTIFY_TEMPLATE_NOT_EXISTS=Modelo de mensagem interna n\u00E3o existe
NOTIFY_TEMPLATE_CODE_DUPLICATE=J\u00E1 existe um modelo de mensagem interna com c\u00F3digo [{}]
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Par\u00E2metro do modelo [{}] ausente
AGENT_NOT_EXISTS=Agente n\u00E3o existe
AGENT_INVITE_CODE_EXISTS=C\u00F3digo de convite j\u00E1 existe
AGENT_HAS_DESCENDANT=Agente ainda possui subagentes, n\u00E3o pode deletar
AGENT_ANCESTOR_NOT_AVAILABLE=Agente pai n\u00E3o dispon\u00EDvel
AUTH_NOT_EXISTS=Certifica\u00E7\u00E3o de membro n\u00E3o existe
CURRENCY_NOT_EXISTS=Moeda n\u00E3o existe
CURRENCYNOTRATE=Esta moeda temporariamente n\u00E3o possui taxa de c\u00E2mbio
BANNER_NOT_EXISTS=BANNER n\u00E3o existe
TENANT_SERVER_NAME_NOT_EXISTS=Nome de dom\u00EDnio do locat\u00E1rio do sistema n\u00E3o existe
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Dados de dicion\u00E1rio do locat\u00E1rio n\u00E3o existem
TIME_CONTRACT_AMOUNT_LESS=Valor de compra menor que o valor m\u00EDnimo
TIME_CONTRACT_RECORD_NOT_EXISTS=Registro de transa\u00E7\u00E3o de contrato limitado por tempo n\u00E3o existe
TRADE_DURATION_NOT_EXISTS=Configura\u00E7\u00E3o de dura\u00E7\u00E3o da ordem n\u00E3o existe
TRADE_MULTIPLE_NOT_EXISTS=Configura\u00E7\u00E3o de multiplicador da ordem n\u00E3o existe
USER_ACCOUNT_NOT_EMPTY=Conta n\u00E3o pode estar vazia
AGENT_HAS_NOT_ANCESTOR=Agente movido n\u00E3o possui nenhum agente pai
USER_EMAIL_NOT_EXISTS=Email n\u00E3o existe
Mail_CODE_SEND_FAIL=Falha no envio do c\u00F3digo de verifica\u00E7\u00E3o por email
#******** Google verification related
GOOGLE_SECRET_BINDING_EXPIRED=Chave secreta do Google vinculada expirou, obtenha novamente
GOOGLE_CODE_ERROR=C\u00F3digo de verifica\u00E7\u00E3o do Google incorreto
ACCOUNT_IS_ERROR=Falha no login, conta n\u00E3o existe
GOOGLE_SECRET_IS_NOT_BINDING=Chave secreta do Google n\u00E3o vinculada, vincule primeiro
TRADE_CONTRACT_RECORD_NOT_EXISTS=Registro de ordem de contrato n\u00E3o existe
TRADE_CONTRACT_CONFIG_ERROR=Erro na configura\u00E7\u00E3o do contrato
ARG_ORDER_STATUS_IS_EMPTY=Status da ordem n\u00E3o pode estar vazio
ARG_LEVERAGE_IS_EMPTY=Par\u00E2metro de alavancagem n\u00E3o pode estar vazio
ARG_ORDER_TYPE_IS_EMPTY=Par\u00E2metro tipo de ordem n\u00E3o pode estar vazio
ARG_VALUE_ERROR=Erro no valor do par\u00E2metro
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=Ordem de contrato j\u00E1 cancelada
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=Ordem de contrato j\u00E1 executada
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=Contrato j\u00E1 fechado
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Erro na taxa de c\u00E2mbio da moeda de ordem
USER_FORBIDDEN_FUNC=Esta funcionalidade foi desabilitada
IMAGE_URL_ERROR=Erro no endere\u00E7o do link da imagem
USER_WITHDRAW_HAS_SUCCESS=Proibido modificar ordem j\u00E1 bem-sucedida 