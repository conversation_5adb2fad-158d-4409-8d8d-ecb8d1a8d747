package com.rf.exchange.framework.i18n.core;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.*;


@Slf4j
@Getter
@Setter
public class I18nInterceptor implements HandlerInterceptor {

    private I18nLocaleContext i18nLocaleContext;

    private final Map<String, Locale> localeMap = new HashMap<>(8);

    public static final String NAME_OF_LANGUAGE_SETTING = "lang";

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception {
        String lang = getLangFromCustomHeader(request);
        if (!StringUtils.hasText(lang)) {
            lang = getLangFromAcceptLanguageHeader(request);
        }
        if (!StringUtils.hasText(lang)) {
            lang = getLangFromSession(request);
        }
        if (!StringUtils.hasText(lang)) {
            lang = getLangFromCookies(request);
        }
        if (!StringUtils.hasText(lang)) {
            return true;
        }
        try {
            Locale locale = getLocaleByLang(lang);
            i18nLocaleContext.setThreadLocale(locale);
        } catch (Exception e) {
            log.error("无效的语言设置：{}", lang, e);
        }
        return true;
    }


    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, Exception ex) {
        try {
            i18nLocaleContext.clear();
        } catch (Exception e) {
            log.error("清理语言设置时遇到错误：", e);
        }
    }

    /**
     * 获得语言设置
     *
     * @param lang 语言设置
     * @return {@link Locale}
     */
    private Locale getLocaleByLang(String lang) {
        String lan = lang.toLowerCase();
        if ("zh".equalsIgnoreCase(lang)) {
            return Locale.SIMPLIFIED_CHINESE;
        }
        Locale localeMapped = localeMap.get(lan);
        if (localeMapped != null) {
            return localeMapped;
        }
        Locale locale = new Locale.Builder().setLanguageTag(lan).build();
        localeMap.put(lan, locale);
        return locale;
    }

    /**
     * 从 session 中获取 国际化语言
     *
     * @param request
     * @return
     */
    private static String getLangFromSession(HttpServletRequest request) {
        Optional<HttpSession> session = Optional.ofNullable(request.getSession());
        if (session.isPresent()) {
            Object attribute = session.get().getAttribute(NAME_OF_LANGUAGE_SETTING);
            if (attribute instanceof Locale) {
                return ((Locale) attribute).getDisplayName();
            } else if (attribute instanceof String) {
                return (String) attribute;
            }
        }
        return "";
    }

    /**
     * 从 cookie 中获取 国际化语言
     *
     * @param request
     * @return
     */
    private static String getLangFromCookies(HttpServletRequest request) {
        return Optional.ofNullable(request.getCookies())
                .flatMap(cookies -> Arrays.stream(cookies)
                        .filter(cookie -> NAME_OF_LANGUAGE_SETTING.equals(cookie.getName()))
                        .findFirst())
                .map(Cookie::getValue)
                .orElse("");
    }

    /**
     * 从 header 中获取自定义的header 国际化语言
     *
     * @param request
     * @return
     */
    private String getLangFromCustomHeader(HttpServletRequest request) {
        String acceptLanguage = request.getHeader(NAME_OF_LANGUAGE_SETTING);
        return Optional.ofNullable(acceptLanguage)
                .map(lang -> lang.split(","))
                .filter(array -> array.length > 0)
                .map(array -> array[0])
                .orElse("");
    }

    /**
     * 从 header 中获取 国际化语言
     *
     * @param request
     * @return
     */
    private String getLangFromAcceptLanguageHeader(HttpServletRequest request) {
        String acceptLanguage = request.getHeader("Accept-Language");
        return Optional.ofNullable(acceptLanguage)
                .map(lang -> lang.split(","))
                .filter(array -> array.length > 0)
                .map(array -> array[0])
                .orElse("");
    }

}
