package com.rf.exchange.framework.i18n.config;

import com.rf.exchange.framework.i18n.core.I18nFilter;
import com.rf.exchange.framework.i18n.core.I18nHolder;
import com.rf.exchange.framework.i18n.core.I18nInterceptor;
import com.rf.exchange.framework.i18n.core.I18nLocaleContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Locale;

/**
 * i18n国际化自动配置
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Slf4j
@Configuration
public class I18nConfiguration {

    @Bean
    public I18nHolder i18nHolder(MessageSource messageSource, I18nLocaleContext i18nLocaleContext) {
        return new I18nHolder(messageSource, i18nLocaleContext);
    }

    @Bean
    public I18nFilter i18nFilter(I18nLocaleContext i18nLocaleContext) {
        I18nInterceptor interceptor = new I18nInterceptor();
        interceptor.setI18nLocaleContext(i18nLocaleContext);

        I18nFilter i18nFilter = new I18nFilter();
        i18nFilter.setI18nInterceptor(interceptor);
        return i18nFilter;
    }

    @Bean
    public I18nLocaleContext i18nLocaleContext() {
        return new I18nLocaleContext(Locale.ENGLISH);
    }
}
