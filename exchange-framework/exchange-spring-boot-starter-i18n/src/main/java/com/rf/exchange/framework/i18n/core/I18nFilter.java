package com.rf.exchange.framework.i18n.core;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

@Setter
@Getter
@Slf4j
public class I18nFilter implements Filter, Ordered {

    private I18nInterceptor i18nInterceptor;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if (this.httpServlet(servletRequest, servletResponse)) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
            HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;
            try {
                i18nInterceptor.preHandle(httpServletRequest, httpServletResponse, null);
            } catch (Exception e) {
                throw new IOException(e);
            }

            filterChain.doFilter(servletRequest, servletResponse);

            i18nInterceptor.afterCompletion(httpServletRequest, httpServletResponse, null, null);

        } else {
            filterChain.doFilter(servletRequest, servletResponse);
        }
    }

    private boolean httpServlet(ServletRequest servletRequest, ServletResponse servletResponse) {
        return servletRequest instanceof HttpServletRequest && servletResponse instanceof HttpServletResponse;
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}
