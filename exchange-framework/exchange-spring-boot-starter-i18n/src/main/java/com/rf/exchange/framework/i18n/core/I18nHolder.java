package com.rf.exchange.framework.i18n.core;

import org.slf4j.Logger;
import org.springframework.context.MessageSource;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Locale;

import static org.slf4j.LoggerFactory.getLogger;


/**
 * <AUTHOR>
 * @since 2024-12-06
 */
public class I18nHolder {

    /**
     * 记录表中时间戳字段的名称
     */
    public static final String COLUMN_NAME_MODIFIED_TIME = "modified_time";

    @SuppressWarnings("unused")
    private static final Logger log = getLogger(I18nHolder.class);

    private static MessageSource messageResource;
    private static I18nLocaleContext i18nLocaleContext;

    /**
     * 每个消息对应一个 MessageFormat，把MessageFormat保存起来用以格式化
     */
    private static HashMap<String, MessageFormat> messageFormatMap;

    public I18nHolder(MessageSource messageSource, I18nLocaleContext i18nLocaleContext) {
        I18nHolder.messageResource = messageSource;
        I18nHolder.i18nLocaleContext = i18nLocaleContext;
    }

    /**
     * 获取国际化消息
     *
     * @param code   消息代码
     * @param params 消息参数
     * @return 国际化后的消息
     */
    public static String getI18nMessage(String code, Object... params) {
        return getI18nMessage(i18nLocaleContext.getLocale(), code, params);
    }

    /**
     * 获取当前请求的语言
     * @return 语言
     */
    public static Locale getLocale() {
        return i18nLocaleContext.getLocale();
    }

    /**
     * 获取请求的语言
     * zh-cn会返回zh
     * @return 语言代码
     */
    public static String getLanguage() {
        return getLocale().getLanguage();
    }

    public static String getLanguageTag() {
        return getLocale().toLanguageTag();
    }

    /**
     * 指定语言，获取国际化消息
     *
     * @param locale 指定区域
     * @param code   消息代码
     * @param params 消息参数
     * @return 国际化后的消息
     */
    public static String getI18nMessage(Locale locale, String code, Object... params) {
        if (!StringUtils.hasText(code)) {
            return code;
        }
        return messageResource.getMessage(code, params, code, locale);
    }

    ///**
    // * 未启用I18N功能时进行本地处理
    // *
    // * @param code   消息代码
    // * @param params 消息参数
    // * @return 本地处理后的信息
    // */
    //private static String getDummyValue(String code, Object... params) {
    //    if (Objects.isNull(params)) {
    //        return code;
    //    }
    //    initMessageFormatMap();
    //    return getMessageFormat(code).format(params);
    //}

    //private static MessageFormat getMessageFormat(String code) {
    //    if (messageFormatMap.containsKey(code)) {
    //        return messageFormatMap.get(code);
    //    }
    //    MessageFormat mf = new MessageFormat(code);
    //    synchronized (messageFormatMap) {
    //        messageFormatMap.put(code, mf);
    //    }
    //    return mf;
    //}

    ///**
    // * 初始化 messageFormatMap
    // */
    //private static void initMessageFormatMap() {
    //    if (Objects.nonNull(messageFormatMap)) {
    //        return;
    //    }
    //    synchronized (I18nHolder.class) {
    //        if (Objects.nonNull(messageFormatMap)) {
    //            return;
    //        }
    //        messageFormatMap = new HashMap<String, MessageFormat>(1000);
    //    }
    //}
}