package com.rf.exchange.framework.i18n.core;

import com.rf.exchange.framework.i18n.I;
import com.rf.exchange.framework.i18n.annotation.I18n;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Component
public class I18nHelper {

    public static  <T> void getContent(List<T> list, Class<T> tClass) {
             List<Field> fieldList = new ArrayList<>();
        Field[] fields = tClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(I18n.class)) {
                fieldList.add(field);
            }
        }
        for (T t : list) {
            for (Field f : fieldList) {
                try {
                    f.setAccessible(true);
                    Object fieldValue = f.get(t);
                    String content = I.n(fieldValue.toString());
                    if (content != null) {
                        f.set(t, content);
                    }
                    //word.add(fieldValue.toString());
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static <T> void getContent(T t,  Class<T> tClass) {
              readTranslation(t, tClass);
    }

    private static <T> void readTranslation(Object t, Class<T> tClass) {
        Field[] fields = tClass.getDeclaredFields();
        for (Field field : fields) {
            try {
                if (field.isAnnotationPresent(I18n.class)) {
                    if (field.getType() == String.class) {
                        field.setAccessible(true);
                        Object fieldValue = field.get(t);
                        if (fieldValue != null) {
                            String key = (String) fieldValue;
                            String content = I.n(key);
                            if (content != null) {
                                field.set(t, content);
                            }
                        }
                    }
//                    else if (isCustomObject(field.getType()) || field.getType() == List.class || field.getType() == Map.class) {
//                        field.setAccessible(true);
//                        Object fieldValue = field.get(t);
//                        readTranslation(fieldValue, lang, field.getClass());
//                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }


    private static boolean isCustomObject(Class<?> clazz) {
        return !(clazz.isPrimitive() ||
                clazz == String.class ||
                Number.class.isAssignableFrom(clazz) ||
                clazz == Boolean.class ||
                clazz == Character.class ||
                clazz.isEnum() ||
                clazz == Byte.class ||
                clazz == Short.class ||
                clazz == java.util.Date.class ||
                clazz == java.time.LocalDate.class ||
                clazz == java.time.LocalDateTime.class ||
                clazz == java.time.LocalTime.class ||
                clazz == java.time.ZonedDateTime.class ||
                clazz == java.time.OffsetDateTime.class);
    }
}
