package com.rf.exchange.framework.tenant.core.aop;

import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.api.permission.RoleApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;

import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

@Aspect
@Slf4j
public class TenantPermissionsAspect {
    @Resource
    private RoleApi roleApi;


    @Pointcut("execution(* com.rf.exchange.module..controller.admin..*(..))")
    public void setupTenantId() {
    }

    @Before("setupTenantId()")
    public void doBefore() throws Throwable {
        LoginUser loginUser = getLoginUser();
        if (null != loginUser) {
            if (roleApi.hasAnySuperAdminOf(loginUser.getRoleIds())) {
                TenantContextHolder.setIgnore(true);
            }
//            else if (roleApi.hasAnyTenantAdminOf(loginUser.getRoleIds())) {
//                TenantContextHolder.setTenantId(loginUser.getTenantId());
//            }
        }
    }
}
