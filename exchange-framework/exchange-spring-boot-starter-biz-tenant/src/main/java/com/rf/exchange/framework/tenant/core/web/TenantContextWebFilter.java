package com.rf.exchange.framework.tenant.core.web;

import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.tenant.core.service.TenantFrameworkService;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 多租户 Context Web 过滤器
 * 将请求 Header 中的 tenant-id 解析出来，添加到 {@link TenantContextHolder} 中，这样后续的 DB 等操作，可以获得到租户编号。
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class TenantContextWebFilter extends OncePerRequestFilter {

    private final TenantFrameworkService tenantFrameworkService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        // 设置租户id
        Long tenantId = WebFrameworkUtils.getHeaderTenantId(request);
        if (tenantId == null) {
            String clientServername = WebFrameworkUtils.getHeaderClientServername(request);
            tenantId = tenantFrameworkService.parseTenantClientServername(clientServername);
        }
        if (tenantId != null) {
            TenantContextHolder.setTenantId(tenantId);
        }
        try {
            chain.doFilter(request, response);
        } finally {
            // 清理
            TenantContextHolder.clear();
        }
    }

}
