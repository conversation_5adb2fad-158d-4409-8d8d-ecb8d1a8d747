package com.rf.exchange.framework.tenant.core.service;

import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.rf.exchange.framework.common.exception.ServiceException;
import com.rf.exchange.framework.common.util.cache.CacheUtils;
import com.rf.exchange.module.system.api.tenant.TenantApi;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Tenant 框架 Service 实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class TenantFrameworkServiceImpl implements TenantFrameworkService {

    private static final ServiceException SERVICE_EXCEPTION_NULL = new ServiceException();
    private static final Pattern DOMAIN_PATTERN = Pattern.compile("(?:[a-z0-9-]+\\.)?([a-z0-9-]+\\.[a-z]+)$");

    private final TenantApi tenantApi;

    /**
     * 针对 {@link #getTenantIds()} 的缓存
     */
    private final LoadingCache<Object, List<Long>> getTenantIdsCache = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<Object, List<Long>>() {

                @Override
                public List<Long> load(Object key) {
                    return tenantApi.getTenantIdList();
                }

            });

    /**
     * 针对 {@link #validTenant(Long)} 的缓存
     */
    private final LoadingCache<Long, ServiceException> validTenantCache = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<Long, ServiceException>() {

                @Override
                public ServiceException load(Long id) {
                    try {
                        tenantApi.validateTenant(id);
                        return SERVICE_EXCEPTION_NULL;
                    } catch (ServiceException ex) {
                        return ex;
                    }
                }

            });

    private final LoadingCache<Object, Map<String, Long>> getTenantServernameCache = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<Object, Map<String, Long>>() {
                @Override
                public Map<String, Long> load(Object key) {
                    return tenantApi.getTenantIdServerNameMap();
                }
            });

    @Override
    @SneakyThrows
    public List<Long> getTenantIds() {
        return getTenantIdsCache.get(Boolean.TRUE);
    }

    @Override
    public void validTenant(Long id) {
        ServiceException serviceException = validTenantCache.getUnchecked(id);
        if (serviceException != SERVICE_EXCEPTION_NULL) {
            throw serviceException;
        }
    }

    @Override
    public Long parseTenantClientServername(String servername) {
        try {
            Map<String, Long> servernameIdMap = getTenantServernameCache.get(Boolean.TRUE);
            for (Map.Entry<String, Long> entry : servernameIdMap.entrySet()) {
                // 配置的租户的域名
                final String configDomain = entry.getKey();
                // 如果请求的域名和配置的域名两者相匹配则返回租户id
                if (configDomain.equalsIgnoreCase(servername)) {
                    return entry.getValue();
                }
                // 或者请求域名的主域名和配置的主域名相匹配也返回租户id
                final String mainDomain = getMainDomain(servername);
                if (mainDomain != null && mainDomain.equalsIgnoreCase(configDomain)) {
                    return entry.getValue();
                }
            }
        } catch (ExecutionException ignored) {
        }
        return null;
    }

    private static String getMainDomain(String servername) {
        Matcher matcher = DOMAIN_PATTERN.matcher(servername);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
