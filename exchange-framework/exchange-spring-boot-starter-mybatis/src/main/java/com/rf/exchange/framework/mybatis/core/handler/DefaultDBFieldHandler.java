package com.rf.exchange.framework.mybatis.core.handler;

import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDOFields;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 通用参数填充实现类
 * <p>
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.nonNull(metaObject)) {
            if (metaObject.getOriginalObject() instanceof BaseDOFields<?> baseFields) {
                Integer userType = WebFrameworkUtils.getLoginUserType();
                Long loginUserId = WebFrameworkUtils.getLoginUserId();
                String username = WebFrameworkUtils.getLoginUsername();
                if (UserTypeEnum.ADMIN.getValue().equals(userType)) {
                    username = WebFrameworkUtils.getLoginNickname();
                } else if (!StringUtils.hasText(username) && loginUserId != null) {
                    username = loginUserId.toString();
                }
                // 创建时间为空，则以当前时间为插入时间
                if (Objects.isNull(baseFields.getCreateTime())) {
                    baseFields.setCreateTime(DateUtils.getUnixTimestampNow());
                }
                // 更新时间为空，则以当前时间为更新时间
                if (Objects.isNull(baseFields.getUpdateTime())) {
                    baseFields.setUpdateTime(DateUtils.getUnixTimestampNow());
                }
                // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
                if (StringUtils.hasText(username) && !StringUtils.hasText(baseFields.getCreator())) {
                    baseFields.setCreator(username);
                } else if (!StringUtils.hasText(baseFields.getCreator())) {
                    baseFields.setCreator("default");
                }
                // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
                if (StringUtils.hasText(username) && !StringUtils.hasText(baseFields.getUpdater())) {
                    baseFields.setUpdater(username);
                } else if (!StringUtils.hasText(baseFields.getUpdater())) {
                    baseFields.setUpdater("default");
                }
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        Object modifyTime = getFieldValByName("updateTime", metaObject);
        if (Objects.isNull(modifyTime)) {
            setFieldValByName("updateTime",DateUtils.getUnixTimestampNow() , metaObject);
        }
        Integer userType = WebFrameworkUtils.getLoginUserType();
        String username = WebFrameworkUtils.getLoginUsername();
        if (UserTypeEnum.ADMIN.getValue().equals(userType)) {
            username = WebFrameworkUtils.getLoginNickname();
        }
        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        Object modifier = getFieldValByName("updater", metaObject);
        if (StringUtils.hasText(username) && Objects.isNull(modifier)) {
            setFieldValByName("updater", username, metaObject);
        }
    }
}
