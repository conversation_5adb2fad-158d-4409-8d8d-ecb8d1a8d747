package com.rf.exchange.framework;

import com.rf.exchange.framework.common.util.encryption.AesUtil;
import com.rf.exchange.framework.datasource.config.MyBeanPostProcessor;

public class EncryptionUtil {

    public static void main(String... args) {
        String url="127.0.0.1:3306";
        String dbname="exchange-dev";
        String user="exchange";
        String pwd="123456";
        System.out.println(AesUtil.encrypt(url, MyBeanPostProcessor.key));
        System.out.println(AesUtil.encrypt(dbname, MyBeanPostProcessor.key));
        System.out.println(AesUtil.encrypt(user, MyBeanPostProcessor.key));
        System.out.println(AesUtil.encrypt(pwd, MyBeanPostProcessor.key));
    }
}
