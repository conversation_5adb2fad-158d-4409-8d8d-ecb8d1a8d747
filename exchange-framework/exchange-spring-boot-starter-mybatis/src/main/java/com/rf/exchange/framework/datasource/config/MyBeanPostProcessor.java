package com.rf.exchange.framework.datasource.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.ds.ItemDataSource;
import com.rf.exchange.framework.common.util.encryption.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.util.Map;

@Slf4j
public class MyBeanPostProcessor implements BeanPostProcessor {
    public static final String key = "comexhavemany%^#";//16位

    private boolean isLocal = false;
    private boolean isDev = false;

    public MyBeanPostProcessor(Environment environment) {
        final String[] activeProfiles = environment.getActiveProfiles();
        for (String activeProfile : activeProfiles) {
            if (activeProfile.contentEquals("local")) {
                isLocal = true;
            }
            if (activeProfile.contentEquals("dev")) {
                isDev = true;
            }
        }
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (isLocal || isDev) {
            return bean;
        }
        if (bean instanceof DynamicRoutingDataSource) {
            DynamicRoutingDataSource dataSource = (DynamicRoutingDataSource) bean;
            Map<String, DataSource> dataSourceList = dataSource.getDataSources();
            for (Map.Entry<String, DataSource> item : dataSourceList.entrySet()) {
                ItemDataSource ids = (ItemDataSource) item.getValue();
                DruidDataSource dds = (DruidDataSource) ids.getRealDataSource();
                String encryptedPassword = dds.getPassword();
                String decryptedPassword = AesUtil.decrypt(encryptedPassword, key);
                dds.setPassword(decryptedPassword);
//                log.info("url----------------------------"+dds.getUrl());
//                log.info("加密的密码是---------------------"+encryptedPassword);
//                log.info("解的密码是---------------------"+decryptedPassword);
            }
        }
        return bean;//BeanPostProcessor.super.postProcessAfterInitialization(bean, beanName);
    }
}
