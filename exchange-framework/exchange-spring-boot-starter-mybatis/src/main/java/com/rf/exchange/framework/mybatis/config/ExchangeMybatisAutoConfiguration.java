package com.rf.exchange.framework.mybatis.config;

import com.rf.exchange.framework.mybatis.core.handler.DefaultDBFieldHandler;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * MyBaits 配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(before = MybatisPlusAutoConfiguration.class) // 目的：先于 MyBatis Plus 自动配置，避免 @MapperScan 可能扫描不到 Mapper 打印 warn 日志
@MapperScan(value = "${exchange.info.base-package}", annotationClass = Mapper.class,
        lazyInitialization = "${mybatis.lazy-initialization:false}") // Mapper 懒加载，目前仅用于单元测试
public class ExchangeMybatisAutoConfiguration {
//    @Resource
//    private ExchangeTranslationInterceptor exchangeTranslationInterceptor;
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor()); // 分页插件
        return mybatisPlusInterceptor;
    }

    @Bean
    public MetaObjectHandler defaultMetaObjectHandler(){
        return new DefaultDBFieldHandler(); // 自动填充参数类
    }

    //@Bean
    //@ConditionalOnProperty(prefix = "mybatis-plus.global-config.db-config", name = "id-type", havingValue = "INPUT")
    //public IKeyGenerator keyGenerator(ConfigurableEnvironment environment) {
    //    DbType dbType = IdTypeEnvironmentPostProcessor.getDbType(environment);
    //    if (dbType != null) {
    //        switch (dbType) {
    //            case POSTGRE_SQL:
    //                return new PostgreKeyGenerator();
    //            case ORACLE:
    //            case ORACLE_12C:
    //                return new OracleKeyGenerator();
    //            case H2:
    //                return new H2KeyGenerator();
    //            case KINGBASE_ES:
    //                return new KingbaseKeyGenerator();
    //            case DM:
    //                return new DmKeyGenerator();
    //        }
    //    }
    //    // 找不到合适的 IKeyGenerator 实现类
    //    throw new IllegalArgumentException(StrUtil.format("DbType{} 找不到合适的 IKeyGenerator 实现类", dbType));
    //}

}
