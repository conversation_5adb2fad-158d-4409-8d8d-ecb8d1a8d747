package com.rf.exchange.framework.mybatis.core.dataobject;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-06-25
 */
public interface BaseDOFields<T extends BaseDOFields<T>>  {

    Long getCreateTime();
    T setCreateTime(Long createTime);

    Long getUpdateTime();
    T setUpdateTime(Long updateTime);

    String getCreator();
    T setCreator(String creator);

    String getUpdater();
    T setUpdater(String updater);
}
