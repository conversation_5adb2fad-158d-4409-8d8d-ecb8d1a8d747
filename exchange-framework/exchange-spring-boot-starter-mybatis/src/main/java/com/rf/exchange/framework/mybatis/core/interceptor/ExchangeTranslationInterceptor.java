//package com.rf.exchange.framework.mybatis.core.interceptor;
//
//import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
//import com.rf.exchange.framework.common.locate.Translatable;
//import com.rf.exchange.framework.mybatis.config.ExchangeMybatisAutoConfiguration;
//import com.rf.exchange.framework.translate.core.TranslationService;
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletRequest;
//import org.apache.ibatis.executor.resultset.ResultSetHandler;
//import org.apache.ibatis.executor.statement.StatementHandler;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.plugin.*;
//import org.springframework.boot.autoconfigure.AutoConfigureBefore;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import java.lang.reflect.Field;
//import java.sql.Statement;
//import java.util.List;
//import java.util.Properties;
//
//@Intercepts({
//        @Signature(type = StatementHandler.class, method = "select", args = {Statement.class}),
//        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
//})
//@Component
//public class ExchangeTranslationInterceptor implements Interceptor,InnerInterceptor  {
//    @Resource
//    private TranslationService translationService;
//    @Resource
//    private HttpServletRequest httpServletRequest;
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        String lang=httpServletRequest.getHeader("lang");
//        if(!StringUtils.hasText(lang)){
//            lang="en";
//        }
//        if (invocation.getTarget() instanceof StatementHandler) {
//            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
//            // 使用反射获取 MappedStatement
//            MappedStatement mappedStatement = (MappedStatement) getFieldValue(statementHandler, "delegate.mappedStatement");
//            String sqlId = mappedStatement.getId();
//
//            // 读取时
//            if (sqlId.matches(".*select.*")) {
//                Object parameterObject = statementHandler.getParameterHandler().getParameterObject();
//                //FIXME 这里应该要使用I18nInterceptor的NAME_OF_LANGUAGE_SETTING
//
//                translateFields(parameterObject, lang); // 假设这里使用中文（zh）为例
//            }
//        } else if (invocation.getTarget() instanceof ResultSetHandler) {
//            ResultSetHandler resultSetHandler = (ResultSetHandler) invocation.getTarget();
//            List<Object> results = (List<Object>) invocation.proceed();
//            for (Object result : results) {
//                translateFields(result, lang); // 从请求头中获取语言
//            }
//            return results;
//        }
//        return invocation.proceed();
//    }
//
//    private void translateFields(Object entity, String lang) {
//        if (entity == null) return;
//
//        Field[] fields = entity.getClass().getDeclaredFields();
//        for (Field field : fields) {
//            try {
//                if (field.isAnnotationPresent(Translatable.class) && field.getType() == String.class) {
//                    field.setAccessible(true);
//                    Object fieldValue = field.get(entity);
//                    if (fieldValue != null) {
//                        String key = (String) fieldValue;
//                        //FIXME 考虑使用 Redis 缓存
//                        String content = translationService.getContentByLang(key, lang);
//                        field.set(entity, content);
//                    }
//                }
//            } catch (IllegalAccessException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//    }
//
//    private Object getFieldValue(Object target, String fieldName) throws Exception {
//        Field field = findField(target.getClass(), fieldName);
//        field.setAccessible(true);
//        return field.get(target);
//    }
//
//    private Field findField(Class<?> clazz, String name) throws NoSuchFieldException {
//        while (clazz != null) {
//            try {
//                return clazz.getDeclaredField(name);
//            } catch (NoSuchFieldException e) {
//                clazz = clazz.getSuperclass();
//            }
//        }
//        throw new NoSuchFieldException("Field " + name + " not found in class hierarchy");
//    }
//}