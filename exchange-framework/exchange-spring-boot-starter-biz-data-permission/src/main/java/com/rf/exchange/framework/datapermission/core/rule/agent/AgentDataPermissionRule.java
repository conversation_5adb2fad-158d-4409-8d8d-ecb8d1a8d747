package com.rf.exchange.framework.datapermission.core.rule.agent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.common.util.collection.CollectionUtils;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.datapermission.core.rule.DataPermissionRule;
import com.rf.exchange.framework.mybatis.core.util.MyBatisUtils;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import com.rf.exchange.module.system.api.permission.PermissionApi;
import com.rf.exchange.module.system.api.permission.dto.AgentDataPermissionRespDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.NullValue;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-07-30
 */
@Slf4j
@AllArgsConstructor
public class AgentDataPermissionRule implements DataPermissionRule {

    /**
     * LoginUser 的 Context 缓存 Key
     */
    public static final String CONTEXT_KEY = AgentDataPermissionRule.class.getSimpleName();
    /**
     * LoginUser 的 Context 更新时间(单位毫秒)
     */
    public static final String CONTEXT_UPDATE_TIME = CONTEXT_KEY + "-updateTime";

    /**
     * 这里指定 user_id 因为查询的时候使用的是 in user_id
     */
    public static final String USER_ID_COLUMN_NAME = "user_id";

    public static final String AGENT_ID_COLUMN_NAME = "agent_id";

    static final Expression EXPRESSION_NULL = new NullValue();

    private final PermissionApi permissionApi;

    /**
     * 使用agent_id作为查找条件表
     * <p>
     * key: 表名
     * value: 字段名
     */
    private final Set<String> agentIdTables = new HashSet<>();

    /**
     * 使用user_id作为查找条件的表
     */
    private final Set<String> userIdTables = new HashSet<>();

    /**
     * 所有表名 {@link #agentIdTables} 的合集
     */
    private final Set<String> TABLE_NAMES = new HashSet<>();

    /**
     * 是否跳过规则
     * true: 调整这个规则
     */
    private final ThreadLocal<Boolean> SKIP_RULE = TransmittableThreadLocal.withInitial(() -> false);

    @Override
    public Set<String> getTableNames() {
        return TABLE_NAMES;
    }

    @Override
    public Expression getExpression(String tableName, Alias tableAlias) {
        // 是否跳过这条规则
        Boolean isSkip = SKIP_RULE.get();
        if (isSkip != null && isSkip) {
            return null;
        }

        // 只有有登陆用户的情况下，才进行代理的数据权限的处理
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return null;
        }
        // 只有管理员类型的用户，才进行代理的数据权限的处理
        if (ObjectUtil.notEqual(loginUser.getUserType(), UserTypeEnum.ADMIN.getValue())) {
            return null;
        }
        // roleId为1表示超级管理员
        if (CollectionUtil.isNotEmpty(loginUser.getRoleIds()) && loginUser.getRoleIds().contains(1L)) {
            return null;
        }
        // 如果当前登录用户的代理id为null也不处理
        if (loginUser.getAgentId() == null) {
            return null;
        }
        // 如果是使用agent_id作为数据判断的依据则直接构建，否则使用user_id作为条件构建数据
        if (agentIdTables.contains(tableName)) {
            return buildAgentIdExpression(tableName, tableAlias, loginUser.getAgentId(), loginUser.getDescendantAgentIds());
        } else {
            long now = System.currentTimeMillis();
            // 获取当前登录的代理账号下面的所有用户id
            AgentDataPermissionRespDTO agentDataPermission = loginUser.getContext(CONTEXT_KEY, AgentDataPermissionRespDTO.class);
            // 缓存的上下文是否过期
            boolean isContextExpire = true;
            final Long updateTime = loginUser.getContext(CONTEXT_UPDATE_TIME, Long.class);
            if (updateTime != null) {
                // 缓存时间是否超过了1分钟
                isContextExpire = now > (updateTime + 60000);
            }
            // 从上下文中拿不到，则调用逻辑进行获取
            if (agentDataPermission == null || isContextExpire) {

                SKIP_RULE.set(Boolean.TRUE);
                agentDataPermission = permissionApi.getAgentDataPermission(loginUser.getAgentId(), loginUser.getDescendantAgentIds());
                SKIP_RULE.remove();

                if (agentDataPermission == null) {
                    log.error("[getExpression][LoginUser({}) 获取数据权限为 null]", JsonUtils.toJsonString(loginUser));
                    throw new NullPointerException(String.format("LoginUser(%d) Table(%s/%s) 未返回数据权限",
                            loginUser.getId(), tableName, tableAlias.getName()));
                }
                // 添加到上下文中，避免重复计算
                loginUser.setContext(CONTEXT_KEY, agentDataPermission);
                loginUser.setContext(CONTEXT_UPDATE_TIME, now);
            }

            if (agentDataPermission.getUserIds().isEmpty()) {
                return new EqualsTo(null, null); // WHERE null = null 保证返回的数据为空
            }

            // 获取代理数据的查询语句
            Expression agentIdExpression = buildAgentIdExpression(tableName, tableAlias, loginUser.getAgentId(), loginUser.getDescendantAgentIds());
            Expression userIdExpression = buildUserIdExpression(tableName, tableAlias, agentDataPermission.getUserIds());
            if (userIdExpression == null && agentIdExpression == null) {
                // TODO: 获得不到条件的时候，暂时不抛出异常，而是不返回数据
                log.warn("[getExpression][LoginUser({}) Table({}/{}) AgentDataPermission({}) 构建的条件为空]",
                        JsonUtils.toJsonString(loginUser), tableName, tableAlias, JsonUtils.toJsonString(agentDataPermission));
                return EXPRESSION_NULL;
            }
            if (userIdExpression == null) {
                return agentIdExpression;
            }
            if (agentIdExpression == null) {
                return userIdExpression;
            }
            return new EqualsTo(null, null);
        }
    }

    private Expression buildUserIdExpression(String tableName, Alias tableAlias, Set<Long> userIds) {
        if (StrUtil.isEmpty(tableName) || !userIdTables.contains(tableName)) {
            return null;
        }
        // 如果后面这里使用 in 语句造成性能问题再改造这里的 SQL语句
        return new InExpression(MyBatisUtils.buildColumn(tableName, tableAlias, USER_ID_COLUMN_NAME),
                new ExpressionList(CollectionUtils.convertList(userIds, LongValue::new)));
    }

    private Expression buildAgentIdExpression(String tableName, Alias tableAlias, Long agentId, Collection<Long> descendantAgentIds) {
        if (StrUtil.isEmpty(tableName) || !agentIdTables.contains(tableName)) {
            return null;
        }
        if (CollectionUtil.isNotEmpty(descendantAgentIds)) {
            Set<Long> allAgentIds = new HashSet<>();
            allAgentIds.add(agentId);
            allAgentIds.addAll(descendantAgentIds);
            return new InExpression(MyBatisUtils.buildColumn(tableName, tableAlias, AGENT_ID_COLUMN_NAME),
                    new ExpressionList(CollectionUtils.convertList(allAgentIds, LongValue::new)));
        } else {

            return new EqualsTo(MyBatisUtils.buildColumn(tableName, tableAlias, AGENT_ID_COLUMN_NAME), new LongValue(agentId));
        }
    }

    // ==================== 添加配置 ====================

    public void addMatchAgentIdTable(String tableName) {
        agentIdTables.add(tableName);
        TABLE_NAMES.add(tableName);
    }

    public void addMatchUserIdTable(String tableName) {
        userIdTables.add(tableName);
        TABLE_NAMES.add(tableName);
    }
}
