package com.rf.exchange.framework.datapermission.config;

import com.rf.exchange.framework.datapermission.core.rule.agent.AgentDataPermissionRule;
import com.rf.exchange.framework.datapermission.core.rule.agent.AgentDataPermissionRuleCustomizer;
import com.rf.exchange.framework.datapermission.core.rule.dept.DeptDataPermissionRule;
import com.rf.exchange.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.system.api.permission.PermissionApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;

import java.util.List;

/**
 * 基于部门的数据权限 AutoConfiguration
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass(LoginUser.class)
@ConditionalOnBean(value = {PermissionApi.class, DeptDataPermissionRuleCustomizer.class})
public class ExchangeDeptDataPermissionAutoConfiguration {

    @Bean
    public DeptDataPermissionRule deptDataPermissionRule(PermissionApi permissionApi,
                                                         List<DeptDataPermissionRuleCustomizer> customizers) {
        // 创建 DeptDataPermissionRule 对象
        DeptDataPermissionRule rule = new DeptDataPermissionRule(permissionApi);
        // 补全表配置
        customizers.forEach(customizer -> customizer.customize(rule));
        return rule;
    }

    @Bean
    public AgentDataPermissionRule agentDataPermissionRule(PermissionApi permissionApi,
                                                           List<AgentDataPermissionRuleCustomizer> customizers) {
        // 创建 AgentDataPermissionRule 对象
        AgentDataPermissionRule rule = new AgentDataPermissionRule(permissionApi);
        customizers.forEach(customizer -> customizer.customize(rule));
        return rule;
    }

}
