<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>exchange-framework</artifactId>
        <groupId>com.rf.dev</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>exchange-spring-boot-starter-biz-data-permission</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>数据权限</description>


    <dependencies>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-common</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-security</artifactId>
            <optional>true</optional> <!-- 可选，如果使用 DeptDataPermissionRule 必须提供 -->
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-system-api</artifactId> <!-- 需要使用它，进行数据权限的获取 -->
            <version>${revision}</version>
        </dependency>

    </dependencies>

</project>
