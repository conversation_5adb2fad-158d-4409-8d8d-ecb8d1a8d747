package com.rf.exchange.framework.common.exception;

import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.common.exception.enums.ServiceErrorCodeRange;
import com.rf.exchange.framework.i18n.I;
import lombok.Data;

/**
 * 错误码对象
 * <p>
 * 全局错误码，占用 [0, 999], 参见 {@link GlobalErrorCodeConstants}
 * 业务异常错误码，占用 [1 000 000 000, +∞)，参见 {@link ServiceErrorCodeRange}
 */
@Data
public class ErrorCode {

    /**
     * 错误码
     */
    private final Integer code;
    /**
     * 错误提示
     */
    private final String msg;
    /**
     * 国际化的code
     */
    private final String i18nCode;

    public ErrorCode(Integer code, String msg, String msgCode) {
        this.code = code;
        this.msg = msg;
        this.i18nCode = msgCode;
    }

    public String getI18nMessage() {
        return I.n(this.i18nCode);
    }
}
