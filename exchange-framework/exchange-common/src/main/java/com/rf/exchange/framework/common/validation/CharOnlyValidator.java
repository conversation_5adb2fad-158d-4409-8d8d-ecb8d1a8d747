package com.rf.exchange.framework.common.validation;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.validation.ValidationUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @since 2024-06-25
 */
public class CharOnlyValidator implements ConstraintValidator<CharOnly, String> {

    @Override
    public void initialize(CharOnly annotation) {
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        // 如果内容为空，默认不校验，即校验通过
        if (StrUtil.isEmpty(s)) {
            return true;
        }
        // 校验内容
        return ValidationUtils.isChar(s);
    }
}
