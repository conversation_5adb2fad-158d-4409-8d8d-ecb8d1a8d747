package com.rf.exchange.framework.common.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-08-17
 */
public class InListStringValueValidator implements ConstraintValidator<InList, String> {

    private final Set<String> validStrings = new HashSet<>();

    @Override
    public void initialize(InList constraintAnnotation) {
        final String[] strings = constraintAnnotation.strValues();
        validStrings.addAll(Arrays.asList(strings));
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        return validStrings.contains(s);
    }
}
