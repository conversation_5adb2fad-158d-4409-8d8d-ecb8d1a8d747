package com.rf.exchange.framework.common.validation;

//create by wskiki on 2024/6/20

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Target({
        ElementType.METHOD,
        ElementType.FIELD,
        ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR,
        ElementType.PARAMETER,
        ElementType.TYPE_USE
})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = CharAndNumberValidator.class
)
public @interface CharAndNumber {

    String message() default "账号格式不正确 ，必须是英文字母(+数字)";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
