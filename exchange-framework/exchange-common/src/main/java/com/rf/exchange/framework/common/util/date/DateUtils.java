package com.rf.exchange.framework.common.util.date;

import cn.hutool.core.date.LocalDateTimeUtil;

import java.time.*;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils {

    /**
     * 时区 - 默认
     */
    public static final String TIME_ZONE_DEFAULT = "UTC+8";

    /**
     * 秒转换成毫秒
     */
    public static final long SECOND_MILLIS = 1000;

    public static final String FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd";

    public static final String FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     * 将 LocalDateTime 转换成 Date
     *
     * @param date LocalDateTime
     * @return LocalDateTime
     */
    public static Date of(LocalDateTime date) {
        if (date == null) {
            return null;
        }
        // 将此日期时间与时区相结合以创建 ZonedDateTime
        ZonedDateTime zonedDateTime = date.atZone(ZoneId.systemDefault());
        // 本地时间线 LocalDateTime 到即时时间线 Instant 时间戳
        Instant instant = zonedDateTime.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return Date.from(instant);
    }

    /**
     * 将 Date 转换成 LocalDateTime
     *
     * @param date Date
     * @return LocalDateTime
     */
    public static LocalDateTime of(Date date) {
        if (date == null) {
            return null;
        }
        // 转为时间戳
        Instant instant = date.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static Date addTime(Duration duration) {
        return new Date(System.currentTimeMillis() + duration.toMillis());
    }

    public static boolean isExpired(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(time);
    }

    /**
     * 是否过期
     *
     * @param time 时间（毫秒单位)
     */
    public static boolean isExpired(Long time) {
        return DateUtils.getUnixTimestampNow() > time;
    }

    /**
     * 创建指定时间
     *
     * @param year  年
     * @param mouth 月
     * @param day   日
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day) {
        return buildTime(year, mouth, day, 0, 0, 0);
    }

    /**
     * 创建指定时间
     *
     * @param year   年
     * @param mouth  月
     * @param day    日
     * @param hour   小时
     * @param minute 分钟
     * @param second 秒
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day,
                                 int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, mouth - 1);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0); // 一般情况下，都是 0 毫秒
        return calendar.getTime();
    }

    public static Date max(Date a, Date b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.compareTo(b) > 0 ? a : b;
    }

    public static LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.isAfter(b) ? a : b;
    }

    /**
     * 是否今天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isToday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now());
    }

    public static boolean isToday(Long unixTimestampInMillis) {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 将Unix时间戳转换为LocalDate
        LocalDate date = Instant.ofEpochMilli(unixTimestampInMillis)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 比较两个日期是否相同
        return today.equals(date);
    }

    /**
     * 是否昨天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isYesterday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now().minusDays(1));
    }

    /**
     * 获取unix当前时间戳，毫秒单位
     *
     * @return 毫秒时间戳（13位）
     */
    public static long getUnixTimestampNow() {
        return System.currentTimeMillis();//如果要用秒为单位 就 / 1000
    }

    /**
     * 获取昨天的开始时间，这个是以服务器所在时间为基础计算的昨天的开始时间，可能在用户那端
     *
     * @return
     */
    public static long getStartOfYesterday() {
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate yesterday = LocalDate.now(zoneId).minusDays(1);
        LocalDateTime startOfYesterday = yesterday.atStartOfDay();
        ZonedDateTime zonedDateTime = startOfYesterday.atZone(zoneId);
        return zonedDateTime.toInstant().toEpochMilli();
    }

    /**
     * 获取昨天的结束时间
     *
     * @return
     */
    public static long getEndOfYesterday() {
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate yesterday = LocalDate.now(zoneId).minusDays(1);
        LocalDateTime endOfYesterday = yesterday.atTime(23, 59, 59, 999999999);
        ZonedDateTime zonedDateTime = endOfYesterday.atZone(zoneId);
        return zonedDateTime.toInstant().toEpochMilli();
    }

    public static long getTodayStart() {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 将当前日期的开始时间转换为 ZonedDateTime
        ZonedDateTime startOfDay = today.atStartOfDay(ZoneId.systemDefault());

        // 将 ZonedDateTime 转换为 Unix 时间戳（以毫秒为单位）
        long startOfDayTimestamp = startOfDay.toInstant().toEpochMilli();

        // 输出结果
        System.out.println("今天开始的时间戳（毫秒单位）： " + startOfDayTimestamp);
        return startOfDayTimestamp;
    }


    /**
     * 获取表示今日的时间戳
     *
     * @return 秒时间戳(10位)
     */
    public static long getTodayTimestamp() {
        return Instant.now().getEpochSecond();
    }


    /**
     * 获取今天的时间戳，秒为单位
     *
     * @return
     */
    public static Long getTodayStartTimeSecondsUtc() {
        // 获取当前时区的当前时间
        LocalDateTime localNow = LocalDateTime.now();

        // 将当前时间转换为 UTC 开始时间 (00:00:00 UTC)
        LocalDateTime utcStartOfDay = localNow.withHour(0).withMinute(0).withSecond(0).withNano(0);

        // 转换为 UTC 时间的 ZonedDateTime
        ZonedDateTime utcZonedDateTime = utcStartOfDay.atZone(ZoneOffset.UTC);
        return utcZonedDateTime.toInstant().toEpochMilli() / 1000;
    }


    /**
     * 获取今天的时间戳，秒为单位
     *
     * @return
     */
    public static Long getAddDayStartTimeSecondsUtc(int days) {
        // 获取当前时区的当前时间
        LocalDateTime localNow = LocalDateTime.now();
        localNow = localNow.plusDays(days);
        // 将当前时间转换为 UTC 开始时间 (00:00:00 UTC)
        LocalDateTime utcStartOfDay = localNow.withHour(0).withMinute(0).withSecond(0).withNano(0);

        // 转换为 UTC 时间的 ZonedDateTime
        ZonedDateTime utcZonedDateTime = utcStartOfDay.atZone(ZoneOffset.UTC);
        return utcZonedDateTime.toInstant().toEpochMilli() / 1000;
    }

    /**
     * 获取本周1的开始时间
     *
     * @return
     */
    public static Long getWeekStartTime() {
        // 获取当前日期时间
        LocalDateTime now = LocalDateTime.now();

        // 获取本周一的日期
        LocalDate today = now.toLocalDate();
        LocalDate monday = today.with(DayOfWeek.MONDAY);

        // 将本周一的时间设置为00:00:00
        LocalDateTime startOfMonday = monday.atStartOfDay();

        // 将时间转换为UTC
        ZonedDateTime utcTime = startOfMonday.atZone(ZoneOffset.UTC);

        // 获取秒为单位的时间戳
        long timestampInSeconds = utcTime.toEpochSecond();
        return timestampInSeconds;
    }

    // 函数：传入一个时间戳（秒为单位），返回该时间戳所在周的周一早上8点（UTC）的时间戳
    public static long getWeekStartTime(long timestamp) {
        // 将时间戳转换为 LocalDateTime 对象 (UTC)
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, ZoneOffset.UTC);

        // 调整到本周的周一
        LocalDateTime monday = dateTime.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 设置时间为早上 8 点
        monday = monday.withHour(0).withMinute(0).withSecond(0).with(ChronoField.NANO_OF_SECOND, 0);

        // 将 LocalDateTime 转换为时间戳 (UTC)
        return monday.toEpochSecond(ZoneOffset.UTC);
    }

    /**
     * 获取今天的时间戳，秒为单位
     *
     * @return
     */
    public static Long getAddDayStartTimeSecondsUtc(long startTime, int days) {
        LocalDateTime localNow = Instant.ofEpochMilli(startTime)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        localNow = localNow.plusDays(days);
        // 将当前时间转换为 UTC 开始时间 (00:00:00 UTC)
        LocalDateTime utcStartOfDay = localNow.withHour(0).withMinute(0).withSecond(0).withNano(0);

        // 转换为 UTC 时间的 ZonedDateTime
        ZonedDateTime utcZonedDateTime = utcStartOfDay.atZone(ZoneOffset.UTC);
        return utcZonedDateTime.toInstant().toEpochMilli() / 1000;
    }

    public static void main(String... args) {
        final LocalDateTime now = LocalDateTime.now().plusMinutes(1);
        System.out.println(LocalDateTimeUtil.beginOfDay(now));
        System.out.println(getWeekStartTime());
    }
}
