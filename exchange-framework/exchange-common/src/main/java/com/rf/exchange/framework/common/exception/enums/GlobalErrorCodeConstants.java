package com.rf.exchange.framework.common.exception.enums;

import com.rf.exchange.framework.common.exception.ErrorCode;

/**
 * 全局错误码枚举
 * 0-999 系统异常编码保留
 * <p>
 * 一般情况下，使用 HTTP 响应状态码 https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Status
 * 虽然说，HTTP 响应状态码作为业务使用表达能力偏弱，但是使用在系统层面还是非常不错的
 * 比较特殊的是，因为之前一直使用 0 作为成功，就不使用 200 啦。
 *
 * <AUTHOR>
 */
public interface GlobalErrorCodeConstants {

    ErrorCode SUCCESS = new ErrorCode(0, "成功","SUCCESS");

    ErrorCode FAIL = new ErrorCode(1, "失败","FAIL");

    ErrorCode BAD_REQUEST = new ErrorCode(400, "错误的请求","BAD_REQUEST");

    ErrorCode UNKNOW_AUTHORIZED = new ErrorCode(400, "未知授权类型[{}]","UNKNOW_AUTHORIZED");

    ErrorCode TOKEN_NOT_SUPPORT_MODE= new ErrorCode(400, "Token 接口不支持 [{}] 授权模式","TOKEN_NOT_SUPPORT_MODE");
    ErrorCode CLIENT_ERROR= new ErrorCode(400, "client_id 或 client_secret 未正确传递","CLIENT_ERROR");
    ErrorCode TOKEN_REFRESH_INVALID= new ErrorCode(400, "无效的刷新令牌","TOKEN_REFRESH_INVALID");
    ErrorCode TOKEN_REFRESH_CLIENT_ERROR= new ErrorCode(400, "刷新令牌的客户端编号不正确","TOKEN_REFRESH_CLIENT_ERROR");
    ErrorCode TOKEN_REFRESH_EXPIRE= new ErrorCode(400, "刷新令牌已过期","TOKEN_REFRESH_EXPIRE");

    ErrorCode TOKEN_NOT_EXISTS= new ErrorCode(400, "访问令牌不存在","TOKEN_NOT_EXISTS");
    ErrorCode TOKEN_EXPIRE= new ErrorCode(400, "访问令牌已过期","TOKEN_EXPIRE");
    ErrorCode GRANT_RESPONSE_TYPE_ERROR= new ErrorCode(400, "response_type 参数值只允许 code 和 token","GRANT_RESPONSE_TYPE_ERROR");

    ErrorCode UNAUTHORIZED = new ErrorCode(401,"账号未登录","UNAUTHORIZED");
    ErrorCode FORBIDDEN = new ErrorCode(403, "没有该操作权限","FORBIDDEN");
    ErrorCode NOT_FOUND = new ErrorCode(404, "请求的资源不存在","NOT_FOUND");
    ErrorCode METHOD_NOT_ALLOWED = new ErrorCode(405,"请求方法不允许","METHOD_NOT_ALLOWED");
    ErrorCode LOCKED = new ErrorCode(423, "锁定中","LOCKED");// 并发请求，不允许
    ErrorCode TOO_MANY_REQUESTS = new ErrorCode(429,"请求过于频繁，请稍后重试","TOO_MANY_REQUESTS");

    // ========== 服务端错误段 ==========

    ErrorCode INTERNAL_SERVER_ERROR = new ErrorCode(500, "系统异常","INTERNAL_SERVER_ERROR");
    ErrorCode NOT_IMPLEMENTED = new ErrorCode(501, "功能未实现/未开启","NOT_IMPLEMENTED");

    // ========== 自定义错误段 ==========
    ErrorCode REPEATED_REQUESTS = new ErrorCode(900,"重复请求，请稍后重试","REPEATED_REQUESTS" ); // 重复请求
    ErrorCode DEMO_DENY = new ErrorCode(901,"演示模式，禁止写操作","DEMO_DENY");
    ErrorCode VALUE_ERROR = new ErrorCode(910, "值错误", "VALUE_ERROR");
    ErrorCode GOOGLE_AUTH_NOT_BIND = new ErrorCode(997, "谷歌验证器未绑定","GOOGLE_AUTH_NOT_BIND");
    ErrorCode GOOGLE_AUTH_CODE_ERROR = new ErrorCode(998, "谷歌验证码错误","GOOGLE_AUTH_CODE_ERROR");

    ErrorCode SCHEDULER_JOB_STOP = new ErrorCode(999, "[定时任务 - 已禁用][参考 https://doc.iocoder.cn/job/ 开启]","SCHEDULER_JOB_STOP");

    ErrorCode CUSTOM_TRADE_PAIR_NEED_REFERENCE_TRADE_PAIR = new ErrorCode(999, "自发币必须填写参考交易对编码","自发币必须填写参考交易对编码");
    ErrorCode CUSTOM_REFERENCE_NOT_EXISTS = new ErrorCode(999, "自发币的参考交易对不存在","自发币的参考交易对不存在");
    ErrorCode CUSTOM_TRADE_PAIR_NEED_ISSUED_PRICE = new ErrorCode(999, "自发币必须有发行价","自发币必须有发行价");
    ErrorCode CUSTOM_TRADE_PAIR_NEED_ONE_MINUTE_TURNOVER = new ErrorCode(999, "自发币必须有每分钟成交额","自发币必须有每分钟成交额");
}
