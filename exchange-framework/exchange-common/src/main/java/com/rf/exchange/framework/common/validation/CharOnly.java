package com.rf.exchange.framework.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2024-06-25
 */
@Target({
        ElementType.METHOD,
        ElementType.FIELD,
        ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR,
        ElementType.PARAMETER,
        ElementType.TYPE_USE
})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = CharOnlyValidator.class
)
public @interface CharOnly {

    String message() default "格式不正确 ，必须是英文字母";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
