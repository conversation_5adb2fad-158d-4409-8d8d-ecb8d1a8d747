package com.rf.exchange.framework.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Target({
        ElementType.METHOD,
        ElementType.FIELD,
        ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR,
        ElementType.PARAMETER,
        ElementType.TYPE_USE
})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = ImageUrlValidator.class
)
public @interface ImageUrl {
    String message() default "image url error";

    boolean extraPath() default true;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
