package com.rf.exchange.framework.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2024-08-17
 */
@Target({
        ElementType.METHOD,
        ElementType.FIELD,
        ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR,
        ElementType.PARAMETER,
        ElementType.TYPE_USE
})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = {InListIntegerValueValidator.class, InListStringValueValidator.class}
)
public @interface InList {

    // 默认的提示内容
    String message() default "必须提交指定的值";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

    int[] values() default { }; //数值数组，提交的值只能是数组里面

    String[] strValues() default { }; //字符串数组，提交的值只能是数组里面
}
