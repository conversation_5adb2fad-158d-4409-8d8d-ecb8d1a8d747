package com.rf.exchange.framework.common.util.image;

import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.URISyntaxException;
@Slf4j
public class ImageUrlUtil {

    public static String extractPath(String url) {
        if (null == url) {
            return null;
        }
        try {
            URI uri = new URI(url);
            return uri.getPath();
        } catch (URISyntaxException e) {
            log.error("image url extract error {}",url,  e);
            throw new RuntimeException(e);
        }
    }
}
