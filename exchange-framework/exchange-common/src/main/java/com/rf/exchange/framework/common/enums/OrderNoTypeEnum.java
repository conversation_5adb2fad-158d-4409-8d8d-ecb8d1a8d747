package com.rf.exchange.framework.common.enums;

import cn.hutool.core.util.ArrayUtil;
import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderNoTypeEnum implements IntArrayValuable {

    RECHARGE(0, "MRE", "充值订单"),
    WITHDRAW(1, "MWI", "提款订单"),
    UPDATE_BALANCE(2, "UBA", "修改余额"),
    TIME_CONTRACT_BUY(3, "TCB", "买入限时合约"),
    CONTRACT_ORDER_CREATE(4, "COC", "合约委托订单"),
    CONTRACT_POSITION_CREATE(5, "CPC", "合约持仓订单"),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(OrderNoTypeEnum::getValue).toArray();

    /**
     * 状态值
     */
    private final Integer value;
    /**
     * 状态名
     */
    private final String name;
    /**
     * 说明
     */
    private final String desc;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static OrderNoTypeEnum valueOf(Integer value) {
        return ArrayUtil.firstMatch(userType -> userType.getValue().equals(value), OrderNoTypeEnum.values());
    }

}
