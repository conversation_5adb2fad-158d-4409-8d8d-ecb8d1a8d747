package com.rf.exchange.framework.common.util.number;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
public class DecimalFormatUtil {

    /**
     * 格式化BigDecimal数据
     *
     * @param number 需要格式化的数字
     * @param scale  小数保留位数
     * @return 返回格式化后内容
     */
    public static String formatWithScale(BigDecimal number, int scale) {
        if (number == null) {
            return "0";
        }
        if (number.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat(patternWithScale(scale));
        return df.format(number);
    }

    /**
     * 构建DecimalFormat使用的pattern
     *
     * @param scale  小数保留位数
     * @return 返回格式化后内容
     */
    public static String patternWithScale(Integer scale) {
        return "#0." + "0".repeat(Math.max(1, scale));
    }

    /**
     * 格式化 BigDecimal 移除末尾的零
     * @param number 数值
     * @return 字符串
     */
    public static String formatPlain(BigDecimal number) {
        return number.stripTrailingZeros().toPlainString();
    }

    public static void main(String[] args) {
        BigDecimal b0 = new BigDecimal("0.00000000");
        BigDecimal b1 = new BigDecimal("0.00001");
        BigDecimal b2 = new BigDecimal("1.010000");
        BigDecimal b3 = new BigDecimal("10.010000");
        BigDecimal b4 = new BigDecimal("10000.1");

        String s0 = DecimalFormatUtil.formatWithScale(b0, 2);
        String s1 = DecimalFormatUtil.formatWithScale(b1, 2);
        String s2 = DecimalFormatUtil.formatWithScale(b2, 2);
        String s3 = DecimalFormatUtil.formatWithScale(b3, 2);
        String s4 = DecimalFormatUtil.formatWithScale(b4, 2);

        System.out.println(s0);
        System.out.println(s1);
        System.out.println(s2);
        System.out.println(s3);
        System.out.println(s4);
    }
}
