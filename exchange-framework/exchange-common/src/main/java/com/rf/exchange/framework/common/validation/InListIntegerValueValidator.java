package com.rf.exchange.framework.common.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-08-17
 */
public class InListIntegerValueValidator implements ConstraintValidator<InList, Integer> {

    private final Set<Integer> validIntValues = new HashSet<>();

    @Override
    public void initialize(InList constraintAnnotation) {
        final int[] intValues = constraintAnnotation.values();
        for (int value : intValues) {
            validIntValues.add(value);
        }
    }

    @Override
    public boolean isValid(Integer integer, ConstraintValidatorContext constraintValidatorContext) {
        return validIntValues.contains(integer);
    }
}
