package com.rf.exchange.framework.common.pojo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "分页结果")
@Data
public final class PageResult<T> implements Serializable {

    @Schema(description = "数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<T> list;

    @Schema(description = "总量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long total;

    @Schema(description = "当前页", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long pageNo;

    @Schema(description = "总页数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long totalPage;

    public PageResult() {
    }

    public PageResult(IPage<T> iPage) {
        this.list = iPage.getRecords();
        this.total = iPage.getTotal();
        this.pageNo = iPage.getCurrent();
        this.totalPage = iPage.getPages();
    }

    public PageResult(List<T> list, PageResult<T> pageResult) {
        this.list = list;
        this.total = pageResult.getTotal();
        this.pageNo = pageResult.getPageNo();
        this.totalPage = pageResult.getTotalPage();
    }

    public PageResult(List<T> list, Long total, Long pageNo, Long totalPage) {
        this.list = list;
        this.total = total;
        this.pageNo = pageNo;
        this.totalPage = totalPage;
    }

    public PageResult(Long total) {
        this.list = new ArrayList<>();
        this.total = total;
        this.totalPage = 1L;
        this.pageNo = 1L;
    }

    public static <T> PageResult<T> empty() {
        return new PageResult<>(0L);
    }

    public static <T> PageResult<T> empty(Long total) {
        return new PageResult<>(total);
    }

}
