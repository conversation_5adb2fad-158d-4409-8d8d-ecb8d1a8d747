package com.rf.exchange.framework.common.util.number;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数字的工具类，补全 {@link cn.hutool.core.util.NumberUtil} 的功能
 *
 * <AUTHOR>
 */
public class NumberUtils {

    public static Long parseLong(String str) {
        return StrUtil.isNotEmpty(str) ? Long.valueOf(str) : null;
    }

    public static Integer parseInt(String str) {
        return StrUtil.isNotEmpty(str) ? Integer.valueOf(str) : null;
    }

    /**
     * 通过经纬度获取地球上两点之间的距离
     * <p>
     * 参考 <<a href="https://gitee.com/dromara/hutool/blob/1caabb586b1f95aec66a21d039c5695df5e0f4c1/hutool-core/src/main/java/cn/hutool/core/util/DistanceUtil.java">DistanceUtil</a>> 实现，目前它已经被 hutool 删除
     *
     * @param lat1 经度1
     * @param lng1 纬度1
     * @param lat2 经度2
     * @param lng2 纬度2
     * @return 距离，单位：千米
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = lat1 * Math.PI / 180.0;
        double radLat2 = lat2 * Math.PI / 180.0;
        double a = radLat1 - radLat2;
        double b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        double distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.pow(Math.sin(b / 2), 2)));
        distance = distance * 6378.137;
        distance = Math.round(distance * 10000d) / 10000d;
        return distance;
    }

    /**
     * 提供精确的乘法运算
     * <p>
     * 和 hutool {@link NumberUtil#mul(BigDecimal...)} 的差别是，如果存在 null，则返回 null
     *
     * @param values 多个被乘值
     * @return 积
     */
    public static BigDecimal mul(BigDecimal... values) {
        for (BigDecimal value : values) {
            if (value == null) {
                return null;
            }
        }
        return NumberUtil.mul(values);
    }

    /**
     * 格式化百分比
     *
     * @param value             值
     * @param scale             保留几位小数
     * @return 格式化字符串
     */
    public static String formatPercent(double value, int scale) {
        if (value == 0) {
            return formatPercent(Math.abs(value), scale, false);
        }
        return formatPercent(value, scale, false);
    }

    /**
     * 格式化百分比
     *
     * @param value             值
     * @param scale             保留几位小数
     * @param isKeepPercentSign 是否保留百分号
     * @return 格式化字符串
     */
    public static String formatPercent(double value, int scale, boolean isKeepPercentSign) {
        String str = NumberUtil.formatPercent(value, scale);
        if (isKeepPercentSign) {
            return str;
        }
        return str.replace("%", "");
    }

    /**
     * 计算倒数
     * @param number
     * @param scale
     * @return
     */
    public static BigDecimal reciprocal(BigDecimal number,int scale) {
        if (number.compareTo(BigDecimal.ZERO) == 0) {
            throw new ArithmeticException("Division by zero");
        }
        // 设置保留的精度和舍入模式，可以根据需要调整
        return BigDecimal.ONE.divide(number, scale, RoundingMode.HALF_UP);
    }

    public static void main(String[] args) {
        double a1 = 0.000121;
        double a2 = 0.00121;
        double a3 = 1.000121;
        double a4 = 1.00121;
        double a5 = 1.0121;
        double a6 = 1.121;
        double a7 = 10.000121;
        double a8 = 10.00121;
        double a9 = 10.0121;
        double a10 = 10.121;
        double a11 = 100.00121;
        double a12 = 100.0121;
        double a13 = 100.121;
        double a14 = 1000.121;
        double a15 = 0.0000001;
        System.out.printf("%s\n", NumberUtils.formatPercent(a1, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a2, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a3, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a4, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a5, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a6, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a7, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a8, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a9, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a10, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a11, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a12, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a13, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a14, 2, true));
        System.out.printf("%s\n", NumberUtils.formatPercent(a15, 2, true));
    }
}
