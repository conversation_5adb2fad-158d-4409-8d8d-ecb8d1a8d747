package com.rf.exchange.framework.common.util.order;

import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

public class OrderUtil {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final Random RANDOM = new Random();

    /**
     * 根据类型生成单号，后缀4位
     * @param type  类型
     * @return 订单号
     */
    public static String generateOrderNumberSuffix4(OrderNoTypeEnum type) {
        return generateOrderNumber(type,4);
    }

    /**
     * 根据类型生成单号
     * @param type  类型
     * @param suffixCount  后缀随机数位数
     * @return
     */
    public static String generateOrderNumber(OrderNoTypeEnum type,int suffixCount) {
        // 获取当前日期时间，并格式化为yyyyMMddHHmmssSSS
        String dateTime = LocalDateTime.now().format(DATE_FORMATTER);

        // 判断是否需要生成后缀随机数
        String formattedRandomNumber="";
        if(suffixCount>0){
            int randomNumber = RANDOM.nextInt((int) Math.pow(10, suffixCount)); // 生成0到10^suffixCount-1之间的随机数
            formattedRandomNumber = String.format("%04d", randomNumber); // 格式化为四位数字，前面补零
        }

        // 拼接前缀、日期时间和随机数
        return type.getName() + dateTime + formattedRandomNumber;
    }
}
