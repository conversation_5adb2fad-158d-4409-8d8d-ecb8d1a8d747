package com.rf.exchange.framework.common.pojo;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 状态更新参数
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Data
public class UpdateStatusReqVO implements Serializable {
    @Serial
    private final static long serialVersionUID = 1L;

    @Schema(description = "数据id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据id不能为空")
    private Long id;

    @Schema(description = "状态，见 CommonStatusEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    @InEnum(value = CommonStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;
}
