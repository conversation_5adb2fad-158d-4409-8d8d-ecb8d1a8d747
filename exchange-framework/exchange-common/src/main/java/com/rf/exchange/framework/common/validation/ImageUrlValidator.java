package com.rf.exchange.framework.common.validation;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.image.ImageUrlUtil;
import com.rf.exchange.framework.common.util.validation.ValidationUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ImageUrlValidator implements ConstraintValidator<ImageUrl, String> {
    @Override
    public void initialize(ImageUrl annotation) {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果url为空，默认不校验，即校验通过
        if (StrUtil.isEmpty(value)) {
            return true;
        }
        // 校验url
        return ValidationUtils.isImageUrl(value);
    }

}
