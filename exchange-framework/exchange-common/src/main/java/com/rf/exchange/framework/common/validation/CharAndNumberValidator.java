package com.rf.exchange.framework.common.validation;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.validation.ValidationUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

//create by wski<PERSON> on 2024/6/20
public class CharAndNumberValidator implements ConstraintValidator<CharAndNumber, String> {

    @Override
    public void initialize(CharAndNumber annotation) {
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        // 如果手机号为空，默认不校验，即校验通过
        if (StrUtil.isEmpty(s)) {
            return true;
        }
        // 校验手机
        return ValidationUtils.isCharAndNumber(s);
    }
}
