package com.rf.exchange.framework.jackson.core.databind;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.rf.exchange.framework.apilog.core.interceptor.BigDecimalContextInterceptor;
import io.swagger.v3.oas.models.security.SecurityScheme;

/**
 * BigDecimal格式化的 Context 上下文
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
public class BigDecimalScaleContextHolder {
    /**
     * 价格信息
     */
    private static final ThreadLocal<Integer> PRICE_SCALE = new TransmittableThreadLocal<>();

    /**
     * 普通的BigDecimal格式化小数点位数
     */
    private static final ThreadLocal<Integer> REGULAR_SCALE = new TransmittableThreadLocal<>();

    /**
     * 设置交易对价格的小数格点位数
     */
    public static void setPriceScale(Integer scale) {
        PRICE_SCALE.set(scale);
    }

    /**
     * 获取交易对价格的小数格点位数
     *
     * @return 保留位数
     */
    public static Integer getPriceScale() {
        return PRICE_SCALE.get();
    }

    /**
     * 设置常规BigDecimal的小数格点位数
     */
    public static void setRegularScale(Integer scale) {
        REGULAR_SCALE.set(scale);
    }

    /**
     * 获取常规BigDecimal的小数点保留位数
     *
     * @return 保留位数
     */
    public static Integer getRegularScale() {
        return REGULAR_SCALE.get();
    }

    /**
     * 清理的动作在 {@link BigDecimalContextInterceptor} 中执行
     */
    public static void clear() {
        PRICE_SCALE.remove();
        REGULAR_SCALE.remove();
    }
}
