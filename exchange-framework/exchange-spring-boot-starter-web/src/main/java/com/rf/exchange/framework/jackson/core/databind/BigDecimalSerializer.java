package com.rf.exchange.framework.jackson.core.databind;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonStreamContext;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024-07-14
 */
@Slf4j
@JsonComponent
public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {

    public static final BigDecimalSerializer INSTANCE = new BigDecimalSerializer();

    /**
     * 匹配交易对价格字段的正则表达式
     * 匹配包含price的字段，以及常见的价格字段名
     */
    private static final Pattern pricePattern = Pattern.compile("(high|low|open|close|current)[Pp]rice|change24h|high24h|low24h|volume24h|closePrice");

    private static final Pattern noScalePattern = Pattern.compile("[Pp]ercentage");

    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        final JsonStreamContext jsonContext = jsonGenerator.getOutputContext();
        final String currentName = jsonContext.getCurrentName();
        // 价格格式化的小数位数
        final Integer scale = BigDecimalScaleContextHolder.getPriceScale();

        // 如果是价格字段，使用指定精度或默认2位精度
        if (pricePattern.matcher(currentName).find()) {
            int priceScale = scale != null ? scale : 2; // 默认2位精度
            jsonGenerator.writeString(DecimalFormatUtil.formatWithScale(bigDecimal, priceScale));
        } else if (noScalePattern.matcher(currentName).find()) {
            jsonGenerator.writeString(bigDecimal.toPlainString());
        } else {
            // 对于其他字段，使用默认格式化（通常是2位小数）
            jsonGenerator.writeString(DecimalFormatUtil.formatPlain(bigDecimal));
        }
    }

}