package com.rf.exchange.framework.jackson.core.databind;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024-07-26
 */
@JsonComponent
public class BooleanDeserializer extends JsonDeserializer<Boolean> {

    public static final BooleanDeserializer INSTANCE = new BooleanDeserializer();

    @Override
    public Boolean deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        final JsonToken jsonToken = jsonParser.getCurrentToken();
        if (jsonToken == JsonToken.VALUE_NUMBER_INT) {
            final int intValue = jsonParser.getIntValue();
            return intValue != 0;
        } else if (jsonToken == JsonToken.VALUE_STRING) {
            String stringValue = jsonParser.getValueAsString();
            return stringValue.equals("true");
        }
        return jsonParser.getBooleanValue();
    }
}
