package com.rf.exchange.framework.tracer.config;

import com.rf.exchange.framework.common.enums.WebFilterOrderEnum;
import com.rf.exchange.framework.tracer.core.aop.BizTraceAspect;
import com.rf.exchange.framework.tracer.core.filter.TraceFilter;
//import io.opentracing.Tracer;
//import io.opentracing.util.GlobalTracer;
//import org.apache.skywalking.apm.toolkit.opentracing.SkywalkingTracer;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

/**
 * Tracer 配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass({BizTraceAspect.class})
@EnableConfigurationProperties(TracerProperties.class)
@ConditionalOnProperty(prefix = "exchange.tracer", value = "enable", matchIfMissing = true)
public class ExchangeTracerAutoConfiguration {

    // TODO: 这里需要调试下skywalking的监控
    @Bean
    public TracerProperties bizTracerProperties() {
        return new TracerProperties();
    }

    @Bean
    public BizTraceAspect bizTracingAop() {
        return new BizTraceAspect();
    }

    //@Bean
    //public SkywalkingTracer tracer() {
    //    // 创建 SkyWalkingTracer 对象
    //    return new SkywalkingTracer();
    //    //Tracer.SpanBuilder spanBuilder = tracer.buildSpan(bizTracerProperties().getSpanName());
    //    //// 设置为 GlobalTracer 的追踪器
    //    //GlobalTracer.registerIfAbsent(tracer);
    //    ////GlobalTracer.register(tracer);
    //    //return tracer;
    //}

    /**
     * 创建 TraceFilter 过滤器，响应 header 设置 traceId
     */
    @Bean
    public FilterRegistrationBean<TraceFilter> traceFilter() {
        FilterRegistrationBean<TraceFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new TraceFilter());
        registrationBean.setOrder(WebFilterOrderEnum.TRACE_FILTER);
        return registrationBean;
    }

}
