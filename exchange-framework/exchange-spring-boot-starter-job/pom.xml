<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>exchange-spring-boot-starter-job</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>任务拓展
        1. 定时任务，基于 Quartz 拓展
        2. 异步任务，基于 Spring Async 拓展
    </description>


    <dependencies>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-common</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->

        <!-- 这里使用的是修改过的xxl-job -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <!-- <dependency> -->
        <!--     <groupId>com.rf.dev</groupId> -->
        <!--     <artifactId>exchange-xxl-job-core</artifactId> -->
        <!-- </dependency> -->

        <!-- 工具类相关 -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

    </dependencies>

</project>
