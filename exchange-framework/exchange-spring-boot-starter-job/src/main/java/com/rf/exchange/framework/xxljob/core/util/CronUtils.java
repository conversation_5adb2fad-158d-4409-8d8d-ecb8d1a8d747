package com.rf.exchange.framework.xxljob.core.util;

import org.springframework.scheduling.support.CronExpression;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Quartz Cron 表达式的工具类
 *
 * <AUTHOR>
 */
public class CronUtils {

    /**
     * 校验 CRON 表达式是否有效
     *
     * @param cronExpression CRON 表达式
     * @return 是否有效
     */
    public static boolean isValid(String cronExpression) {
        return CronExpression.isValidExpression(cronExpression);
    }

    /**
     * 基于 CRON 表达式，获得下 n 个满足执行的时间
     *
     * @param cronExpression CRON 表达式
     * @param n 数量
     * @return 满足条件的执行时间
     */
    public static List<LocalDateTime> getNextTimes(String cronExpression, int n) {
        // 获得 CronExpression 对象
        CronExpression cron;
        cron = CronExpression.parse(cronExpression);
        // 从当前开始计算，n 个满足条件的
        LocalDateTime now = LocalDateTime.now();
        List<LocalDateTime> nextTimes = new ArrayList<>(n);
        for (int i = 0; i < n; i++) {
            LocalDateTime nextTime = cron.next(now);
            nextTimes.add(nextTime);
            // 切换现在，为下一个触发时间；
            now = nextTime;
        }
        return nextTimes;
    }

}
