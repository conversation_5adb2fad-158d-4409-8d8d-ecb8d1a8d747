package com.rf.exchange.framework.xxljob.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@Validated
@ConfigurationProperties(prefix = "xxl.job")
public class ExchangeXxlJobProperties {

    private Boolean enable;

    @NotNull(message = "admin不能为空")
    private Admin admin;

    @NotNull(message = "executor不能为空")
    private Executor executor;

    //@Value("${xxl.job.accessToken}")
    @NotEmpty(message = "accessToken不能为空")
    private String accessToken;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Valid
    public static class Admin {
        //@Value("${xxl.job.admin.addresses}")
        @NotEmpty(message = "addresses不能为空")
        private String addresses;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Valid
    public static class Executor {

        @NotEmpty(message = "appName不能为空")
        private String appName;

        private String address;

        private String ip;

        /**
         * port为空自动获取端口
         */
        private Integer port;

        @NotEmpty(message = "logPath不能为空")
        private String logPath;

        @NotNull(message = "logRetentionDays不能为空")
        private int logRetentionDays = 7;
    }
}
