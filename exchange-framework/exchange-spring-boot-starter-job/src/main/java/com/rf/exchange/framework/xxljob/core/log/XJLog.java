package com.rf.exchange.framework.xxljob.core.log;

import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-07-24
 */
@Slf4j
public class XJLog {

    public static void log(String appendLogPattern, Object... appendLogArguments) {
        info(appendLogPattern, appendLogArguments);
    }

    public static void info(String appendLogPattern, Object... appendLogArguments) {
        XxlJobContext xxlJobContext = XxlJobContext.getXxlJobContext();
        if (xxlJobContext != null) {
            XxlJobHelper.log("[INFO] " + appendLogPattern, appendLogArguments);
        }
        log.info(appendLogPattern, appendLogArguments);
    }

    public static void error(String appendLogPattern, Object... appendLogArguments) {
        XxlJobContext xxlJobContext = XxlJobContext.getXxlJobContext();
        if (xxlJobContext != null) {
            XxlJobHelper.log("[ERROR] " + appendLogPattern, appendLogArguments);
        }
        log.error(appendLogPattern, appendLogArguments);
    }

    public static void warn(String appendLogPattern, Object... appendLogArguments) {
        XxlJobContext xxlJobContext = XxlJobContext.getXxlJobContext();
        if (xxlJobContext != null) {
            XxlJobHelper.log("[WARN] " + appendLogPattern, appendLogArguments);
        }
        log.warn(appendLogPattern, appendLogArguments);
    }

    public static void debug(String appendLogPattern, Object... appendLogArguments) {
        XxlJobContext xxlJobContext = XxlJobContext.getXxlJobContext();
        if (xxlJobContext != null) {
            XxlJobHelper.log("[DEBUG] " + appendLogPattern, appendLogArguments);
        }
        log.debug(appendLogPattern, appendLogArguments);
    }
}
