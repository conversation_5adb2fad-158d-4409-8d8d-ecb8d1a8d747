package com.rf.exchange.framework.websocket.core.message;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * WebSocket收到的消息
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
public class WebSocketRecvMessage implements Serializable {
    /**
     * 浏览器指纹
     */
    private String fp;
    /**
     * 时间戳
     */
    private Long ts;
    /**
     * 消息类型
     */
    private String type;
    /**
     * 命令
     */
    private Integer cmd;
    /**
     * 消息内容
     */
    private Map<String, Object> data;
}
