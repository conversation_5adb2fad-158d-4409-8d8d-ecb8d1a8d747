package com.rf.exchange.framework.websocket.core.session;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.websocket.config.WebSocketProperties;
import com.rf.exchange.framework.websocket.core.util.WebSocketFrameworkUtils;
import com.rf.exchange.framework.xxljob.core.log.XJLog;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * 默认的 {@link WebSocketSessionManager} 实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class WebSocketSessionManagerImpl implements WebSocketSessionManager {

    private final static int HASH_MAP_SMALL_CAPACITY = (int) (50 / 0.75 + 1);

    // 假定一个租户同时有5000个连接，如果超过这个数值需要考虑优化session管理的方式
    private final static int HASH_MAP_LARGE_CAPACITY = (int) (5000 / 0.75 + 1);

    private final WebSocketProperties properties;

    /**
     * SessionId 与 WebSocketSession 映射
     * <p>
     * key：Session 编号
     */
    private final ConcurrentMap<String, WebSocketSession> idSessions = new ConcurrentHashMap<>(HASH_MAP_LARGE_CAPACITY);

    ///**
    // * user 与 WebSocketSession 映射
    // * <p>
    // * key1: 租户id
    // * key2：用户类型
    // * key3：用户编号
    // */
    //private final ConcurrentMap<Long, ConcurrentMap<Long, ConcurrentMap<Long, CopyOnWriteArrayList<String>>>> userSessions = new ConcurrentHashMap<>(HASH_MAP_SMALL_CAPACITY);

    /**
     * SystemUser 与 WebSocketSession ID 的 映射
     * <p>
     * key1: 租户id
     * key2: 用户id
     */
    private final ConcurrentMap<Long, ConcurrentMap<Long, ConcurrentSkipListSet<String>>> systemUserSessionIds = new ConcurrentHashMap<>(HASH_MAP_SMALL_CAPACITY);

    /**
     * member 与 WebSocketSession ID 的 映射
     * <p>
     * key1: 租户id
     * key2: 用户id
     */
    private final ConcurrentMap<Long, ConcurrentMap<Long, ConcurrentSkipListSet<String>>> memberUserSessionIds = new ConcurrentHashMap<>(HASH_MAP_SMALL_CAPACITY);

    /**
     * SessionId 与 连接建立时的时间戳 映射
     * <p>
     * key: Session 编号
     */
    private final ConcurrentMap<String, Long> sessionConnectTime = new ConcurrentHashMap<>(HASH_MAP_LARGE_CAPACITY);

    /**
     * 未认证的Session
     */
    private final ConcurrentSkipListSet<String> unAuthedSessions = new ConcurrentSkipListSet<>();

    /**
     * 已验证登录的Session
     */
    @Getter
    private final ConcurrentSkipListSet<String> authedSessions = new ConcurrentSkipListSet<>();

    //======================
    // 这两个map实际是与业务有关的映射关系，按理说不应该放在这里，但是因为断连之后不方便移除订阅关系，所以放在这里
    /**
     * session订阅的交易对 映射
     * 格式: <订阅的交易对: ConcurrentSkipListSet<Session编号>>
     */
    private final ConcurrentMap<String, ConcurrentSkipListSet<String>> tradePairSubscribeMap = new ConcurrentHashMap<>(HASH_MAP_LARGE_CAPACITY);

    /**
     * session订阅的交易对 映射
     * 格式：<Session编号: 订阅的交易对>
     */
    private final ConcurrentMap<String, String> sessionSubscribeMap = new ConcurrentHashMap<>(HASH_MAP_LARGE_CAPACITY);
    //======================


    public WebSocketSessionManagerImpl(WebSocketProperties properties) {
        this.properties = properties;
    }

    @Override
    public void logDebugInfo() {
        XJLog.info("会话更新中的session 数量：{}", sessionConnectTime.size());
        XJLog.info("未认证会话的session 数量：{}", unAuthedSessions.size());
        XJLog.info("认证会话的session   数量：{}", authedSessions.size());
        XJLog.info("idSession的        数量：{}", idSessions.size());
    }

    @Override
    public void addUnAuthedSession(WebSocketSession session) {
        // 添加到 idSessions 中
        idSessions.put(session.getId(), session);
        // 添加到未认证的Session Set中
        unAuthedSessions.add(session.getId());
        // 刷新存活时间
        refreshSession(session, "unAuth");
        log.info("未认证 ws连接总数: {}", unAuthedSessions.size());
    }

    @Override
    public void addAuthSession(WebSocketSession session, LoginUser loginUser) {
        if (loginUser == null) {
            return;
        }
        // 如果认证登录用户成功则更新attributes中的用户信息
        WebSocketFrameworkUtils.setLoginUser(loginUser, session.getAttributes());
        // 从未认证的session集合中移除session
        unAuthedSessions.remove(session.getId());
        // 添加已经认证的session集合中
        authedSessions.add(session.getId());
        // 添加到 idSessions 中
        idSessions.put(session.getId(), session);
        // 刷新session的存活时间
        refreshSession(session, "auth");

        // 获取租户所有的websocket连接
        ConcurrentMap<Long, ConcurrentSkipListSet<String>> tenantSessionMap = getTenantWebSocketSessionMap(loginUser.getTenantId(), loginUser.getUserType());
        ConcurrentSkipListSet<String> loginUserSessionIdSet = tenantSessionMap.get(loginUser.getId());
        if (loginUserSessionIdSet == null) {
            loginUserSessionIdSet = new ConcurrentSkipListSet<>();
            if (tenantSessionMap.putIfAbsent(loginUser.getId(), loginUserSessionIdSet) != null) {
                loginUserSessionIdSet = tenantSessionMap.get(loginUser.getId());
            }
        } else if (loginUser.getUserType().equals(UserTypeEnum.MEMBER.getValue())) {
            // 如果是会员的连接则判断连接数是否超过限制
            if (loginUserSessionIdSet.size() >= properties.getMaxUserSessionCount()) {
                String prevSessionId = loginUserSessionIdSet.getFirst();
                if (StrUtil.isNotEmpty(prevSessionId) && !prevSessionId.equals(session.getId())) {
                    log.info("用户:{} 的连接数 {}", loginUser.getId(), loginUserSessionIdSet.size());
                    //WebSocketSession prevSession = idSessions.get(prevSessionId);
                    //closeSession(prevSession);
                }
            }
        }
        loginUserSessionIdSet.add(session.getId());
        log.debug("转移到认证连接 userId:{} sessionId:{} 总连接数:{}", loginUser.getId(), session.getId(), loginUserSessionIdSet.size());
    }

    private ConcurrentMap<Long, ConcurrentSkipListSet<String>> getTenantWebSocketSessionMap(Long tenantId, Integer userType) {
        ConcurrentMap<Long, ConcurrentMap<Long, ConcurrentSkipListSet<String>>> userSessionIdMap;
        if (Objects.equals(UserTypeEnum.ADMIN.getValue(), userType)) {
            userSessionIdMap = systemUserSessionIds;
        } else {
            userSessionIdMap = memberUserSessionIds;
        }
        // 添加到 userSessions 中
        ConcurrentMap<Long, ConcurrentSkipListSet<String>> tenantSessionIdMap = userSessionIdMap.get(tenantId);
        if (tenantSessionIdMap == null) {
            tenantSessionIdMap = new ConcurrentHashMap<>(HASH_MAP_LARGE_CAPACITY);
            if (userSessionIdMap.putIfAbsent(tenantId, tenantSessionIdMap) != null) {
                tenantSessionIdMap = userSessionIdMap.get(tenantId);
            }
        }
        return tenantSessionIdMap;
    }

    @Override
    public void removeSession(WebSocketSession session) {
        if (!idSessions.containsKey(session.getId())) {
            return;
        }
        // 关闭连接前移除订阅关系
        removeSessionSubscribe(session.getId());
        // 从未认证的 Session中移除
        unAuthedSessions.remove(session.getId());
        // 移除从 idSessions 中
        idSessions.remove(session.getId());
        // 如果这个连接是登录连接
        LoginUser loginUser = WebSocketFrameworkUtils.getLoginUser(session);
        if (loginUser != null) {
            authedSessions.remove(session.getId());
            ConcurrentMap<Long, ConcurrentSkipListSet<String>> tenantSessionMap = getTenantWebSocketSessionMap(loginUser.getTenantId(), loginUser.getUserType());
            if (tenantSessionMap == null) {
                return;
            }
            ConcurrentSkipListSet<String> loginUserSessionIdSet = tenantSessionMap.get(loginUser.getId());
            if (loginUserSessionIdSet != null) {
                loginUserSessionIdSet.removeIf(removeSession -> removeSession.equals(session.getId()));
                // 如果当前用户没有任何连接则从租户的连接map中移除list
                if (CollUtil.isEmpty(loginUserSessionIdSet)) {
                    tenantSessionMap.remove(loginUser.getId(), loginUserSessionIdSet);
                }
            }
            log.info("移除连接 userId:{} session:{} left:{} now:{}", loginUser.getId(), session.getId(), loginUserSessionIdSet != null ? loginUserSessionIdSet.size() : 0, System.currentTimeMillis());
        } else {
            log.info("移除未认证连接 session:{} left:{} now:{}", session.getId(), unAuthedSessions.size(), System.currentTimeMillis());
        }
        sessionConnectTime.remove(session.getId());
        authedSessions.remove(session.getId());
        log.info("清理session记录 {}", session.getId());
    }

    @Override
    public void closeSession(WebSocketSession session) {
        try {
            session.close(CloseStatus.GOING_AWAY);
            removeSession(session);
            log.debug("关闭连接 session:{} now:{}", session.getId(), System.currentTimeMillis());
        } catch (IOException e) {
            log.error("关闭ws session失败 id:{} error:{}", session.getId(), e.getMessage());
        }
    }

    @Override
    public void kickOutAllExpireSessions() {
        Collection<String> expiredSessions = getAllExpiredSessionIds();
        for (String sessionId : expiredSessions) {
            WebSocketSession wsSession = idSessions.get(sessionId);
            closeSession(wsSession);
            // 从更新时间的集合中移除
            sessionConnectTime.remove(sessionId);
            log.info("踢出 session:{} now:{}", sessionId, System.currentTimeMillis());
        }
    }

    @Override
    public void refreshSession(WebSocketSession session, String from) {
        long now = System.currentTimeMillis();
        log.info("刷新 session:{} now:{} ip:{} from:{}", session.getId(), now, session.getRemoteAddress(), from);
        sessionConnectTime.put(session.getId(), now);
    }

    @Override
    public Collection<String> getAllExpiredSessionIds() {
        Set<String> expiredSessions = new HashSet<>();
        long now = System.currentTimeMillis();
        for (Map.Entry<String, Long> entry : sessionConnectTime.entrySet()) {
            // 如果最后的更新时间和当前时间相差超过30秒钟则添加到需要移除会话的集合中
            if (((now - entry.getValue()) / 1000) > properties.getHeartTimeOut()) {
                expiredSessions.add(entry.getKey());
            }
        }
        return expiredSessions;
    }

    @Override
    public Collection<String> getUnAuthedSessions() {
        return unAuthedSessions;
    }

    @Override
    public WebSocketSession getWebSocketSession(String id) {
        return idSessions.get(id);
    }

    @Override
    public Collection<WebSocketSession> getWebSocketSessions(Collection<String> sessionIds) {
        List<WebSocketSession> sessionList = new ArrayList<>(sessionIds.size());
        for (String sessionId : sessionIds) {
            sessionList.add(getWebSocketSession(sessionId));
        }
        return sessionList;
    }

    @Override
    public Collection<String> getSessionIds(Long tenantId, Integer userType) {
        ConcurrentMap<Long, ConcurrentSkipListSet<String>> tenantSessionMap = getTenantWebSocketSessionMap(tenantId, userType);
        Set<String> sessionIdSet = new HashSet<>(tenantSessionMap.size());
        for (Map.Entry<Long, ConcurrentSkipListSet<String>> entry : tenantSessionMap.entrySet()) {
            ConcurrentSkipListSet<String> idSet = entry.getValue();
            sessionIdSet.addAll(idSet);
        }
        return sessionIdSet;
    }

    @Override
    public Collection<String> getSessionIds(Long tenantId, Integer userType, Long userId) {
        ConcurrentMap<Long, ConcurrentSkipListSet<String>> tenantSessionMap = getTenantWebSocketSessionMap(tenantId, userType);
        if (CollUtil.isEmpty(tenantSessionMap)) {
            return new ArrayList<>();
        }
        Set<String> websocketSessionList = new HashSet<>(tenantSessionMap.size());
        ConcurrentSkipListSet<String> sessions = tenantSessionMap.get(userId);
        if (sessions != null && !sessions.isEmpty()) {
            websocketSessionList.addAll(sessions);
        }
        return websocketSessionList;
    }

    @Override
    public Collection<String> getSessionIds(Integer userType) {
        Set<String> websocketSessionList = new HashSet<>((int) (100 / 0.75 + 1));
        if (UserTypeEnum.ADMIN.getValue().equals(userType)) {
            getWebSocketSession(websocketSessionList, systemUserSessionIds);
        } else {
            getWebSocketSession(websocketSessionList, memberUserSessionIds);
        }
        return websocketSessionList;
    }

    private void getWebSocketSession(Collection<String> returnCollection, ConcurrentMap<Long, ConcurrentMap<Long, ConcurrentSkipListSet<String>>> sessionIdMap) {
        for (ConcurrentMap<Long, ConcurrentSkipListSet<String>> value : sessionIdMap.values()) {
            for (Map.Entry<Long, ConcurrentSkipListSet<String>> entry : value.entrySet()) {
                returnCollection.addAll(entry.getValue());
            }
        }
    }

    @Override
    public ConcurrentMap<String, ConcurrentSkipListSet<String>> getTradePairSubscribeMap() {
        return tradePairSubscribeMap;
    }

    @Override
    public ConcurrentMap<String, String> getSessionSubscribeMap() {
        return sessionSubscribeMap;
    }

    @Override
    public void subscribeTradePair(String sessionId, String tradePairCode) {
        if (sessionSubscribeMap.containsKey(tradePairCode)) {
            return;
        }
        final String lastSubTradePairCode = sessionSubscribeMap.get(sessionId);
        // 如果之前订阅的交易对不为空则移除之前订阅的交易对
        if (StrUtil.isNotEmpty(lastSubTradePairCode)) {
            final ConcurrentSkipListSet<String> sessionSet = tradePairSubscribeMap.get(lastSubTradePairCode);
            if (CollUtil.isNotEmpty(sessionSet)) {
                sessionSet.remove(sessionId);
            }
        }

        // 更新会话订阅的交易对
        sessionSubscribeMap.put(sessionId, tradePairCode);

        // 更新交易对订阅的会话集合
        ConcurrentSkipListSet<String> sessionSubscribeSet = tradePairSubscribeMap.get(tradePairCode);
        if (sessionSubscribeSet == null) {
            sessionSubscribeSet = tradePairSubscribeMap.get(tradePairCode);
            if (sessionSubscribeSet == null) {
                ConcurrentSkipListSet<String> newSet = new ConcurrentSkipListSet<>();
                tradePairSubscribeMap.put(tradePairCode, newSet);
                sessionSubscribeSet = newSet;
            }
        }
        sessionSubscribeSet.add(sessionId);
    }

    @Override
    public void removeSessionSubscribe(String sessionId) {
        final String tradePairCode = sessionSubscribeMap.get(sessionId);
        if (StrUtil.isNotEmpty(tradePairCode)) {
            ConcurrentSkipListSet<String> tradePairSessionSet = tradePairSubscribeMap.get(tradePairCode);
            if (tradePairSessionSet != null && !tradePairSessionSet.isEmpty()) {
                tradePairSessionSet.remove(sessionId);
            }
            sessionSubscribeMap.remove(sessionId);
        }
    }
}
