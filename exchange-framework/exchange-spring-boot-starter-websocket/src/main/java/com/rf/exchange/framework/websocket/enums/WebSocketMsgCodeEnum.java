package com.rf.exchange.framework.websocket.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-07-10
 */
@Getter
@AllArgsConstructor
public enum WebSocketMsgCodeEnum {
    SUCCESS(200, "成功"),
    TOKEN_INVALID(401, "token失效"),
    TOO_FREQUENCY(429, "发送频繁"),
    CMD_WRONG(500, "cmd不存在错误"),
    BAD_FORMAT(600, "格式错误"),
    BAD_MESSAGE(601, "内容错误");

    private final int code;
    private final String name;
}

