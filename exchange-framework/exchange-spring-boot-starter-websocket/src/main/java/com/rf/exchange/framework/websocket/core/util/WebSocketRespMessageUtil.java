package com.rf.exchange.framework.websocket.core.util;

import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.websocket.core.message.WebSocketRespMessage;
import com.rf.exchange.framework.websocket.enums.WebSocketMsgTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024-07-10
 */
@Slf4j
public class WebSocketRespMessageUtil {

    public static String getResponseMessage(Integer code, String message) {
        WebSocketRespMessage resp = new WebSocketRespMessage()
                .setCode(code)
                .setType(WebSocketMsgTypeEnum.RESP.getType())
                .setTs(System.currentTimeMillis())
                .setMsg(message);
        return JsonUtils.toJsonString(resp);
    }

    public static void sendResponse(WebSocketSession session, Integer code, String message) {
        String responseMessage = getResponseMessage(code, message);
        try {
            session.sendMessage(new TextMessage(responseMessage));
        } catch (IOException e) {
            log.error("发送响应失败 {}", e.getMessage());
        }
    }
}
