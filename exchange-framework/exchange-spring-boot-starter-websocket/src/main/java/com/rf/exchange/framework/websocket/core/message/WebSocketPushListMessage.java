package com.rf.exchange.framework.websocket.core.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WebSocketPushListMessage<T extends Serializable> {
    private Long ts;
    private String type;
    private Integer cmd;
    private List<T> data;
}
