<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>exchange-spring-boot-starter-protection</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>服务保证，提供分布式锁、幂等、限流、熔断等等功能</description>


    <dependencies>
        <!-- Web 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-web</artifactId>
            <!-- 设置为 provided，只有限流、幂等使用到 -->
            <scope>provided</scope>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

</project>
