package com.rf.exchange.module.exc.enums;

import com.rf.exchange.framework.common.exception.ErrorCode;

/**
 * Exchange 错误码枚举类
 * <p>
 * exchange 系统，使用 1-014-000-000 段
 */
public interface ErrorCodeConstants {
    ErrorCode TRADE_PAIR_NOT_EXISTS = new ErrorCode(1_014_000_000, "交易对不存在","TRADE_PAIR_NOT_EXISTS");
    ErrorCode TRADE_PAIR_EXISTS = new ErrorCode(1_014_000_001, "交易对已存在","TRADE_PAIR_EXISTS");
    ErrorCode TRADE_PAIR_TENANT_NOT_EXISTS = new ErrorCode(1_014_001_000, "租户没有该交易对","TRADE_PAIR_TENANT_NOT_EXISTS");
    ErrorCode TRADE_PAIR_TENANT_EXISTS = new ErrorCode(1_014_001_001, "租户交易对已存在","TRADE_PAIR_TENANT_EXISTS");
    ErrorCode TRADE_TENANT_ASSET_TYPE_NOT_EXISTS = new ErrorCode(1_014_001_002, "租户交易对资产类型配置不存在", "TRADE_TENANT_ASSET_TYPE_NOT_EXISTS");
    ErrorCode TRADE_TENANT_NEED_DEFAULT = new ErrorCode(1_014_001_003, "必须有一个默认租户交易对", "TRADE_TENANT_NEED_DEFAULT");
    ErrorCode TRADE_TENANT_DUPLICATE_DEFAULT = new ErrorCode(1_014_001_003, "租户的默认交易对已经存在", "TRADE_TENANT_DUPLICATE_DEFAULT");
}