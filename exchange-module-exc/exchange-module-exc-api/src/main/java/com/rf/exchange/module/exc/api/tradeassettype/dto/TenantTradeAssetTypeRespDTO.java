package com.rf.exchange.module.exc.api.tradeassettype.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
public class TenantTradeAssetTypeRespDTO {
    /**
     * 编号
     */
    private Long id;
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 交易对资产类型
     */
    private Integer assetType;
    /**
     * 资产了类型名称code
     */
    private String nameI18n;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 开启状态（0正常 1停用）
     */
    private Integer status;
}
