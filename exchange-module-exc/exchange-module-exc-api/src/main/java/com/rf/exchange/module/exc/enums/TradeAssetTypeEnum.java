package com.rf.exchange.module.exc.enums;

import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 交易对的资产类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum TradeAssetTypeEnum implements IntArrayValuable {
    CRYPTO(0, "ASSET_TYPE_CRYPTO"),
    STOCKS(1, "ASSET_TYPE_STOCKS"),
    COMMODITY(2, "ASSET_TYPE_COMMODITY"),
    FOREX(3, "ASSET_TYPE_FOREX"),
    FAVORITES(4, "ASSET_TYPE_FAVORITES");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradeAssetTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
