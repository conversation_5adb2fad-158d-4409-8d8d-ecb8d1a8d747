package com.rf.exchange.module.exc.enums;

import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 交易对的资产类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum TradeAssetTypeEnum implements IntArrayValuable {
    CRYPTO(0, "加密货币"),
    STOCKS(1, "股票"),
    COMMODITY(2, "大宗商品/贵金属"),
    FOREX(3, "外汇"),
    FAVORITES(4, "自选");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradeAssetTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
