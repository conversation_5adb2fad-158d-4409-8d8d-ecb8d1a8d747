package com.rf.exchange.module.exc.api.tradepair;

import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-06-21
 */
public interface TradePairTenantApi {

    /**
     * 拷贝租户的交易对数据
     *
     * @param sourceTenantId 源租户id
     * @param targetTenantId 目标租户id
     */
    void copyDataFromTenant(Long sourceTenantId, Long targetTenantId);

    /**
     * 获取租户默认交易对
     *
     * @return 默认交易对
     */
    TradePairRespDTO getDefaultTradePair(Long tenantId);

    /**
     * 校验租户运营的交易对是否存在
     *
     * @param tenantId    租户id
     * @param tradePairId 交易对id
     * @return 交易对信息
     */
    TradePairRespDTO validateTradePairExists(Long tenantId, Long tradePairId);

    TradePairRespDTO validateTradePairExists(Long tenantId, String tradePairCode);

    ///**
    // * 获取指定租户的指定交易对
    // *
    // * @param tenantId    租户id
    // * @param tradePairId 交易对id
    // * @return 交易对信息
    // */
    //TradePairRespDTO getTradePair(Long tenantId, Long tradePairId);

    /**
     * 获取租户所有交易对列表
     *
     * @param tenantId 交易对id
     * @param codeSet  交易对id集合
     * @return 交易对列表
     */
    List<TradePairRespDTO> getTradePairListByCodes(Long tenantId, Set<String> codeSet);

    /**
     * 获取指定交易类型或者指定资产类型的交易对列表
     *
     * @param tenantId  租户id
     * @param tradeType 交易类型
     * @param assetType 资产类型
     * @return 交易对列表
     */
    List<TradePairRespDTO> getListCachedByType(Long tenantId, Integer tradeType, Integer assetType);

}
