package com.rf.exchange.module.exc.api.tradeassettype;

import com.rf.exchange.module.exc.api.tradeassettype.dto.TenantTradeAssetTypeRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
public interface TradeAssetTypeApi {

    /**
     * 拷贝租户的资产类型数据
     *
     * @param sourceTenantId 源租户id
     * @param targetTenantId 目标租户id
     */
    void copyDataFromTenant(Long sourceTenantId, Long targetTenantId);

    List<TenantTradeAssetTypeRespDTO> getTradeTypes(Long tenantId);

}
