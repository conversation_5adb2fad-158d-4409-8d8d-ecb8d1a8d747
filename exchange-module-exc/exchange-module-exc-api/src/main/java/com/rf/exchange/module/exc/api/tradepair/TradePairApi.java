package com.rf.exchange.module.exc.api.tradepair;

import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface TradePairApi {

    /**
     * 校验交易对是否存在
     *
     * @param id 交易对id
     * @return 交易对信息
     */
    TradePairRespDTO validateTradePairExists(Long id);

    /**
     * 校验交易对是否存在
     *
     * @param code 交易对代码
     * @return 交易对信息
     */
    TradePairRespDTO validateTradePairExists(String code);

    /**
     * 获取指定id的交易对
     *
     * @param id 交易对id
     * @return 交易对信息
     */
    TradePairRespDTO getTradePair(Long id);

    /**
     * 获取所有交易对列表(只有启用交易对)
     *
     * @return 交易对列表
     */
    List<TradePairRespDTO> getTradePairEnableCached();

    /**
     * 获取所有交易对列表(包括已停用的)
     *
     * @return 交易对列表
     */
    List<TradePairRespDTO> getTradePairAll();

    /**
     * 获取所有缓存的交易对
     *
     * @return 交易对列表
     */
    List<TradePairRespDTO> getTradePairListCached();

    /**
     * 通过交易对代码获取缓存的交易对
     *
     * @param code 交易对代码
     * @return 交易对信息
     */
    TradePairRespDTO getTradePairCachedByCode(String code);

    /**
     * 获取所有缓存的可用交易对
     *
     * @return 交易对列表
     */
    List<TradePairRespDTO> getTradePairEnableListCached();

    /**
     * 获取所有缓存的交易对
     *
     * @return 交易列表
     */
    Map<String, TradePairRespDTO> getTradePairMapCached();

    /**
     * 获取指定id集合的交易对
     *
     * @param ids 交易对的id集合
     * @return 交易对列表
     */
    List<TradePairRespDTO> getTradePairListByIds(Set<Long> ids);

    /**
     * 通过代码获取名称
     *
     * @param code
     * @return
     */
    String getNameByCode(String code);

    /**
     * 获取自发币列表
     *
     * @return 自发币列表
     */
    List<TradePairRespDTO> getCustomTradePair();

    /**
     * 获取非24小时交易的交易对
     *
     * @return 非24小时交易的交易对列表
     */
    List<TradePairRespDTO> getNot24HourTradePairs();

    /**
     * 获取引用交易对的复制币信息
     *
     * @param referenceCode 引用交易对
     * @return 复制币信息
     */
    List<TradePairRespDTO> getCopyTradeOfReference(String referenceCode);
}
