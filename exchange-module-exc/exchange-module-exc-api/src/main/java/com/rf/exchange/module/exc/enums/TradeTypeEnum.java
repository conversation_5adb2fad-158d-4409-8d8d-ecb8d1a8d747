package com.rf.exchange.module.exc.enums;

import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 交易对的交易类型
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@AllArgsConstructor
@Getter
public enum TradeTypeEnum implements IntArrayValuable {
    SPOT(0, "现货"),
    FUTURES(1, "期货合约"),
    MARGIN(2, "杠杆保证金"),
    PERIOD(3, "限时"); // 实际没有这种限时交易类型，所以这种交易类型对行情数据使用现货的行情数据

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradeTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
