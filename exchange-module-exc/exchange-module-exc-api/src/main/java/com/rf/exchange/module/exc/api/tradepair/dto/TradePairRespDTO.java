package com.rf.exchange.module.exc.api.tradepair.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
public class TradePairRespDTO implements Serializable {
    @Serial
    private final static long serialVersionUID = 1L;
    /**
     * 交易对编号
     */
    private Long id;
    /**
     * 交易对名称
     */
    private String name;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 基础资产
     */
    private String baseAsset;
    /**
     * 报价资产
     */
    private String quoteAsset;
    /**
     * 交易类型 0:现货 1:合约期货 2:杠杆保证金
     */
    private Integer tradeType;
    /**
     * 资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇
     */
    private Integer assetType;
    /**
     * 开启状态（0正常 1停用）
     */
    private Integer status;
    /**
     * 数据同步状态 (0正常 1停用)
     */
    private Integer syncStatus;
    /**
     * 三方数据源 0:polygon 1:alltick
     */
    private Integer source;
    /**
     * 价格保留小数位数
     */
    private Integer scale;
    /**
     * 标记价格
     */
    private String markPrice;
    /**
     * 最新价格
     */
    private String currentPrice = "";
    /**
     * 涨跌幅百分比
     */
    private String percentage = "0.00";
    /**
     * 每分钟成交额
     */
    private BigDecimal oneMinuteTurnover;

    /**
     * 参考币种
     */
    private String referenceCode;

    /**
     * 发行价格
     */
    private BigDecimal issuedPrice;

    /**
     * k线开始时间
     */
    private Long candleStartTime;

    /**
     * k线当前时间
     */
    private Long candleTime;

    /**
     * 是否自发币
     */
    private Boolean isCustom = false;

    /**
     * 是否复制
     */
    private Boolean isCopy=false;

    /**
     * 交易对图标
     */
    private String icon;

    /**
     * 是否是全天24小时交易对的交易对
     * 加密货币主要是全天24小时交易的交易对，
     * 其他类型的交易对则不是全天交易
     */
    private Boolean is24Hour;

    /**
     * 夏令时的交易时间
     */
    private Long transactionTimeDstId;

    /**
     * 冬令时的交易时间
     */
    private Long transactionTimeWinterId;
}
