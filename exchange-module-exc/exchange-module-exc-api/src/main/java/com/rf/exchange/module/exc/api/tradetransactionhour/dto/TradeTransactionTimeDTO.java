package com.rf.exchange.module.exc.api.tradetransactionhour.dto;

import com.rf.exchange.module.exc.enums.TransactionSeasonEnum;
import lombok.Data;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @since 2024-10-03
 */
@Data
public class TradeTransactionTimeDTO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 时令
     * {@link TransactionSeasonEnum}
     */
    private String season;
    /**
     * 周几开市
     * {@link java.util.Calendar 中定义的星期}
     */
    private String dayOfWeekOpen;
    /**
     * 开市时间
     */
    private LocalTime marketOpen;
    /**
     * 周几休市
     */
    private String dayOfWeekClose;
    /**
     * 休市时间
     */
    private LocalTime marketClose;
    /**
     * 是否有日内时间
     */
    private Boolean hasDailyBreak;
    /**
     * 日内开市时间
     */
    private LocalTime dailyBreakStart;
    /**
     * 日内休市时间
     */
    private LocalTime dailyBreakEnd;
    /**
     * 时区
     */
    private String timezone;

}
