<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange-module-exc</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>exchange-module-exc-api</artifactId>
    <packaging>jar</packaging>
    <description>
        交易所通用业务 模块 API，暴露给其它模块调用
    </description>

    <dependencies>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-common</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>