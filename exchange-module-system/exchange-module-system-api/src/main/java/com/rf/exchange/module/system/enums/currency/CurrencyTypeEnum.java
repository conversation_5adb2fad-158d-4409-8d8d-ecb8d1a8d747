package com.rf.exchange.module.system.enums.currency;

import cn.hutool.core.util.ObjUtil;
import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024-06-19
 */
@Getter
@AllArgsConstructor
public enum CurrencyTypeEnum implements IntArrayValuable {
    /**
     * 法币
     */
    FIAT(0),
    /**
     * 加密货币
     */
    CRYPTO(1);

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CurrencyTypeEnum::getType).toArray();

    private final Integer type;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static boolean isFiat(Integer type) {
        return ObjUtil.equal(FIAT.type, type);
    }

    public static boolean isCrypto(Integer type) {
        return ObjUtil.equal(CRYPTO.type, type);
    }
}
