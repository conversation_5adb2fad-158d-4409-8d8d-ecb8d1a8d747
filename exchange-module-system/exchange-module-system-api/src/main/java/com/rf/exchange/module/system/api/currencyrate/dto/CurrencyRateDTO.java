package com.rf.exchange.module.system.api.currencyrate.dto;

import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2024-07-11
 */
@Data
public class CurrencyRateDTO {
    /**
     * 基础币种
     */
    private String baseCurrency;
    /**
     * 报价币种
     */
    private String quoteCurrency;
    /**
     * 实时汇率
     */
    private BigDecimal rate;
    /**
     * 固定汇率
     */
    private String fixedRate = "0";

    /**
     * 获取汇率值
     * 如果有固定汇率则使用固定汇率
     *
     * @return 汇率值
     */
    public BigDecimal getRateValue() {
        if (!fixedRate.equals("0")) {
            return new BigDecimal(fixedRate).setScale(2, RoundingMode.HALF_UP);
        }
        return rate.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取格式化的汇率值
     *
     * @return 格式化之后的汇率值
     */
    public String getFormattedRate() {
        return DecimalFormatUtil.formatWithScale(getRateValue(), 2);
    }
}
