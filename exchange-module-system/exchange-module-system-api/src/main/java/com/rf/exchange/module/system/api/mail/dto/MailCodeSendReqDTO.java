package com.rf.exchange.module.system.api.mail.dto;

import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 邮箱验证码的发送 Request DTO
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Data
public class MailCodeSendReqDTO {
    /**
     * 收件人的用户id
     */
    private Long toUserId;
    /**
     * 收件人的用户名
     */
    private String toUsername;
    /**
     * 收件人的用户类型
     * <p>
     * {@link UserTypeEnum}
     */
    private Integer toUserType;
    /**
     * 邮箱
     */
    @Email
    @NotEmpty(message = "邮箱不能为空")
    private String email;
    /**
     * 发送场景
     */
    @NotNull(message = "发送场景不能为空")
    @InEnum(MailSceneEnum.class)
    private Integer scene;
    /**
     * 发送 IP
     */
    @NotEmpty(message = "发送 IP 不能为空")
    private String createIp;
    /**
     * 发送邮件的租户id
     */
    @NotNull(message = "发送邮件的租户id不能为空")
    private Long tenantId;
    /**
     * 语言
     */
    @NotNull(message = "语言")
    private String lang;

    /**
     * 请求发送邮箱的域名
     */
    private String domain;
}
