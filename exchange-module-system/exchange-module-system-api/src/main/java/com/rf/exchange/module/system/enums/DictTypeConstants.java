package com.rf.exchange.module.system.enums;

/**
 * System 字典类型的枚举类
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    String USER_TYPE = "user_type"; // 用户类型
    String COMMON_STATUS = "common_status"; // 系统状态

    // ========== SYSTEM 模块 ==========

    String USER_SEX = "system_user_sex"; // 用户性别

    String LOGIN_TYPE = "system_login_type"; // 登录日志的类型
    String LOGIN_RESULT = "system_login_result"; // 登录结果

    String ERROR_CODE_TYPE = "system_error_code_type"; // 错误码的类型枚举

    String SMS_CHANNEL_CODE = "system_sms_channel_code"; // 短信渠道编码
    String SMS_TEMPLATE_TYPE = "system_sms_template_type"; // 短信模板类型
    String SMS_SEND_STATUS = "system_sms_send_status"; // 短信发送状态
    String SMS_RECEIVE_STATUS = "system_sms_receive_status"; // 短信接收状态

    String SUPPORTED_AREA = "supported_area"; // 支持的国家地区
    String AREA_INFO = "area_info"; // 支持运营国家地区的配置信息，如法定货币，手机国家码
    String ALLTICK_CODE_LIST = "code_list_alltick"; // AllTick的产品列表
    String JIANG_SHAN_CODE_LIST = "code_list_jiang_shan"; // 匠山的产品列表

    String DEFAULT_CURRENCY_CODE = "USD";

    // ============ 租户字典 ==============
    String TENANT_CURRENCY_RATE = "tenant_currency_rate"; // 租户的汇率配置
    String TENANT_TIME_CONTRACT_LOSE = "time_contract_lose"; // 租户的限时合约订单亏损比例
    String TENANT_DEFAULT_LANGUAGE = "tenant_default_language"; // 租户的默认语言

    /**
     * 提现配置key
     */
    String TENANT_WITHDRAW_CONFIG = "withdraw_config"; // 租户的提现配置
    /**
     * 充值配置key
     */
    String TENANT_RECHARGE_CONFIG = "recharge_config"; // 租户充值配置

    /**
     * 认证状态配置，只有认证了才能做什么
     */
    String TENANT_AUTH_CONFIG = "auth_config"; // 租户充值配置

    /**
     * 币种配置
     */
    String TENANT_CURRENCY_CONFIG = "currency_config";
    String TENANT_CURRENCY_CONFIG_LABEL_BASE_CURRENCY = "base_currency"; //

    String GOOGLE_SECRET_CONFIG = "google_secret_config";
}
