package com.rf.exchange.module.system.api.tenantdict;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.number.NumberUtils;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantAuthConfigRespDTO;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantDictDataRespDTO;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantRechargeConfigRespDTo;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantWithdrawConfigRespDTo;
import com.rf.exchange.module.system.enums.DictTypeConstants;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.util.collection.CollectionUtils.convertList;
import static com.rf.exchange.module.system.enums.DictTypeConstants.TENANT_CURRENCY_CONFIG_LABEL_BASE_CURRENCY;

/**
 * 租户字典 API
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
public interface TenantDictDataApi {
    /**
     * 校验字典数据们是否有效。如下情况，视为无效： 1. 字典数据不存在 2. 字典数据被禁用
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @param values   字典数据值的数组
     */
    void validateDictDataList(long tenantId, String dictType, Collection<String> values);

    /**
     * 获得指定的字典数据，从缓存中
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @param value    字典数据值
     * @return 字典数据
     */
    TenantDictDataRespDTO getDictData(long tenantId, String dictType, String value);

    /**
     * 获得指定的字典标签，从缓存中
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @param value    字典数据值
     * @return 字典标签
     */
    default String getDictDataLabel(long tenantId, String dictType, Integer value) {
        TenantDictDataRespDTO dictData = getDictData(tenantId, dictType, String.valueOf(value));
        if (ObjUtil.isNull(dictData)) {
            return StrUtil.EMPTY;
        }
        return dictData.getLabel();
    }

    /**
     * 解析获得指定的字典数据，从缓存中
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @param label    字典数据标签
     * @return 字典数据
     */
    TenantDictDataRespDTO parseDictData(long tenantId, String dictType, String label);

    String parseDictDataStringValue(long tenantId, String dictType, String label);

    int parseDictDataIntValue(long tenantId, String dictType, String label);

    double parseDictDataDoubleValue(long tenantId, String dictType, String label);

    boolean parseDictDataBoolValue(long tenantId, String dictType, String label);

    /**
     * 获得指定字典类型的字典数据列表
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<TenantDictDataRespDTO> getDictDataList(long tenantId, String dictType);

    /**
     * 获得字典数据标签列表
     *
     * @param tenantId 租户编号
     * @param dictType 字段类型
     * @return 字典数据标签列表
     */
    default List<String> getDictDataLabelList(long tenantId, String dictType) {
        List<TenantDictDataRespDTO> list = getDictDataList(tenantId, dictType);
        return convertList(list, TenantDictDataRespDTO::getLabel);
    }

    /**
     * 获取租户默认币种
     *
     * @param tenantId
     * @return
     */
    default String getTenantCurrencyCode(Long tenantId) {
        List<TenantDictDataRespDTO> currencyList = getDictDataList(tenantId, DictTypeConstants.TENANT_CURRENCY_CONFIG);
        Map<String, String> currencyMap = currencyList.parallelStream()
                .collect(Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));
        // final String fundsCurrency = currencyMap.get("funds_currency") == null ? "" :
        // currencyMap.get("funds_currency");//出入库金币种
        final String baseCurrency = currencyMap.get(TENANT_CURRENCY_CONFIG_LABEL_BASE_CURRENCY);
        return baseCurrency == null ? DictTypeConstants.DEFAULT_CURRENCY_CODE : baseCurrency;// 计价币种;
    }

    /**
     * 获取租户的认证配置
     *
     * @param tenantId
     * @return
     */
    default TenantAuthConfigRespDTO getTenantAuthConfig(Long tenantId) {
        List<TenantDictDataRespDTO> list = getDictDataList(tenantId, DictTypeConstants.TENANT_AUTH_CONFIG);
        Map<String, String> map = list.parallelStream()
                .collect(Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));
        boolean timeContract = map.getOrDefault("time_contract", "1").equals("1");
        boolean withdraw = map.getOrDefault("withdraw", "1").equals("1");
        boolean recharge = map.getOrDefault("recharge", "1").equals("1");
        return new TenantAuthConfigRespDTO(recharge, withdraw, timeContract);
    }

    /**
     * 获取租户提现配置
     *
     * @param tenantId
     * @return
     */
    default TenantWithdrawConfigRespDTo getTenantWithdrawConfig(Long tenantId) {
        List<TenantDictDataRespDTO> list = getDictDataList(tenantId, DictTypeConstants.TENANT_WITHDRAW_CONFIG);
        Map<String, String> map = list.parallelStream()
                .collect(Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));
        BigDecimal max = NumberUtil.toBigDecimal(map.getOrDefault("max", "999999"));// 最多提现金额
        BigDecimal min = NumberUtil.toBigDecimal(map.getOrDefault("min", "1"));// 最少提现金额
        BigDecimal feeMax = NumberUtil.toBigDecimal(map.getOrDefault("fee_max", "0"));// 封顶手续费
        BigDecimal feeMin = NumberUtil.toBigDecimal(map.getOrDefault("fee_min", "0"));// 最低手续费
        BigDecimal feeScale = NumberUtil.toBigDecimal(map.getOrDefault("fee_scale", "0"));// 手续费比例
        BigDecimal feeFix = NumberUtil.toBigDecimal(map.getOrDefault("fee_fix", "0"));// 固定手续费
        int maxProcess = NumberUtils.parseInt(map.getOrDefault("max_process", "1"));// 提现处理中最多几笔
        int createWithWallet = NumberUtils.parseInt(map.getOrDefault("use_wallet", "1"));// 提现是否使用钱包
        return TenantWithdrawConfigRespDTo.builder().max(max).min(min).feeMax(feeMax).feeMin(feeMin).feeScale(feeScale).useWallet(createWithWallet == 1)
                .feeFix(feeFix).maxProcess(maxProcess).build();
    }

    /**
     * 获取租户充值配置
     *
     * @param tenantId
     * @return
     */
    default TenantRechargeConfigRespDTo getTenantRechargeConfig(Long tenantId) {
        List<TenantDictDataRespDTO> list = getDictDataList(tenantId, DictTypeConstants.TENANT_RECHARGE_CONFIG);
        Map<String, String> map = list.parallelStream()
                .collect(Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));
        int count = NumberUtil.parseInt(map.getOrDefault("max_process", "1"), 1);
        return new TenantRechargeConfigRespDTo(count);
    }

    /**
     * 获取租户-是否开启谷歌密钥
     *
     * @param tenantId
     * @return
     */

    default Boolean getTenantGoogleSecretConfig(Long tenantId) {
        List<TenantDictDataRespDTO> list = getDictDataList(tenantId, DictTypeConstants.GOOGLE_SECRET_CONFIG);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        } else {
            Map<String, String> map = list.parallelStream()
                    .collect(Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));
            return Boolean.valueOf(map.getOrDefault("enable", "false"));
        }
    }
}
