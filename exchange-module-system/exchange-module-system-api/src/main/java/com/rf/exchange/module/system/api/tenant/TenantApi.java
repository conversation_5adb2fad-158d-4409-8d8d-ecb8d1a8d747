package com.rf.exchange.module.system.api.tenant;

import com.rf.exchange.module.system.api.tenant.dto.TenantRespDTO;

import java.util.List;
import java.util.Map;

/**
 * 多租户的 API 接口
 *
 * <AUTHOR>
 */
public interface TenantApi {

    /**
     * 获得所有租户
     *
     * @return 租户编号数组
     */
    List<Long> getTenantIdList();

    /**
     * 获取所有租户
     *
     * @return 租户列表
     */
    List<TenantRespDTO> getTenantList();

    /**
     * 校验租户是否合法
     *
     * @param id 租户编号
     */
    void validateTenant(Long id);

    /**
     * 获取域名和租户id的映射
     *
     * @return 域名和租户id的映射
     */
    Map<String, Long> getTenantIdServerNameMap();
}
