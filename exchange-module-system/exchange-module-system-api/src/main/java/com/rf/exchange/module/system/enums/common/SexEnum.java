package com.rf.exchange.module.system.enums.common;

import cn.hutool.core.util.ArrayUtil;
import com.rf.exchange.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 性别的枚举值
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SexEnum implements IntArrayValuable {

    /** 男 */
    MALE(1),
    /** 女 */
    FEMALE(2),
    /* 未知 */
    UNKNOWN(3);

    /**
     * 性别
     */
    private final Integer sex;
    @Override
    public int[] array() {
        return ARRAYS;
    }
    public static SexEnum get(int value) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getSex().equals(value),
                values());
    }
    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(SexEnum::getSex).toArray();
}
