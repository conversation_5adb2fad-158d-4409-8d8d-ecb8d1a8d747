package com.rf.exchange.module.system.api.currencyrate;

import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;

import java.math.BigDecimal;
import java.util.Collection;

/**
 * 系统汇率 API
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
public interface CurrencyRateApi {

    /**
     * 获取所有的系统汇率
     * 不区分租户
     *
     * @return 汇率列表
     */
    Collection<CurrencyRateDTO> getCurrencyRateList();

    /**
     * 获取租户的所有系统汇率
     *
     * @param tenantId 租户编号
     * @return 汇率列表
     */
    Collection<CurrencyRateDTO> getTenantCurrencyRateList(Long tenantId);

    /**
     * 获取租户指定基础货币和报价货币的汇率
     * @param tenantId 租户id
     * @param baseCurrency 基础货币
     * @param quoteCurrency 报价货币
     * @return 汇率信息 baseCurrency/quoteCurrency
     */
    CurrencyRateDTO getTenantCurrencyRate(Long tenantId, String baseCurrency, String quoteCurrency);

    /**
     * 获取租户指定的法币和美金之间的汇率
     *
     * @param tenantId      租户id
     * @param quoteCurrency 基础货币
     * @return USD/quoteCurrency 的汇率信息
     */
    BigDecimal getTenantCurrencyRateToUSD(Long tenantId, String quoteCurrency);

    /**
     * 更新汇率信息
     *
     * @param dtoList 汇率信息列表
     */
    void updateCurrencyRateList(Collection<CurrencyRateDTO> dtoList);

    /**
     * 更新汇率信息
     *
     * @param dto 汇率信息
     */
    void updateCurrencyRate(CurrencyRateDTO dto);

    /**
     * 获取租户的默认币种汇率
     *
     * @param tenantId 租户id
     * @param srcCurrencyCode 法币币种
     * @return 汇率
     */
    BigDecimal getTenantCurrencyRate(Long tenantId, String srcCurrencyCode);
}
