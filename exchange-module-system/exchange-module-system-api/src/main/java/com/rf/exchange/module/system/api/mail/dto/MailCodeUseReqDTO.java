package com.rf.exchange.module.system.api.mail.dto;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 邮箱验证码的校验 Request DTO
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Data
public class MailCodeUseReqDTO {

    /**
     * 手机号
     */
    @Email
    @NotEmpty(message = "邮箱不能为空")
    private String email;
    /**
     * 发送场景
     */
    @NotNull(message = "发送场景不能为空")
    @InEnum(MailSceneEnum.class)
    private Integer scene;
    /**
     * 验证码
     */
    @NotEmpty(message = "验证码")
    private String code;

    @NotEmpty(message = "使用 IP 不能为空")
    private String usedIp;
}
