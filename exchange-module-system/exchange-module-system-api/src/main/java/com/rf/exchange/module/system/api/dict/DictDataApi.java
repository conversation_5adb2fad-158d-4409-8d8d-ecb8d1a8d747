package com.rf.exchange.module.system.api.dict;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.validation.ImageUrl;
import com.rf.exchange.framework.i18n.I;
import com.rf.exchange.module.system.api.dict.dto.DictDataRespDTO;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static com.rf.exchange.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 字典数据 API 接口
 *
 * <AUTHOR>
 */
public interface DictDataApi {

    /**
     * 校验字典数据们是否有效。如下情况，视为无效：
     * 1. 字典数据不存在
     * 2. 字典数据被禁用
     *
     * @param dictType 字典类型
     * @param values   字典数据值的数组
     */
    void validateDictDataList(String dictType, Collection<String> values);

    /**
     * 获得指定的字典数据，从缓存中
     *
     * @param type  字典类型
     * @param value 字典数据值
     * @return 字典数据
     */
    DictDataRespDTO getDictData(String type, String value);

    /**
     * 获得指定的字典标签，从缓存中
     *
     * @param type  字典类型
     * @param value 字典数据值
     * @return 字典标签
     */
    default String getDictDataLabel(String type, Integer value) {
        DictDataRespDTO dictData = getDictData(type, String.valueOf(value));
        if (ObjUtil.isNull(dictData)) {
            return StrUtil.EMPTY;
        }
        return dictData.getLabel();
    }

    /**
     * 解析获得指定的字典数据，从缓存中
     *
     * @param type  字典类型
     * @param label 字典数据标签
     * @return 字典数据
     */
    DictDataRespDTO parseDictData(String type, String label);

    /**
     * 获得指定字典类型的字典数据列表
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<DictDataRespDTO> getDictDataList(String dictType);

    /**
     * 获得字典数据标签列表
     *
     * @param dictType 字典类型
     * @return 字典数据标签列表
     */
    default List<String> getDictDataLabelList(String dictType) {
        List<DictDataRespDTO> list = getDictDataList(dictType);
        return convertList(list, DictDataRespDTO::getLabel);
    }

    /**
     * 填充s3主机头
     *
     * @param list
     * @param <T>
     */
    default <T> void fillS3Host(List<T> list, Class<T> tClass) {
        DictDataRespDTO data = parseDictData("url_config", "s3host");
        if (data == null) return;
        //通过反射查找需要添加前缀的url
        List<Field> fieldList = new ArrayList<>();
        Field[] fields = tClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ImageUrl.class)) {
                fieldList.add(field);
            }
        }
        for (T t : list) {
            if(t==null)continue;
            for (Field f : fieldList) {
                try {
                    f.setAccessible(true);
                    Object fieldValue = f.get(t);
                    String content =fieldValue.toString();
                    if (StrUtil.isNotEmpty(content) && !content.startsWith("http")) {
                        f.set(t, data.getValue() + content);
                    }
                    //word.add(fieldValue.toString());
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 填充s3主机头
     *
     * @param t
     * @param <T>
     */
    default <T> void fillS3Host(T t, Class<T> tClass) {
        if(t==null)return;
        DictDataRespDTO data = parseDictData("url_config", "s3host");
        if (data == null) return;
        //通过反射查找需要添加前缀的url
        List<Field> fieldList = new ArrayList<>();
        Field[] fields = tClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ImageUrl.class)) {
                fieldList.add(field);
            }
        }
        for (Field f : fieldList) {
            try {
                f.setAccessible(true);
                Object fieldValue = f.get(t);
                String content = I.n(fieldValue.toString());
                if (content != null && !content.startsWith("http")) {
                    f.set(t, data.getValue() + content);
                }
                //word.add(fieldValue.toString());
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }
}
