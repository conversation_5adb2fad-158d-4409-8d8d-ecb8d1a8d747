package com.rf.exchange.module.system.enums.banner;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import com.rf.exchange.framework.common.core.IntArrayValuable;
import com.rf.exchange.module.system.enums.currency.CurrencyTypeEnum;
import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Arrays;
@Getter
@AllArgsConstructor
public enum BannerLinkTypeEnum implements IntArrayValuable {

    INNER_LINK(1, "内网跳转"),
    EXTRANET_LINK(2, "外网跳转")
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(BannerLinkTypeEnum::getValue).toArray();

    /**
     * 场景
     */
    private final Integer value;
    /**
     * 描述
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static BannerLinkTypeEnum getCodeByScene(Integer scene) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getValue().equals(scene),
                values());
    }
}
