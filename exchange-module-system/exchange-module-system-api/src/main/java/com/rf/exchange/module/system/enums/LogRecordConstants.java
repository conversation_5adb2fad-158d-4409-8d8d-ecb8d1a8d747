package com.rf.exchange.module.system.enums;

/**
 * System 操作日志枚举
 * 目的：统一管理，也减少 Service 里各种“复杂”字符串
 *
 * <AUTHOR>
 */
public interface LogRecordConstants {

    // ======================= SYSTEM_USER 用户 =======================

    String SYSTEM_USER_TYPE = "SYSTEM 用户";
    String SYSTEM_USER_CREATE_SUB_TYPE = "创建用户";
    String SYSTEM_USER_CREATE_SUCCESS = "创建了用户【{{#user.nickname}}】";
    String SYSTEM_USER_UPDATE_SUB_TYPE = "更新用户";
    String SYSTEM_USER_UPDATE_SUCCESS = "更新了用户【{{#user.nickname}}】: {_DIFF{#updateReqVO}}";
    String SYSTEM_USER_DELETE_SUB_TYPE = "删除用户";
    String SYSTEM_USER_DELETE_SUCCESS = "删除了用户【{{#user.nickname}}】";
    String SYSTEM_USER_UPDATE_PASSWORD_SUB_TYPE = "重置用户密码";
    String SYSTEM_USER_UPDATE_PASSWORD_SUCCESS = "将用户【{{#user.nickname}}】的密码从 重置为【{{#newPassword}}】";

    // ======================= SYSTEM_ROLE 角色 =======================

    String SYSTEM_ROLE_TYPE = "SYSTEM 角色";
    String SYSTEM_ROLE_CREATE_SUB_TYPE = "创建角色";
    String SYSTEM_ROLE_CREATE_SUCCESS = "创建了角色【{{#role.name}}】";
    String SYSTEM_ROLE_UPDATE_SUB_TYPE = "更新角色";
    String SYSTEM_ROLE_UPDATE_SUCCESS = "更新了角色【{{#role.name}}】: {_DIFF{#updateReqVO}}";
    String SYSTEM_ROLE_DELETE_SUB_TYPE = "删除角色";
    String SYSTEM_ROLE_DELETE_SUCCESS = "删除了角色【{{#role.name}}】";

    // ====================== SYSTEM_AGENT 代理 =======================
    String SYSTEM_AGENT_TYPE = "SYSTEM 代理";
    String SYSTEM_AGENT_CREATE_SUB_TYPE = "创建代理";
    String SYSTEM_AGENT_CREATE_SUCCESS = "创建代理【{{#agent.name}}】【{{#agent.code}}】";
    String SYSTEM_AGENT_UPDATE_SUB_TYPE = "更新代理";
    String SYSTEM_AGENT_UPDATE_SUCCESS = "更新了代理【{{#agent.id}}】: {_DIFF{#updateReqVO}}";
    String SYSTEM_AGENT_UPDATE_STATUS_SUB_TYPE = "更新代理状态";
    String SYSTEM_AGENT_UPDATE_STATUS_SUCCESS = "更新了代理状态 【{{#agent.id}}】status:【{{#agent.status}}】";
    String SYSTEM_AGENT_DELETE_SUB_TYPE = "删除代理";
    String SYSTEM_AGENT_DELETE_SUCCESS = "删除了代理【{{#agent.name}}】";
    String SYSTEM_AGENT_MOVE_SUB_TYPE = "移动代理";
    String SYSTEM_AGENT_MOVE_SUCCESS = "移动代理 【{{#agentName}}】旧父代理:【{{#oldAncestor}}】新父代理:【{{#newAncestor}}】";
}
