package com.rf.exchange.module.system.api.tenant.dto;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
public class TenantRespDTO {
    /**
     * 租户编号，自增
     */
    private Long id;
    /**
     * 租户码，唯一
     */
    private String code;
    /**
     * 租户名
     */
    private String name;
    /**
     * 联系人的用户编号
     */
    private Long contactUserId;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系手机
     */
    private String contactMobile;
    /**
     * 租户状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
 
    /**
     * 租户套餐编号
     */
    private Long packageId;
    /**
     * 过期时间
     */
    private Long expireTime;
    /**
     * 账号数量
     */
    private Integer accountCount;
}
