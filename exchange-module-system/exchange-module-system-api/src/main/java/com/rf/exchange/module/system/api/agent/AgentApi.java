package com.rf.exchange.module.system.api.agent;

import com.rf.exchange.module.system.api.agent.dto.AgentBaseRespDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @since 2024-06-08
 */
public interface AgentApi {

    AgentBaseRespDTO getAgentByCode(String code);

    AgentBaseRespDTO getAgent(Long id);

    /**
     * 获取指定租户的默认代理
     *
     * @return 租户的默认代理
     */
    AgentBaseRespDTO getDefault(long tenantId);

    Map<Long, AgentBaseRespDTO> getAncestorListByIds(Set<Long> idList);

    /**
     * 获取父代理下的所有下级代理
     *
     * @param ancestor 父代理
     * @return 代理列表
     */
    List<AgentBaseRespDTO> getAllDescendants(Long ancestor);

    /**
     * 获取租户根代理下的所有代理
     *
     * @param agentId 代理id
     */
    AgentBaseRespDTO getAgentChainRoot(Long agentId);

    /**
     * 获取指定子代理的所有祖先代理(即上级代理)
     *
     * @param agentId 子代理id
     * @return 子代理的所有上级代理
     */
    TreeMap<Long, AgentBaseRespDTO> getAncestorMapOfAgent(Long agentId);

    /**
     * 获取指定代理id集合的代理信息
     * @param ids 代理id集合
     * @return 代理信息的map key:代理id
     */
    Map<Long, AgentBaseRespDTO> getAgentsByIds(Set<Long> ids);

    /**
     * 获取会员修改代理时可用的新父代理列表
     * @param agentId 会员的代理id
     * @return 代理列表
     */
    List<AgentBaseRespDTO> getUserMoveAvailableAgents(Long agentId);
}
