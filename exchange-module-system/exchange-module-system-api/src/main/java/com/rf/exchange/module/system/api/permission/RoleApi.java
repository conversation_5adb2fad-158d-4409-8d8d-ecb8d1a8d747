package com.rf.exchange.module.system.api.permission;

import java.util.Collection;

/**
 * 角色 API 接口
 *
 * <AUTHOR>
 */
public interface RoleApi {

    /**
     * 校验角色们是否有效。如下情况，视为无效：
     * 1. 角色编号不存在
     * 2. 角色被禁用
     *
     * @param ids 角色编号数组
     */
    void validRoleList(Collection<Long> ids);

    /**
     * 角色列表中是否有超级管理员
     *
     * @param roleIds 角色id列表
     * @return 是否是超级管理员
     */
    boolean hasAnySuperAdminOf(Collection<Long> roleIds);

    /**
     * 角色列表中是否有任何租户管理员
     *
     * @param roleIds 角色id列表
     * @return 是否是租户管理员
     */
    boolean hasAnyTenantAdminOf(Collection<Long> roleIds);
}
