package com.rf.exchange.module.system.service.miner;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductReqVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductCreateOrderReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerRedemptionOrderReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductOrder;

public interface MinerService {

    /**
     * 创建挖矿订单
     * @param reqVO 下单参数
     * @return boolean
     */
    Boolean createOrder(MinerProductCreateOrderReqVo reqVO);

    /**
     * 赎回挖矿订单
     * @param reqVO 下单参数
     * @return boolean
     */
    Boolean redemptionOrder(MinerRedemptionOrderReqVo reqVO);


    /**
     * 创建挖矿产品
     * @param reqVO reqVO
     * @return boolean
     */
    Boolean createMinerProduct(MinerProductReqVO reqVO);

    /**
     * 编辑挖矿产品
     * @param reqVO reqVO
     * @return boolean
     */
    Boolean updateMinerProduct(MinerProductReqVO reqVO);

    /**
     *
     * @param id 数据ID
     * @return boolean
     */
    Boolean deleteMinerProduct(Long id);

    /**
     * 获取挖矿产品-分野
     *
     * @param pageReqVO 分页参数
     * @return 分页信息
     */
    PageResult<MinerProduct> getMinerPage(MinerProductPageReqVO pageReqVO);

    /**
     * 获取挖矿产品-分野
     *
     * @param pageReqVO 分页参数
     * @return 分页信息
     */
    PageResult<MinerProductOrder> getMinerOrders(MinerOrderPageReqVo pageReqVO);


}
