package com.rf.exchange.module.system.controller.admin.agreement.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 协议管理分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Schema(description = "管理后台 - 协议管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AgreementPageReqVO extends PageParam {

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @Schema(description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明", example = "1")
    private Integer type;

    @Schema(description = "协议标题", example = "隐私协议")
    private String title;

    @Schema(description = "状态：0-禁用 1-启用", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
