package com.rf.exchange.module.system.controller.admin.agreement;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementPageReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementRespVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.service.agreement.AgreementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 协议管理
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Tag(name = "管理后台 - 协议管理")
@RestController
@RequestMapping("/system/agreement")
@Validated
public class AgreementController {

    @Resource
    private AgreementService agreementService;

    @PostMapping("/create")
    @Operation(summary = "创建协议管理")
    @PreAuthorize("@ss.hasPermission('system:agreement:create')")
    public CommonResult<Long> createAgreement(@Valid @RequestBody AgreementSaveReqVO createReqVO) {
        return success(agreementService.createAgreement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新协议管理")
    @PreAuthorize("@ss.hasPermission('system:agreement:update')")
    public CommonResult<Boolean> updateAgreement(@Valid @RequestBody AgreementSaveReqVO updateReqVO) {
        agreementService.updateAgreement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除协议管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:agreement:delete')")
    public CommonResult<Boolean> deleteAgreement(@RequestParam("id") Long id) {
        agreementService.deleteAgreement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得协议管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:agreement:query')")
    public CommonResult<AgreementRespVO> getAgreement(@RequestParam("id") Long id) {
        AgreementDO agreement = agreementService.getAgreement(id);
        return success(BeanUtils.toBean(agreement, AgreementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得协议管理分页")
    @PreAuthorize("@ss.hasPermission('system:agreement:query')")
    public CommonResult<PageResult<AgreementRespVO>> getAgreementPage(@Valid AgreementPageReqVO pageReqVO) {
        PageResult<AgreementDO> pageResult = agreementService.getAgreementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgreementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出协议管理 Excel")
    @PreAuthorize("@ss.hasPermission('system:agreement:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAgreementExcel(@Valid AgreementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgreementDO> list = agreementService.getAgreementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "协议管理.xls", "数据", AgreementRespVO.class,
                        BeanUtils.toBean(list, AgreementRespVO.class));
    }
}
