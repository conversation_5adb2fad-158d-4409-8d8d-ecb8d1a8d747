package com.rf.exchange.module.system.controller.admin.miner.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 挖矿产品创建-编辑参数")
@Data
public class MinerProductRespVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "文字内容")
    @NotEmpty(message = "标题不能为空")
    private String title;

    @Schema(description = "周期", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    @NotNull(message = "周期不能为空")
    private Integer cycle;

    @Schema(description = "图片", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    @NotEmpty(message = "图片不能为空")
    private String img;

    @Schema(description = "最小收益率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.5")
    @ExcelProperty("最小收益率")
    @NotNull(message = "最小收益率不能为空")
    private BigDecimal minProfit;

    @Schema(description = "最大收益率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.5")
    @ExcelProperty("最大收益率")
    @NotNull(message = "最大收益率不能为空")
    private BigDecimal maxProfit;

    @Schema(description = "最低购买金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    @ExcelProperty("最低购买金额")
    @NotNull(message = "最低购买金额不能为空")
    private BigDecimal minPurchase;

    @Schema(description = "最大购买金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "500")
    @ExcelProperty("最大购买金额")
    @NotNull(message = "最大购买金额不能为空")
    private BigDecimal maxPurchase;

    @Schema(description = "提前赎回违约金百分比", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.2")
    @ExcelProperty("提前赎回违约金百分比")
    @NotNull(message = "提前赎回违约金百分比不能为空")
    private BigDecimal liquidatedDamagesRatio;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    @NotNull(message = "排序不能为空")
    private Integer sort;
}
