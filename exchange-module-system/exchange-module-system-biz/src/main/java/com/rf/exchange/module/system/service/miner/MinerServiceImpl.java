package com.rf.exchange.module.system.service.miner;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.common.util.order.OrderUtil;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.member.api.balance.MemberBalanceApi;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceDTO;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.MemberUserApi;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductReqVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductCreateOrderReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerRedemptionOrderReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductOrder;
import com.rf.exchange.module.system.dal.mysql.miner.MinerProductMapper;
import com.rf.exchange.module.system.dal.mysql.miner.MinerProductOrderMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * 短信渠道 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MinerServiceImpl implements MinerService {

    @Resource
    @Lazy
    private MemberUserApi memberUserApi;

    @Resource
    @Lazy
    private MemberBalanceApi memberBalanceApi;

    @Resource
    private MinerProductMapper minerProductMapper;

    @Resource
    private MinerProductOrderMapper minerProductOrderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOrder(MinerProductCreateOrderReqVo reqVO) {
        MinerProduct product = minerProductMapper.selectById(reqVO.getProductId());
        if (product == null) {
            throw exception(MINER_NOT_EXISTS);
        }
        //判断最低消费金额
        if (reqVO.getAmount().compareTo(product.getMinPurchase()) < 0) {
            throw exception(MINER_AMOUNT_LIMIT);
        }
        if (product.getCycle()==null || product.getCycle()<1) {
            throw exception(MINER_CYCLE_ERR);
        }
        //判断用户金额
        Long userId = getLoginUserId();
        final MemberUserRespDTO user = memberUserApi.checkUserExists(userId);
        // 判断用户余额是否足够 (需要大于保证金加上手续费)
        memberBalanceApi.checkUSDTBalanceEnough(userId, reqVO.getAmount());
        String orderNo = OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.UPDATE_BALANCE);

        //创建业务订单
        MinerProductOrder insert = new MinerProductOrder();
        insert.setCycle(product.getCycle());
        insert.setUsername(user.getUsername());
        insert.setUserId(userId);
        insert.setOrderAmount(reqVO.getAmount());
        insert.setOrderIncome(BigDecimal.ZERO);
        insert.setOrderStatus(1);
        insert.setProductId(product.getId());
        insert.setOrderNo(orderNo);
        insert.setLiquidatedDamages(BigDecimal.ZERO);
        insert.setLiquidatedDamagesRatio(product.getLiquidatedDamagesRatio());
        long expireTime = DateUtils.getUnixTimestampNow() + product.getCycle() * 24L * 60 * 60 * 1000;
        insert.setExpireTime(expireTime);

        minerProductOrderMapper.insert(insert);

        //添加账变信息
        MemberBalanceUpdateReqVO balanceUpdate = new MemberBalanceUpdateReqVO();
        balanceUpdate.setUserId(user.getId());
        balanceUpdate.setAmount(reqVO.getAmount());
        MemberTransactionSaveReqVO transactionSave = new MemberTransactionSaveReqVO();
        transactionSave.setUserId(user.getId());
        transactionSave.setTransactionType(MemberTransactionsTypeEnum.MINING_ORDER);
        transactionSave.setRemark(MemberTransactionsTypeEnum.MINING_ORDER.getLabel());
        transactionSave.setBizOrderNo(orderNo);
        transactionSave.setAmountCurrency("USDT");
        transactionSave.setTenantId(user.getTenantId());
        MemberBalanceUpdateContext context = new MemberBalanceUpdateContext(user, balanceUpdate, transactionSave);
        memberBalanceApi.decrUserBalance(context);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean redemptionOrder(MinerRedemptionOrderReqVo reqVO) {

        LambdaQueryWrapper<MinerProductOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MinerProductOrder::getOrderNo,reqVO.getOrderNo());
        MinerProductOrder productOrder = minerProductOrderMapper.selectOne(queryWrapper);
        if (productOrder == null) {
            throw exception(MINER_NOT_EXISTS);
        }
        if (productOrder.getOrderStatus()!=1){
            throw exception(MINER_ORDER_STATUS_ERROR);
        }

        Long userId = getLoginUserId();
        MemberUserRespDTO userDTO = memberUserApi.checkUserExists(userId);
        MemberBalanceUpdateReqVO balanceUpdate = new MemberBalanceUpdateReqVO();
        balanceUpdate.setUserId(userId);
        balanceUpdate.setAmount(productOrder.getOrderAmount());
        //账变
        MemberTransactionSaveReqVO transactionSave = new MemberTransactionSaveReqVO();
        transactionSave.setUserId(userId);
        transactionSave.setTransactionType(MemberTransactionsTypeEnum.MINING_ORDER_PRINCIPAL_REFUND);
        transactionSave.setRemark(MemberTransactionsTypeEnum.MINING_ORDER_PRINCIPAL_REFUND.getLabel());
        transactionSave.setBizOrderNo(productOrder.getOrderNo());
        transactionSave.setAmountCurrency("USDT");
        transactionSave.setTenantId(userDTO.getTenantId());
        MemberBalanceUpdateContext context = new MemberBalanceUpdateContext(userDTO, balanceUpdate, transactionSave);
        //退回本金
        memberBalanceApi.incrUserBalance(context);
        MemberBalanceUpdateReqVO redemptionBalanceUpdate = new MemberBalanceUpdateReqVO();
        balanceUpdate.setUserId(userId);
        BigDecimal redemptionAmount = productOrder.getOrderAmount().multiply(productOrder.getLiquidatedDamagesRatio());
        redemptionBalanceUpdate.setAmount(redemptionAmount);
        //账变
        MemberTransactionSaveReqVO redemptionTransactionSave = new MemberTransactionSaveReqVO();
        redemptionTransactionSave.setUserId(userId);
        redemptionTransactionSave.setTransactionType(MemberTransactionsTypeEnum.MINING_ORDER_REDEMPTION_PENALTY);
        redemptionTransactionSave.setRemark(MemberTransactionsTypeEnum.MINING_ORDER_REDEMPTION_PENALTY.getLabel());
        redemptionTransactionSave.setBizOrderNo(productOrder.getOrderNo());
        redemptionTransactionSave.setAmountCurrency("USDT");
        redemptionTransactionSave.setTenantId(userDTO.getTenantId());
        MemberBalanceUpdateContext redemptionContext = new MemberBalanceUpdateContext(userDTO, redemptionBalanceUpdate, redemptionTransactionSave);
        //扣除违约金
        memberBalanceApi.decrUserBalance(redemptionContext);
        //修改订单状态
        productOrder.setOrderStatus(2);
        productOrder.setLiquidatedDamages(redemptionAmount);
        minerProductOrderMapper.updateById(productOrder);
        return true;
    }

    @Override
    public Boolean createMinerProduct(MinerProductReqVO reqVO) {
        MinerProduct minerProduct = BeanUtils.toBean(reqVO, MinerProduct.class);
        return minerProductMapper.insert(minerProduct) > 0;
    }

    @Override
    public Boolean updateMinerProduct(MinerProductReqVO reqVO) {
        MinerProduct product = minerProductMapper.selectById(reqVO.getId());
        if (product == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        MinerProduct minerProduct = BeanUtils.toBean(reqVO, MinerProduct.class);
        return minerProductMapper.updateById(minerProduct) > 0;
    }

    @Override
    public Boolean deleteMinerProduct(Long id) {
        LambdaQueryWrapper<MinerProductOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MinerProductOrder::getProductId, id);
        queryWrapper.eq(MinerProductOrder::getOrderStatus, 1);
        Long count = minerProductOrderMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw exception(MINER_HAS_ORDER);
        }
        return minerProductMapper.deleteById(id) > 0;
    }

    @Override
    public PageResult<MinerProduct> getMinerPage(MinerProductPageReqVO pageReqVO) {
        return minerProductMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<MinerProductOrder> getMinerOrders(MinerOrderPageReqVo pageReqVO) {
        Long paramUid = getLoginUserId();
        if (paramUid==null) {
            if (pageReqVO.getUserId()!=null){
                paramUid=pageReqVO.getUserId();
            }
        }
        return minerProductOrderMapper.selectPage(pageReqVO,paramUid);
    }
}
