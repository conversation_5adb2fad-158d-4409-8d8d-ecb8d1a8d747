package com.rf.exchange.module.system.controller.app.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户 App - 协议多语言 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Schema(description = "用户 App - 协议多语言 Response VO")
@Data
public class AppAgreementMultiLangRespVO {

    @Schema(description = "协议ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long agreementId;

    @Schema(description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "协议版本号", example = "1.0")
    private String version;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveTime;

    @Schema(description = "当前语言内容")
    private AppAgreementContentRespVO currentContent;

    @Schema(description = "支持的语言列表")
    private List<LanguageOption> availableLanguages;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    /**
     * 协议内容响应VO
     */
    @Schema(description = "协议内容响应VO")
    @Data
    public static class AppAgreementContentRespVO {

        @Schema(description = "语言代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "zh-CN")
        private String languageCode;

        @Schema(description = "语言显示名称", example = "简体中文")
        private String languageDisplayName;

        @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
        private String title;

        @Schema(description = "协议内容（HTML格式）", requiredMode = Schema.RequiredMode.REQUIRED)
        private String content;
    }

    /**
     * 语言选项
     */
    @Schema(description = "语言选项")
    @Data
    public static class LanguageOption {

        @Schema(description = "语言代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "zh-CN")
        private String languageCode;

        @Schema(description = "语言显示名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "简体中文")
        private String languageDisplayName;

        @Schema(description = "是否为当前语言", example = "true")
        private Boolean isCurrent;

        public LanguageOption() {}

        public LanguageOption(String languageCode, String languageDisplayName, Boolean isCurrent) {
            this.languageCode = languageCode;
            this.languageDisplayName = languageDisplayName;
            this.isCurrent = isCurrent;
        }
    }

    // ========== 便利方法 ==========

    /**
     * 设置当前内容的语言显示名称
     */
    public void setCurrentContentLanguageDisplayName() {
        if (currentContent != null && currentContent.getLanguageCode() != null) {
            currentContent.setLanguageDisplayName(getLanguageDisplayName(currentContent.getLanguageCode()));
        }
    }

    /**
     * 设置可用语言的当前状态
     */
    public void setAvailableLanguagesCurrentStatus(String currentLanguageCode) {
        if (availableLanguages != null) {
            availableLanguages.forEach(lang -> 
                lang.setIsCurrent(currentLanguageCode.equals(lang.getLanguageCode()))
            );
        }
    }

    /**
     * 获取语言显示名称
     */
    private String getLanguageDisplayName(String languageCode) {
        return switch (languageCode) {
            case "zh-CN", "zh" -> "简体中文";
            case "zh-TW" -> "繁体中文";
            case "en", "en-US" -> "English";
            case "ja", "ja-JP" -> "日本語";
            case "ko", "ko-KR" -> "한국어";
            case "es" -> "Español";
            case "fr" -> "Français";
            case "de" -> "Deutsch";
            case "ru" -> "Русский";
            case "ar" -> "العربية";
            case "pt" -> "Português";
            case "it" -> "Italiano";
            case "th" -> "ไทย";
            case "vi" -> "Tiếng Việt";
            case "id" -> "Bahasa Indonesia";
            case "ms" -> "Bahasa Melayu";
            case "hi" -> "हिन्दी";
            default -> languageCode;
        };
    }

    /**
     * 是否支持指定语言
     */
    public boolean supportsLanguage(String languageCode) {
        if (availableLanguages == null) {
            return false;
        }
        return availableLanguages.stream()
                .anyMatch(lang -> languageCode.equals(lang.getLanguageCode()));
    }

    /**
     * 获取默认语言代码（优先中文，其次英文）
     */
    public String getDefaultLanguageCode() {
        if (availableLanguages == null || availableLanguages.isEmpty()) {
            return "zh-CN";
        }
        
        // 优先返回中文
        if (supportsLanguage("zh-CN")) {
            return "zh-CN";
        }
        
        // 其次返回英文
        if (supportsLanguage("en")) {
            return "en";
        }
        
        // 最后返回第一个
        return availableLanguages.get(0).getLanguageCode();
    }
}
