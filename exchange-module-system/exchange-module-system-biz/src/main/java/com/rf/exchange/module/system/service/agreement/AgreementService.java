package com.rf.exchange.module.system.service.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementPageReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import jakarta.validation.Valid;

/**
 * 协议管理 Service 接口
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface AgreementService {

    /**
     * 创建协议管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAgreement(@Valid AgreementSaveReqVO createReqVO);

    /**
     * 更新协议管理
     *
     * @param updateReqVO 更新信息
     */
    void updateAgreement(@Valid AgreementSaveReqVO updateReqVO);

    /**
     * 删除协议管理
     *
     * @param id 编号
     */
    void deleteAgreement(Long id);

    /**
     * 获得协议管理
     *
     * @param id 编号
     * @return 协议管理
     */
    AgreementDO getAgreement(Long id);

    /**
     * 获得协议管理分页
     *
     * @param pageReqVO 分页查询
     * @return 协议管理分页
     */
    PageResult<AgreementDO> getAgreementPage(AgreementPageReqVO pageReqVO);

    /**
     * 根据租户ID和协议类型获取协议
     *
     * @param tenantId 租户ID
     * @param type     协议类型
     * @return 协议信息
     */
    AgreementDO getAgreementByTenantIdAndType(Long tenantId, Integer type);

    /**
     * 校验协议是否存在
     *
     * @param id 协议ID
     * @return 协议信息
     */
    AgreementDO validateAgreementExists(Long id);
}
