package com.rf.exchange.module.system.service.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementPageReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementSaveReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementMultiLangSaveReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementContentVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 协议管理 Service 接口
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface AgreementService {

    /**
     * 创建协议管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAgreement(@Valid AgreementSaveReqVO createReqVO);

    /**
     * 更新协议管理
     *
     * @param updateReqVO 更新信息
     */
    void updateAgreement(@Valid AgreementSaveReqVO updateReqVO);

    /**
     * 删除协议管理
     *
     * @param id 编号
     */
    void deleteAgreement(Long id);

    /**
     * 获得协议管理
     *
     * @param id 编号
     * @return 协议管理
     */
    AgreementDO getAgreement(Long id);

    /**
     * 获得协议管理分页
     *
     * @param pageReqVO 分页查询
     * @return 协议管理分页
     */
    PageResult<AgreementDO> getAgreementPage(AgreementPageReqVO pageReqVO);

    /**
     * 根据租户ID和协议类型获取协议
     *
     * @param tenantId 租户ID
     * @param type     协议类型
     * @return 协议信息
     */
    AgreementDO getAgreementByTenantIdAndType(Long tenantId, Integer type);

    /**
     * 校验协议是否存在
     *
     * @param id 协议ID
     * @return 协议信息
     */
    AgreementDO validateAgreementExists(Long id);

    // ==================== 多语言协议管理 ====================

    /**
     * 创建多语言协议
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMultiLangAgreement(@Valid AgreementMultiLangSaveReqVO createReqVO);

    /**
     * 更新多语言协议
     *
     * @param updateReqVO 更新信息
     */
    void updateMultiLangAgreement(@Valid AgreementMultiLangSaveReqVO updateReqVO);

    /**
     * 获取协议的多语言内容
     *
     * @param agreementId 协议ID
     * @return 多语言内容列表
     */
    List<AgreementContentDO> getAgreementContents(Long agreementId);

    /**
     * 根据租户ID、协议类型和语言获取协议内容
     *
     * @param tenantId     租户ID
     * @param type         协议类型
     * @param languageCode 语言代码
     * @return 协议内容
     */
    AgreementContentDO getAgreementContent(Long tenantId, Integer type, String languageCode);

    /**
     * 获取协议支持的语言列表
     *
     * @param agreementId 协议ID
     * @return 语言代码列表
     */
    List<String> getAgreementLanguages(Long agreementId);
}
