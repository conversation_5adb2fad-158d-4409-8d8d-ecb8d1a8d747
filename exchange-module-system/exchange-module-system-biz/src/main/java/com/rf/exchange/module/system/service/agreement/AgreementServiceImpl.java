package com.rf.exchange.module.system.service.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementPageReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementSaveReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementMultiLangSaveReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementMultiLangRespVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementContentRespVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;
import com.rf.exchange.module.system.dal.mysql.agreement.AgreementMapper;
import com.rf.exchange.module.system.dal.mysql.agreement.AgreementContentMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * 协议管理 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Service
@Validated
public class AgreementServiceImpl implements AgreementService {

    @Resource
    private AgreementMapper agreementMapper;

    @Resource
    private AgreementContentMapper agreementContentMapper;

    @Override
    public Long createAgreement(AgreementSaveReqVO createReqVO) {
        // 校验协议类型是否重复
        validateAgreementTypeDuplicate(createReqVO.getTenantId(), createReqVO.getType(), null);
        // 校验协议标题是否重复
        validateAgreementTitleDuplicate(createReqVO.getTenantId(), createReqVO.getTitle(), null);

        // 插入
        AgreementDO agreement = BeanUtils.toBean(createReqVO, AgreementDO.class);
        if (agreement.getStatus() == null) {
            agreement.setStatus(1); // 默认启用
        }
        if (agreement.getVersion() == null) {
            agreement.setVersion("1.0"); // 默认版本
        }
        agreementMapper.insert(agreement);
        // 返回
        return agreement.getId();
    }

    @Override
    public void updateAgreement(AgreementSaveReqVO updateReqVO) {
        // 校验存在
        validateAgreementExists(updateReqVO.getId());
        // 校验协议类型是否重复
        validateAgreementTypeDuplicate(updateReqVO.getTenantId(), updateReqVO.getType(), updateReqVO.getId());
        // 校验协议标题是否重复
        validateAgreementTitleDuplicate(updateReqVO.getTenantId(), updateReqVO.getTitle(), updateReqVO.getId());

        // 更新
        AgreementDO updateObj = BeanUtils.toBean(updateReqVO, AgreementDO.class);
        agreementMapper.updateById(updateObj);
    }

    @Override
    public void deleteAgreement(Long id) {
        // 校验存在
        validateAgreementExists(id);
        // 删除
        agreementMapper.deleteById(id);
    }

    private void validateAgreementTypeDuplicate(Long tenantId, Integer type, Long id) {
        AgreementDO agreement = agreementMapper.selectByTenantIdAndTypeForValidation(tenantId, type);
        if (agreement == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的协议
        if (id == null) {
            throw exception(AGREEMENT_TYPE_DUPLICATE);
        }
        if (!agreement.getId().equals(id)) {
            throw exception(AGREEMENT_TYPE_DUPLICATE);
        }
    }

    private void validateAgreementTitleDuplicate(Long tenantId, String title, Long id) {
        AgreementDO agreement = agreementMapper.selectByTenantIdAndTitle(tenantId, title);
        if (agreement == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的协议
        if (id == null) {
            throw exception(AGREEMENT_TITLE_DUPLICATE);
        }
        if (!agreement.getId().equals(id)) {
            throw exception(AGREEMENT_TITLE_DUPLICATE);
        }
    }

    @Override
    public AgreementDO getAgreement(Long id) {
        return agreementMapper.selectById(id);
    }

    @Override
    public PageResult<AgreementDO> getAgreementPage(AgreementPageReqVO pageReqVO) {
        return agreementMapper.selectPage(pageReqVO);
    }

    @Override
    public AgreementDO getAgreementByTenantIdAndType(Long tenantId, Integer type) {
        return agreementMapper.selectByTenantIdAndType(tenantId, type);
    }

    @Override
    public AgreementDO validateAgreementExists(Long id) {
        AgreementDO agreement = agreementMapper.selectById(id);
        if (agreement == null) {
            throw exception(AGREEMENT_NOT_EXISTS);
        }
        return agreement;
    }

    // ==================== 多语言协议管理实现 ====================

    @Override
    public Long createMultiLangAgreement(AgreementMultiLangSaveReqVO createReqVO) {
        // 校验协议类型是否重复
        validateAgreementTypeDuplicate(createReqVO.getTenantId(), createReqVO.getType(), null);

        // 创建协议主记录
        AgreementDO agreement = BeanUtils.toBean(createReqVO, AgreementDO.class);
        if (agreement.getStatus() == null) {
            agreement.setStatus(1); // 默认启用
        }
        if (agreement.getVersion() == null) {
            agreement.setVersion("1.0"); // 默认版本
        }

        // 设置协议标题为默认语言的标题
        AgreementMultiLangSaveReqVO.AgreementContentSaveReqVO defaultContent = createReqVO.getDefaultContent();
        if (defaultContent != null) {
            agreement.setTitle(defaultContent.getTitle());
        }

        agreementMapper.insert(agreement);

        // 创建多语言内容
        for (AgreementMultiLangSaveReqVO.AgreementContentSaveReqVO contentReqVO : createReqVO.getContents()) {
            AgreementContentDO content = BeanUtils.toBean(contentReqVO, AgreementContentDO.class);
            content.setAgreementId(agreement.getId());
            agreementContentMapper.insert(content);
        }

        return agreement.getId();
    }

    @Override
    public void updateMultiLangAgreement(AgreementMultiLangSaveReqVO updateReqVO) {
        // 校验协议是否存在
        validateAgreementExists(updateReqVO.getAgreementId());
        // 校验协议类型是否重复
        validateAgreementTypeDuplicate(updateReqVO.getTenantId(), updateReqVO.getType(), updateReqVO.getAgreementId());

        // 更新协议主记录
        AgreementDO agreement = BeanUtils.toBean(updateReqVO, AgreementDO.class);
        agreement.setId(updateReqVO.getAgreementId());

        // 设置协议标题为默认语言的标题
        AgreementMultiLangSaveReqVO.AgreementContentSaveReqVO defaultContent = updateReqVO.getDefaultContent();
        if (defaultContent != null) {
            agreement.setTitle(defaultContent.getTitle());
        }

        agreementMapper.updateById(agreement);

        // 更新多语言内容
        for (AgreementMultiLangSaveReqVO.AgreementContentSaveReqVO contentReqVO : updateReqVO.getContents()) {
            AgreementContentDO content = BeanUtils.toBean(contentReqVO, AgreementContentDO.class);
            content.setAgreementId(updateReqVO.getAgreementId());

            if (contentReqVO.getId() != null) {
                // 更新现有内容
                content.setId(contentReqVO.getId());
                agreementContentMapper.updateById(content);
            } else {
                // 新增内容
                agreementContentMapper.insert(content);
            }
        }
    }

    @Override
    public List<AgreementContentDO> getAgreementContents(Long agreementId) {
        return agreementContentMapper.selectByAgreementId(agreementId);
    }

    @Override
    public AgreementContentDO getAgreementContent(Long tenantId, Integer type, String languageCode) {
        // 先查询协议
        AgreementDO agreement = getAgreementByTenantIdAndType(tenantId, type);
        if (agreement == null) {
            return null;
        }

        // 再查询协议内容
        return agreementContentMapper.selectByAgreementIdAndLanguageCode(agreement.getId(), languageCode);
    }

    @Override
    public List<String> getAgreementLanguages(Long agreementId) {
        return agreementContentMapper.selectLanguagesByAgreementId(agreementId);
    }

    @Override
    public Long saveMultiLangAgreement(AgreementMultiLangSaveReqVO saveReqVO) {
        if (saveReqVO.isCreate()) {
            return createMultiLangAgreement(saveReqVO);
        } else {
            updateMultiLangAgreement(saveReqVO);
            return saveReqVO.getAgreementId();
        }
    }

    @Override
    public AgreementMultiLangRespVO getMultiLangAgreement(Long id) {
        // 获取协议主记录
        AgreementDO agreement = validateAgreementExists(id);

        // 获取多语言内容
        List<AgreementContentDO> contents = getAgreementContents(id);

        // 转换为响应VO
        AgreementMultiLangRespVO respVO = BeanUtils.toBean(agreement, AgreementMultiLangRespVO.class);
        respVO.setContents(BeanUtils.toBean(contents, AgreementContentRespVO.class));

        return respVO;
    }

    @Override
    public void deleteAgreementLanguage(Long agreementId, String languageCode) {
        // 校验协议是否存在
        validateAgreementExists(agreementId);

        // 校验是否至少保留一种语言
        List<String> languages = getAgreementLanguages(agreementId);
        if (languages.size() <= 1) {
            throw exception(AGREEMENT_LANGUAGE_CANNOT_DELETE_LAST);
        }

        // 删除指定语言的内容
        agreementContentMapper.deleteByAgreementIdAndLanguage(agreementId, languageCode);
    }

    @Override
    public void copyAgreementLanguage(Long agreementId, String sourceLanguage, String targetLanguage) {
        // 校验协议是否存在
        validateAgreementExists(agreementId);

        // 获取源语言内容
        AgreementContentDO sourceContent = agreementContentMapper.selectByAgreementIdAndLanguage(agreementId, sourceLanguage);
        if (sourceContent == null) {
            throw exception(AGREEMENT_LANGUAGE_NOT_EXISTS);
        }

        // 检查目标语言是否已存在
        AgreementContentDO existingContent = agreementContentMapper.selectByAgreementIdAndLanguage(agreementId, targetLanguage);
        if (existingContent != null) {
            throw exception(AGREEMENT_LANGUAGE_ALREADY_EXISTS);
        }

        // 复制内容
        AgreementContentDO targetContent = BeanUtils.toBean(sourceContent, AgreementContentDO.class);
        targetContent.setId(null);
        targetContent.setLanguageCode(targetLanguage);
        targetContent.setCreateTime(null);
        targetContent.setUpdateTime(null);

        agreementContentMapper.insert(targetContent);
    }
}
