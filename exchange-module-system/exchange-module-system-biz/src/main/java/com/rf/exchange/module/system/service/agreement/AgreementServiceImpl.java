package com.rf.exchange.module.system.service.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementPageReqVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.dal.mysql.agreement.AgreementMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * 协议管理 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Service
@Validated
public class AgreementServiceImpl implements AgreementService {

    @Resource
    private AgreementMapper agreementMapper;

    @Override
    public Long createAgreement(AgreementSaveReqVO createReqVO) {
        // 校验协议类型是否重复
        validateAgreementTypeDuplicate(createReqVO.getTenantId(), createReqVO.getType(), null);
        // 校验协议标题是否重复
        validateAgreementTitleDuplicate(createReqVO.getTenantId(), createReqVO.getTitle(), null);

        // 插入
        AgreementDO agreement = BeanUtils.toBean(createReqVO, AgreementDO.class);
        if (agreement.getStatus() == null) {
            agreement.setStatus(1); // 默认启用
        }
        if (agreement.getVersion() == null) {
            agreement.setVersion("1.0"); // 默认版本
        }
        agreementMapper.insert(agreement);
        // 返回
        return agreement.getId();
    }

    @Override
    public void updateAgreement(AgreementSaveReqVO updateReqVO) {
        // 校验存在
        validateAgreementExists(updateReqVO.getId());
        // 校验协议类型是否重复
        validateAgreementTypeDuplicate(updateReqVO.getTenantId(), updateReqVO.getType(), updateReqVO.getId());
        // 校验协议标题是否重复
        validateAgreementTitleDuplicate(updateReqVO.getTenantId(), updateReqVO.getTitle(), updateReqVO.getId());

        // 更新
        AgreementDO updateObj = BeanUtils.toBean(updateReqVO, AgreementDO.class);
        agreementMapper.updateById(updateObj);
    }

    @Override
    public void deleteAgreement(Long id) {
        // 校验存在
        validateAgreementExists(id);
        // 删除
        agreementMapper.deleteById(id);
    }

    private void validateAgreementTypeDuplicate(Long tenantId, Integer type, Long id) {
        AgreementDO agreement = agreementMapper.selectByTenantIdAndTypeForValidation(tenantId, type);
        if (agreement == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的协议
        if (id == null) {
            throw exception(AGREEMENT_TYPE_DUPLICATE);
        }
        if (!agreement.getId().equals(id)) {
            throw exception(AGREEMENT_TYPE_DUPLICATE);
        }
    }

    private void validateAgreementTitleDuplicate(Long tenantId, String title, Long id) {
        AgreementDO agreement = agreementMapper.selectByTenantIdAndTitle(tenantId, title);
        if (agreement == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的协议
        if (id == null) {
            throw exception(AGREEMENT_TITLE_DUPLICATE);
        }
        if (!agreement.getId().equals(id)) {
            throw exception(AGREEMENT_TITLE_DUPLICATE);
        }
    }

    @Override
    public AgreementDO getAgreement(Long id) {
        return agreementMapper.selectById(id);
    }

    @Override
    public PageResult<AgreementDO> getAgreementPage(AgreementPageReqVO pageReqVO) {
        return agreementMapper.selectPage(pageReqVO);
    }

    @Override
    public AgreementDO getAgreementByTenantIdAndType(Long tenantId, Integer type) {
        return agreementMapper.selectByTenantIdAndType(tenantId, type);
    }

    @Override
    public AgreementDO validateAgreementExists(Long id) {
        AgreementDO agreement = agreementMapper.selectById(id);
        if (agreement == null) {
            throw exception(AGREEMENT_NOT_EXISTS);
        }
        return agreement;
    }
}
