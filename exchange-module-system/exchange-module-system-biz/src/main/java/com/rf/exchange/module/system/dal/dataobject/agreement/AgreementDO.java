package com.rf.exchange.module.system.dal.dataobject.agreement;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 协议管理 DO
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@TableName("system_agreement")
@KeySequence("system_agreement_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgreementDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们
     */
    private Integer type;
    
    /**
     * 协议标题
     */
    private String title;
    
    /**
     * 协议内容（HTML格式）
     */
    private String content;
    
    /**
     * 协议版本号
     */
    private String version;
    
    /**
     * 状态：0-禁用 1-启用
     */
    private Integer status;
    
    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;
    
    /**
     * 备注
     */
    private String remark;
}
