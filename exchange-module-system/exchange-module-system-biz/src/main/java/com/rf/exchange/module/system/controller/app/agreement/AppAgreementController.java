package com.rf.exchange.module.system.controller.app.agreement;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.controller.app.agreement.vo.AppAgreementRespVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.service.agreement.AgreementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

/**
 * 用户 App - 协议管理
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Tag(name = "用户 App - 协议管理")
@RestController
@RequestMapping("/system/agreement")
@Validated
public class AppAgreementController {

    @Resource
    private AgreementService agreementService;

    @GetMapping("/privacy-policy")
    @Operation(summary = "获取隐私协议")
    public CommonResult<AppAgreementRespVO> getPrivacyPolicy() {
        Long tenantId = TenantContextHolder.getTenantId();
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, 1);
        return success(BeanUtils.toBean(agreement, AppAgreementRespVO.class));
    }

    @GetMapping("/user-guidelines")
    @Operation(summary = "获取用户准则")
    public CommonResult<AppAgreementRespVO> getUserGuidelines() {
        Long tenantId = TenantContextHolder.getTenantId();
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, 2);
        return success(BeanUtils.toBean(agreement, AppAgreementRespVO.class));
    }

    @GetMapping("/terms-of-service")
    @Operation(summary = "获取服务条款")
    public CommonResult<AppAgreementRespVO> getTermsOfService() {
        Long tenantId = TenantContextHolder.getTenantId();
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, 3);
        return success(BeanUtils.toBean(agreement, AppAgreementRespVO.class));
    }

    @GetMapping("/disclaimer")
    @Operation(summary = "获取免责声明")
    public CommonResult<AppAgreementRespVO> getDisclaimer() {
        Long tenantId = TenantContextHolder.getTenantId();
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, 4);
        return success(BeanUtils.toBean(agreement, AppAgreementRespVO.class));
    }

    @GetMapping("/get-by-type")
    @Operation(summary = "根据类型获取协议")
    @Parameter(name = "type", description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", required = true, example = "1")
    public CommonResult<AppAgreementRespVO> getAgreementByType(@RequestParam("type") Integer type) {
        Long tenantId = TenantContextHolder.getTenantId();
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, type);
        return success(BeanUtils.toBean(agreement, AppAgreementRespVO.class));
    }
}
