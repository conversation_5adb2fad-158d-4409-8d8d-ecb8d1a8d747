package com.rf.exchange.module.system.controller.app.agreement;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.controller.app.agreement.vo.AppAgreementRespVO;
import com.rf.exchange.module.system.controller.app.agreement.vo.AgreementTypeRespVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementContentRespVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;
import com.rf.exchange.module.system.enums.agreement.AgreementTypeEnum;
import com.rf.exchange.module.system.service.agreement.AgreementService;
import com.rf.exchange.framework.i18n.core.I18nHelper;
import com.rf.exchange.framework.i18n.I;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.AGREEMENT_NOT_EXISTS;

/**
 * 用户 App - 协议管理
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Tag(name = "用户 App - 协议管理")
@RestController
@RequestMapping("/system/agreement")
@Validated
public class AppAgreementController {

    @Resource
    private AgreementService agreementService;

    @GetMapping("/privacy-policy")
    @Operation(summary = "获取隐私协议")
    public CommonResult<AppAgreementRespVO> getPrivacyPolicy() {
        return getAgreementByTypeWithLanguage(1);
    }

    @GetMapping("/user-guidelines")
    @Operation(summary = "获取用户准则")
    public CommonResult<AppAgreementRespVO> getUserGuidelines() {
        return getAgreementByTypeWithLanguage(2);
    }

    @GetMapping("/terms-of-service")
    @Operation(summary = "获取服务条款")
    public CommonResult<AppAgreementRespVO> getTermsOfService() {
        return getAgreementByTypeWithLanguage(3);
    }

    @GetMapping("/disclaimer")
    @Operation(summary = "获取免责声明")
    public CommonResult<AppAgreementRespVO> getDisclaimer() {
        return getAgreementByTypeWithLanguage(4);
    }

    @GetMapping("/get-by-type")
    @Operation(summary = "根据类型获取协议")
    @Parameter(name = "type", description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", required = true, example = "1")
    public CommonResult<AppAgreementRespVO> getAgreementByType(@RequestParam("type") Integer type) {
        return getAgreementByTypeWithLanguage(type);
    }

    @GetMapping("/types")
    @Operation(summary = "获取协议类型列表")
    public CommonResult<List<AgreementTypeRespVO>> getAgreementTypes() {
        List<AgreementTypeRespVO> types = new ArrayList<>();
        for (AgreementTypeEnum typeEnum : AgreementTypeEnum.values()) {
            AgreementTypeRespVO typeVO = new AgreementTypeRespVO();
            typeVO.setType(typeEnum.getType());
            typeVO.setName(typeEnum.getName());
            types.add(typeVO);
        }
        // 应用国际化
        I18nHelper.getContent(types, AgreementTypeRespVO.class);
        return success(types);
    }

    @GetMapping("/get-content")
    @Operation(summary = "获取协议内容")
    @Parameter(name = "type", description = "协议类型", required = true, example = "1")
    @Parameter(name = "language", description = "语言代码", example = "zh-CN")
    public CommonResult<AgreementContentRespVO> getAgreementContent(@RequestParam("type") Integer type,
                                                                   @RequestParam(value = "language", required = false) String language) {
        // 获取当前租户ID
        Long tenantId = TenantContextHolder.getTenantId();

        // 如果没有指定语言，使用当前请求的语言
        if (StrUtil.isBlank(language)) {
            language = I.getLang(); // 获取当前请求的语言
        }

        // 获取协议内容，支持智能语言回退
        AgreementContentDO content = getAgreementContentWithFallback(tenantId, type, language);

        if (content == null) {
            throw exception(AGREEMENT_NOT_EXISTS);
        }

        return success(BeanUtils.toBean(content, AgreementContentRespVO.class));
    }

    @GetMapping("/languages")
    @Operation(summary = "获取协议支持的语言列表")
    @Parameter(name = "type", description = "协议类型", required = true, example = "1")
    public CommonResult<List<String>> getAgreementLanguages(@RequestParam("type") Integer type) {
        Long tenantId = TenantContextHolder.getTenantId();
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, type);

        if (agreement == null) {
            return success(List.of());
        }

        List<String> languages = agreementService.getAgreementLanguages(agreement.getId());
        return success(languages);
    }

    /**
     * 根据类型和语言获取协议的私有方法
     */
    private CommonResult<AppAgreementRespVO> getAgreementByTypeWithLanguage(Integer type) {
        Long tenantId = TenantContextHolder.getTenantId();
        String languageCode = I.getLang(); // 获取当前请求的语言

        // 获取多语言协议内容，支持智能回退
        AgreementContentDO content = getAgreementContentWithFallback(tenantId, type, languageCode);

        if (content == null) {
            // 如果还是没有，返回空结果
            return success(null);
        }

        // 转换为响应VO
        AppAgreementRespVO respVO = new AppAgreementRespVO();
        respVO.setId(content.getAgreementId());
        respVO.setType(type);
        respVO.setTitle(content.getTitle());
        respVO.setContent(content.getContent());

        return success(respVO);
    }

    /**
     * 获取协议内容，支持智能语言回退
     * 回退顺序：指定语言 -> 中文 -> 英文 -> 任意可用语言
     */
    private AgreementContentDO getAgreementContentWithFallback(Long tenantId, Integer type, String language) {
        // 1. 尝试获取指定语言
        AgreementContentDO content = agreementService.getAgreementContent(tenantId, type, language);
        if (content != null) {
            return content;
        }

        // 2. 如果指定语言不是中文，尝试获取中文
        if (!"zh-CN".equals(language)) {
            content = agreementService.getAgreementContent(tenantId, type, "zh-CN");
            if (content != null) {
                return content;
            }
        }

        // 3. 如果中文不存在，尝试获取英文
        if (!"en".equals(language)) {
            content = agreementService.getAgreementContent(tenantId, type, "en");
            if (content != null) {
                return content;
            }
        }

        // 4. 如果都不存在，获取该协议的任意可用语言
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, type);
        if (agreement != null) {
            List<AgreementContentDO> contents = agreementService.getAgreementContents(agreement.getId());
            if (!contents.isEmpty()) {
                return contents.get(0); // 返回第一个可用的语言版本
            }
        }

        return null;
    }
}
